# Knowledge Base Chatbot API

A powerful, AI-driven chatbot platform that combines structured Q&A flows with knowledge base search capabilities to provide intelligent customer support.

## Technology Stack

- **Backend**: FastAPI (Python 3.11+)
- **Database**: PostgreSQL for structured data storage
- **Search Engine**: Elasticsearch for vector search and semantic retrieval
- **AI Models**: OpenAI's o4-mini for chat completions and text-embedding-3-small for embeddings
- **Caching**: Redis for conversation state management
- **Containerization**: Docker and Docker Compose for easy deployment
- **Monitoring**: Prometheus for metrics collection and monitoring
- **Logging**: JSON-formatted logs with standardized structure

## Features

### Knowledge Base Management

- **Document Upload**: Upload PDF documents to build your knowledge base
- **Vector Search**: Documents are automatically chunked and converted to vector embeddings for semantic search
- **Multi-tenant Support**: Organize knowledge bases by tenant for complete data isolation

### Chatbot Creation and Management

- **Custom Chatbots**: Create chatbots with custom names, descriptions, and Q&A flows
- **Structured Q&A**: Define specific questions to collect information from users
- **Knowledge Base Association**: Link chatbots to specific knowledge bases for targeted responses
- **Entity-Based Triggers**: Configure chatbots with triggers (NEW_ENTITY/EXISTING_ENTITY) for specific business flows
- **Connected Account Integration**: Link chatbots to external accounts with entity types and account IDs

### Conversation Flow

- **Guided Conversations**: Lead users through a structured Q&A flow to collect necessary information
- **Enhanced Welcome Messages**: AI-powered welcome messages that provide a human touch to user interactions
- **Off-topic Detection**: Politely redirect users when they ask unrelated questions during the Q&A flow
- **Information Verification**: Verify collected information with users before proceeding to knowledge search
- **Semantic Search**: Use AI embeddings to find the most relevant information in the knowledge base
- **Conversation Persistence**: Store complete conversation histories in the database for auditing and analytics
- **Conversation Ending Detection**: Detect when users want to end the conversation and provide a polite farewell
- **Smart Conversation Termination**: Automatically end conversations with thank you messages when no knowledgebase is available

### AI-Powered Features

- **Natural Language Understanding**: Understand user intent and provide contextually relevant responses
- **Smart Summarization**: Generate concise summaries of collected information
- **Contextual Responses**: Generate responses that acknowledge previous messages for a more natural conversation
- **Fallback Mechanisms**: Gracefully handle situations where no relevant information is found

### Billing and Credit Management

- **Credit-Based Billing**: Automatic credit tracking for question-answer interactions
- **Tiered Pricing**: 2 credits for Q&A with knowledgebase, 1 credit for Q&A without knowledgebase
- **Usage Analytics**: Detailed credit usage reports by chatbot with date filtering
- **Real-time Tracking**: Track credits consumed for each conversation turn
- **Usage Summary**: Comprehensive summaries including total credits, question counts, and averages

### Monitoring and Observability

- **Prometheus Metrics**: Expose key metrics for monitoring application performance
- **Request Tracing**: Each request has a unique ID for tracing through logs
- **Structured Logging**: JSON-formatted logs with consistent structure for easy parsing
- **Performance Metrics**: Track latency, request counts, and error rates

## Authentication

The API uses JWT (JSON Web Token) authentication. All API endpoints require a valid JWT token to be included in the Authorization header:

```
Authorization: Bearer <JWT Token>
```

The JWT token payload should have the following format:

```json
{
  "iss": "sell",
  "data": {
    "accessToken": "d82b61d4-e071-4e7e-ab6d-5d10f6df7fbe:7:7",
    "expiresIn": 43199,
    "expiry": 1749837217490,
    "tokenType": "bearer",
    "refreshToken": "3e56fc0e-3c53-41b3-9ace-efeb95e652f7:7:7",
    "permissions": [
      {
        "id": 35,
        "name": "email",
        "description": "has access to Emails",
        "limits": 5,
        "units": "count",
        "action": {
          "read": true,
          "write": false,
          "update": false,
          "delete": true,
          "email": false,
          "call": false,
          "sms": false,
          "task": false,
          "note": false,
          "meeting": false,
          "document": false,
          "readAll": true,
          "updateAll": false,
          "deleteAll": true,
          "quotation": false,
          "reshare": false,
          "reassign": false
        }
      }
    ],
    "userId": "7",
    "username": "<EMAIL>",
    "tenantId": "7",
    "source": {
      "type": "Web",
      "id": "7",
      "name": "User"
    },
    "meta": {
      "rate-limit": 5,
      "pid": 2
    }
  }
}
```

The system automatically extracts the `tenantId` and `userId` from the token, so you don't need to include these as parameters in your API requests.

### Example API Request with Authentication

```bash
curl -X POST "http://localhost:8000/v1/chatbot/knowledgebase" \
  -H "Authorization: Bearer {your-jwt-token}" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/document.pdf"
```

### Permission-Based Access Control

The API uses the permissions defined in the JWT token to control access to various endpoints. Each endpoint requires specific permissions to be present in the token.

## Getting Started

### Prerequisites

- Docker and Docker Compose
- OpenAI API key

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/knowledge-base-chatbot.git
   cd knowledge-base-chatbot
   ```

2. Create a `.env` file with your configuration:
   ```
   ELASTICSEARCH_URL=http://elasticsearch-vector-master:9200
   POSTGRES_USER=sdwhatsapp
   POSTGRES_PASSWORD=sdwhatsapp
   POSTGRES_DB=sdwhatsapp
   POSTGRES_HOST=postgres
   OPENAI_API_KEY=your_openai_api_key
   REDIS_URL=redis://redis:6379/0
   LOG_LEVEL=INFO
   ```

3. Start the services:
   ```bash
   docker-compose up -d
   ```

4. The API will be available at `http://localhost:8000`

## Monitoring

### Prometheus Metrics

The application exposes Prometheus metrics at the `/metrics` endpoint. This endpoint is publicly accessible without authentication:

```
http://localhost:8000/metrics
```

Available metrics include:

- `app_request_count`: Counter for HTTP requests by method, endpoint, and status code
- `app_request_latency_seconds`: Histogram for request latency
- `app_active_connections`: Gauge for active connections
- `app_auth_failure_count`: Counter for authentication failures
- `app_elasticsearch_query_time_seconds`: Summary for Elasticsearch query time
- `app_chatbot_conversation_count`: Counter for chatbot conversations
- `app_openai_api_call_count`: Counter for OpenAI API calls

### Logging

The application uses structured JSON logging with the following format:

```json
{
  "timestamp": "2023-06-15 14:32:45.123",
  "level": "INFO",
  "thread": "MainThread(140736282896128)",
  "logger": "app.main",
  "message": "Request started",
  "path": "/app/main.py",
  "line": 60,
  "request_id": "550e8400-e29b-41d4-a716-446655440000",
  "method": "GET",
  "path": "/v1/chatbot/conversations",
  "client_ip": "***********",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
}
```

Each log entry includes:
- Timestamp in `yyyy-MM-dd HH:mm:ss.SSS` format
- Log level (INFO, WARNING, ERROR, etc.)
- Thread information
- Logger name
- Message
- File path and line number
- Request ID for tracing
- Additional context specific to the log entry

## API Usage

### 1. Upload Documents to Knowledge Base

```bash
curl -X POST "http://localhost:8000/v1/chatbot/knowledgebase" \
  -H "Authorization: Bearer {your-jwt-token}" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/document.pdf"
```

### 2. Create a Chatbot

```bash
curl -X POST "http://localhost:8000/v1/chatbot/" \
  -H "Authorization: Bearer {your-jwt-token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Customer Support Bot",
    "description": "Helps customers with product inquiries",
    "questions": [
      {"question": "What is your name?"},
      {"question": "What product are you interested in?"},
      {"question": "What specific feature would you like to know about?"}
    ],
    "knowledgebase_ids": ["kb_id_1", "kb_id_2"]
  }'
```

### 3. Start a Conversation

```bash
curl -X POST "http://localhost:8000/v1/chatbot/conversations" \
  -H "Authorization: Bearer {your-jwt-token}" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello, I need help"
  }'
```

### 4. Continue a Conversation

```bash
curl -X POST "http://localhost:8000/v1/chatbot/conversations/{conversation_id}" \
  -H "Authorization: Bearer {your-jwt-token}" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "My name is John"
  }'
```

### 5. Verify Information

```bash
curl -X POST "http://localhost:8000/v1/chatbot/conversations/{conversation_id}/verify" \
  -H "Authorization: Bearer {your-jwt-token}" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Yes, that information is correct"
  }'
```

## Conversation Flow

1. **Initial Contact**: User starts a conversation with the chatbot
2. **Structured Q&A**: Chatbot guides the user through predefined questions
   - If user asks off-topic questions, they are politely redirected
3. **Information Verification**: Chatbot summarizes collected information and asks for confirmation
   - If user confirms, proceed to knowledge search phase
   - If user indicates corrections needed, chatbot asks which information to correct
4. **Knowledge Search**: User can now ask questions that are answered using the knowledge base
   - Each response ends with "Is there anything else I can help with?"
5. **Conversation Ending**: When user indicates they're done, chatbot provides a farewell message

## Development

### Project Structure

```
knowledge-base-chatbot/
├── app/
│   ├── routers/
│   │   ├── chatbot.py      # Chatbot API endpoints
│   │   ├── items.py        # Example items API
│   │   └── users.py        # User management API
│   ├── services/
│   │   ├── elasticsearch_service.py  # Elasticsearch integration
│   │   └── redis_service.py          # Redis integration
│   ├── database.py         # Database connection and session management
│   ├── dependencies.py     # FastAPI dependencies
│   ├── models.py           # SQLAlchemy models
│   ├── monitoring.py       # Prometheus metrics
│   ├── logging_config.py   # Logging configuration
│   └── main.py             # FastAPI application entry point
├── docker-compose.yml      # Docker Compose configuration
├── Dockerfile              # Docker build configuration
├── requirements.txt        # Python dependencies
└── README.md               # Project documentation
```

### Adding New Features

1. Define new SQLAlchemy models in `app/models.py`
2. Create new API endpoints in the appropriate router file
3. Implement business logic in service classes
4. Update the documentation to reflect new features

## System Flow

### Knowledge Base Creation and Query Flow

```mermaid
flowchart TD
    A[User] -->|Upload PDF Document| B[API Endpoint]
    B -->|Extract Text| C[PDF Processing]
    C -->|Generate Embeddings| D[OpenAI Embedding API]
    D -->|Store Vectors| E[Elasticsearch]
    D -->|Store Metadata| F[PostgreSQL]
    
    A -->|Ask Question| G[Chatbot API]
    G -->|Extract Context| H[Conversation Management]
    H -->|Generate Embeddings| I[OpenAI Embedding API]
    I -->|Vector Search| E
    E -->|Retrieve Relevant Chunks| J[Context Assembly]
    J -->|Generate Response| K[OpenAI Chat Completion API]
    K -->|Return Answer| A
    
    H -->|Store Conversation| F
    H -->|Cache State| L[Redis]
```

### Conversation Flow

```mermaid
sequenceDiagram
    participant User
    participant API as API Gateway
    participant Auth as Authentication
    participant Bot as Chatbot Engine
    participant KB as Knowledge Base
    participant DB as Database
    participant AI as OpenAI API

    User->>API: Start conversation
    API->>Auth: Validate JWT token
    Auth->>API: Extract tenant_id & user_id
    API->>Bot: Initialize conversation
    Bot->>DB: Create conversation record
    DB->>Bot: Return conversation_id
    Bot->>User: Welcome message & first question

    loop Structured Q&A
        User->>API: Send message
        API->>Auth: Validate JWT token
        Auth->>API: Authorize request
        API->>Bot: Process message
        Bot->>DB: Update conversation state
        
        alt Off-topic question
            Bot->>User: Redirect to current question
        else Valid answer
            Bot->>DB: Store answer
            Bot->>User: Next question or summary
        end
    end

    Bot->>User: Verify collected information
    User->>API: Confirm information
    API->>Bot: Process confirmation
    
    alt Information correct
        Bot->>User: Enter knowledge search phase
    else Information needs correction
        Bot->>User: Ask which information to correct
        User->>API: Specify correction
        API->>Bot: Process correction
        Bot->>DB: Update information
        Bot->>User: Verify again
    end

    loop Knowledge Search Phase
        User->>API: Ask question
        API->>Bot: Process question
        Bot->>KB: Search for relevant information
        KB->>Bot: Return context
        Bot->>AI: Generate response with context
        AI->>Bot: Return response
        Bot->>User: Answer + "Anything else I can help with?"
    end

    User->>API: End conversation
    API->>Bot: Process end request
    Bot->>DB: Mark conversation as completed
    Bot->>User: Farewell message
```

### Document Processing Flow

```mermaid
flowchart TD
    A[User] -->|Upload PDF| B[API Endpoint]
    B -->|Validate File Type| C{Is PDF?}
    C -->|No| D[Return Error]
    C -->|Yes| E[Extract Text]
    E -->|Process Text| F[Chunk Text]
    F -->|For Each Chunk| G[Generate Embedding]
    G -->|Store| H[Elasticsearch]
    B -->|Store Metadata| I[PostgreSQL]
    I -->|Return| J[Success Response]
    J -->|Document ID| A
```

### Authentication and Authorization Flow

```mermaid
flowchart TD
    A[Client] -->|Request with JWT| B[API Gateway]
    B -->|Validate Token| C{Valid Token?}
    C -->|No| D[Return 401 Unauthorized]
    C -->|Yes| E[Extract Claims]
    E -->|Get tenant_id & user_id| F[Create Auth Context]
    F -->|Check Permissions| G{Has Permission?}
    G -->|No| H[Return 403 Forbidden]
    G -->|Yes| I[Process Request]
    I -->|Return Response| A
```

### Monitoring and Logging Flow

```mermaid
flowchart TD
    A[Request] -->|Middleware| B[Request ID Generation]
    B -->|Log Request| C[JSON Logger]
    B -->|Process Request| D[API Endpoint]
    D -->|Increment Counter| E[Prometheus Metrics]
    D -->|Measure Latency| E
    D -->|Process| F[Business Logic]
    F -->|External API Call| G[OpenAI API]
    G -->|Log API Call| C
    G -->|Increment Counter| E
    F -->|Database Query| H[PostgreSQL]
    H -->|Log Query| C
    F -->|Search Query| I[Elasticsearch]
    I -->|Measure Query Time| E
    I -->|Log Search| C
    F -->|Generate Response| J[Response]
    J -->|Log Response| C
    J -->|Return| K[Client]
```

These flowcharts illustrate the key processes in the Knowledge Base Chatbot system, including document processing, conversation flow, authentication, and monitoring. The diagrams help visualize how different components interact and the sequence of operations for various user actions.

## Document Storage

The system stores uploaded PDF documents in an AWS S3 bucket with the following folder structure:

```
{bucket_name}/
├── {tenant_id}_knowledgebase/
│   ├── {chatbot_id}/
│   │   ├── files/
│   │   │   ├── {filename}.pdf
│   │   │   └── ...
│   └── ...
└── ...
```

### S3 Configuration

To configure S3 storage, set the following environment variables:

```bash
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=your-knowledge-base-bucket
```

### Chatbot API Endpoints

#### Chatbot Management
- `GET /v1/chatbot/` - List all chatbots for the tenant
- `POST /v1/chatbot/` - Create a new chatbot with trigger and connected account support
- `GET /v1/chatbot/{chatbot_id}` - Get chatbot details by ID
- `PUT /v1/chatbot/{chatbot_id}` - Update chatbot configuration
- `DELETE /v1/chatbot/{chatbot_id}` - Delete a chatbot

#### Conversation Management
- `POST /v1/chatbot/conversations` - Start a conversation using entityType, connectedAccountId, and trigger
- `POST /v1/chatbot/conversations/{conversation_id}` - Continue an existing conversation

#### Knowledge Base Management
- `POST /v1/chatbot/{chatbot_id}/knowledgebase` - Upload documents to chatbot's knowledge base

#### Billing and Credit Management
- `GET /v1/chatbot/credit-usage` - Get detailed credit usage records with date filtering across all chatbots
- `GET /v1/chatbot/credit-summary` - Get credit usage summary and statistics across all chatbots

#### Question Management
- `POST /v1/chatbot/{chatbot_id}/questions` - Configure questions for a chatbot
- `DELETE /v1/chatbot/{chatbot_id}/questions/{question_id}` - Delete a specific question

## License

This project is licensed under the MIT License - see the LICENSE file for details.

### Complete System Sequence Diagram

The following sequence diagram illustrates the complete flow of a user interaction with the Knowledge Base Chatbot system, from authentication to conversation completion:

```mermaid
sequenceDiagram
    participant User
    participant API as API Gateway
    participant Auth as Auth Service
    participant ChatSvc as Chatbot Service
    participant KBSvc as Knowledge Base Service
    participant ES as Elasticsearch
    participant DB as PostgreSQL
    participant Redis
    participant OpenAI as OpenAI API
    participant Metrics as Prometheus Metrics

    Note over User,Metrics: Document Upload Phase
    User->>API: Upload PDF document
    API->>Auth: Validate JWT token
    Auth->>API: Return auth context (tenant_id, user_id)
    API->>Metrics: Increment request counter
    API->>KBSvc: Process document
    KBSvc->>KBSvc: Extract text from PDF
    KBSvc->>OpenAI: Generate embeddings for chunks
    OpenAI->>KBSvc: Return embeddings
    KBSvc->>ES: Store document chunks with embeddings
    KBSvc->>DB: Store document metadata
    DB->>KBSvc: Confirm storage
    KBSvc->>API: Return success
    API->>User: Document uploaded successfully
    API->>Metrics: Record latency

    Note over User,Metrics: Conversation Initialization
    User->>API: Start conversation
    API->>Auth: Validate JWT token
    Auth->>API: Return auth context
    API->>ChatSvc: Initialize conversation
    ChatSvc->>DB: Create conversation record
    DB->>ChatSvc: Return conversation_id
    ChatSvc->>Redis: Store conversation state
    ChatSvc->>API: Return welcome message
    API->>User: Display welcome message & first question
    
    Note over User,Metrics: Structured Q&A Phase
    loop Structured Q&A Flow
        User->>API: Send answer to question
        API->>Auth: Validate JWT token
        Auth->>API: Return auth context
        API->>ChatSvc: Process message
        ChatSvc->>Redis: Get conversation state
        ChatSvc->>ChatSvc: Validate answer
        
        alt Invalid or Off-topic Answer
            ChatSvc->>API: Request to stay on topic
            API->>User: Please answer the current question
        else Valid Answer
            ChatSvc->>DB: Store answer
            ChatSvc->>Redis: Update conversation state
            
            alt More Questions Remaining
                ChatSvc->>API: Next question
                API->>User: Display next question
            else All Questions Answered
                ChatSvc->>API: Summary of collected info
                API->>User: Display summary & ask for confirmation
            end
        end
    end
    
    Note over User,Metrics: Information Verification
    User->>API: Confirm information
    API->>ChatSvc: Process confirmation
    
    alt Information Correct
        ChatSvc->>Redis: Update conversation state
        ChatSvc->>API: Enter knowledge search phase
        API->>User: What would you like to know?
    else Information Needs Correction
        ChatSvc->>API: Ask which information to correct
        API->>User: Which information needs correction?
        User->>API: Specify correction
        API->>ChatSvc: Process correction
        ChatSvc->>Redis: Update conversation state
        ChatSvc->>DB: Update information
        ChatSvc->>API: Verify again
        API->>User: Is this information correct now?
    end
    
    Note over User,Metrics: Knowledge Search Phase
    loop Knowledge Search
        User->>API: Ask question
        API->>Auth: Validate JWT token
        Auth->>API: Return auth context
        API->>Metrics: Increment query counter
        API->>ChatSvc: Process question
        ChatSvc->>Redis: Get conversation context
        ChatSvc->>OpenAI: Generate embedding for question
        OpenAI->>ChatSvc: Return embedding
        ChatSvc->>KBSvc: Search for relevant information
        KBSvc->>ES: Vector search query
        ES->>KBSvc: Return relevant document chunks
        KBSvc->>ChatSvc: Return context
        ChatSvc->>OpenAI: Generate response with context
        OpenAI->>ChatSvc: Return response
        ChatSvc->>DB: Log interaction
        ChatSvc->>Redis: Update conversation state
        ChatSvc->>API: Return answer
        API->>User: Display answer
        API->>Metrics: Record latency
    end
    
    Note over User,Metrics: Conversation Ending
    User->>API: End conversation or detected end
    API->>ChatSvc: Process end request
    ChatSvc->>DB: Mark conversation as completed
    ChatSvc->>Redis: Clear conversation state
    ChatSvc->>API: Farewell message
    API->>User: Display farewell message
    API->>Metrics: Record conversation metrics
```

This sequence diagram provides a detailed visualization of the entire user journey through the Knowledge Base Chatbot system, from document upload to conversation completion. It illustrates the interactions between all system components and the flow of data throughout the process.

## Recent Updates and Changes

### Version 2.0 - Enhanced Chatbot Platform

#### Major Features Added

1. **Entity-Based Chatbot Triggers**
   - Added `trigger` parameter to chatbot creation with values `NEW_ENTITY` or `EXISTING_ENTITY`
   - Enhanced chatbot-entity relationship management
   - Improved conversation routing based on entity types and triggers

2. **Streamlined API Architecture**
   - Removed standalone document endpoints (`/documents/*`)
   - All document operations now handled through chatbot-specific endpoints
   - Simplified API surface and improved consistency

3. **Enhanced Conversation API**
   - Modified `POST /chatbot/conversations` to use `entityType`, `connectedAccountId`, and `trigger` parameters
   - Removed direct `chatbot_id` parameter for better entity-based routing
   - Improved conversation initialization flow

4. **AI-Enhanced Welcome Messages**
   - Welcome messages now enhanced with OpenAI for human-like interactions
   - Contextual and warm greeting generation
   - Maintains professionalism while adding personality

5. **Smart Conversation Management**
   - Automatic conversation termination when no knowledgebase is available
   - Enhanced thank you messages using chatbot-specific configurations
   - Improved user experience for different conversation scenarios

6. **Credit-Based Billing System**
   - Implemented comprehensive credit tracking for all Q&A interactions
   - Tiered pricing: 2 credits with knowledgebase, 1 credit without
   - Detailed usage analytics and reporting endpoints
   - Real-time credit consumption tracking

7. **Simplified Admin Interface**
   - Removed RabbitMQ admin endpoints for cleaner API
   - Focused on core chatbot functionality
   - Reduced complexity for end users

#### Database Changes

- Added `trigger` column to `chatbots` table
- Created `chatbot_credit_usage` table for billing tracking
- Enhanced indexing for better performance
- Migration scripts provided for seamless upgrades

#### Testing and Quality Assurance

- Comprehensive test suite for all new features
- Migration verification scripts
- Backward compatibility considerations
- Performance optimization testing

### Migration Guide

To upgrade to version 2.0, run the migration script:

```bash
python migrations/add_trigger_and_credit_usage.py
```

To verify the migration:

```bash
python migrations/add_trigger_and_credit_usage.py --verify
```

To rollback if needed:

```bash
python migrations/add_trigger_and_credit_usage.py --rollback
```

### Testing

Run the comprehensive test suite:

```bash
python test_comprehensive_chatbot_changes.py
```

This ensures all new features are working correctly and existing functionality remains intact.
