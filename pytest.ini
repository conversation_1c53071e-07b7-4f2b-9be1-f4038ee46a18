[tool:pytest]
# Pytest configuration file

# Test discovery patterns
python_files = test_*.py *_test.py verify_*.py
python_classes = Test*
python_functions = test_*

# Test directories
testpaths = test

# Output options
addopts =
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --asyncio-mode=auto

# Markers for categorizing tests
markers =
    unit: Unit tests that don't require external dependencies
    integration: Integration tests that require external services
    slow: Tests that take a long time to run
    api: API endpoint tests
    database: Tests that require database connection
    elasticsearch: Tests that require Elasticsearch
    rabbitmq: Tests that require RabbitMQ
    migration: Database migration tests
    validation: Input validation tests
    service: Service layer tests
    crud: CRUD operation tests
    auth: Authentication and authorization tests

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Ignore certain warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::UserWarning:urllib3.*
