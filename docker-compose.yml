version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: sd-whatsapp-postgres
    environment:
      POSTGRES_USER: sdwhatsapp
      POSTGRES_PASSWORD: sdwhatsapp
      POSTGRES_DB: sdwhatsapp
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sdwhatsapp"]
      interval: 10s
      timeout: 5s
      retries: 5

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.12.0
    container_name: sd-whatsapp-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200"]
      interval: 30s
      timeout: 10s
      retries: 5

  kibana:
    image: docker.elastic.co/kibana/kibana:8.12.0
    container_name: sd-whatsapp-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      elasticsearch:
        condition: service_healthy

  redis:
    image: redis:7-alpine
    container_name: sd-whatsapp-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
  s3mock:
    image: adobe/s3mock:latest
    environment:
      - initialBuckets=bucket1
      - validKmsKeys=region:acct-id
    ports:
      - 9090:9090

  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: sd-whatsapp-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  alembic:
    build:
      context: .
      dockerfile: alembic-Dockerfile
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-sdwhatsapp}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-sdwhatsapp}
      - POSTGRES_DB=${POSTGRES_DB:-sdwhatsapp}
      - POSTGRES_HOST=postgres
    command: [ "upgrade", "head" ]
    volumes:
      - ./.env:/code/.env

volumes:
  postgres_data:
  elasticsearch_data:
  redis_data:
  rabbitmq_data: