"""
Test cases for rule-based chatbot rules endpoint and dependent methods.

This module tests:
1. POST /{chatbot_id}/rules endpoint
2. RuleBasedChatbotService.create_flow() method
3. RuleBasedChatbotService.validate_flow() method
4. RuleBasedChatbotService._create_nodes() method
5. RuleBasedChatbotService._create_edges() method
6. RuleBasedChatbotService._delete_existing_flow() method
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from fastapi import HTTPException, FastAPI

from app.main import app
from app.models import (
    Chatbot, ChatbotNode, ChatbotNodeEntityField, ChatbotEdge,
    RuleBasedChatbotFlow, ChatbotNodeCreate, Chatbot<PERSON>dgeCreate,
    NodePosition, NodeData, NodeOption, NodeButton, NodeSection
)
from app.services.rule_based_chatbot_service import RuleBasedChatbotService
from app.dependencies import AuthContext
from app.exceptions import (
    ChatbotNotFoundException,
    RuleBasedFlowValidationError,
    RuleBasedFlowCreationError,
    RuleBasedEdgeValidationError,
    ActiveChatbotExistsError
)


class TestChatbotRulesEndpoint:
    """Test cases for the POST /{chatbot_id}/rules endpoint"""
    
    def setup_method(self):
        """Setup test data"""
        self.tenant_id = 12345
        self.user_id = "user-123"
        self.chatbot_id = "chatbot-456"
        
        # Mock auth context
        self.auth_context = AuthContext(
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            source_type="test",
            source_id="test-source-id",
            source_name="test-source",
            permissions=[]
        )
        
        # Create test app without authentication middleware
        self.test_app = FastAPI()
        
        # Register error handlers for proper exception handling
        from app.error_handlers import register_error_handlers
        register_error_handlers(self.test_app)
        
        # Add middleware to set auth context in request.state
        @self.test_app.middleware("http")
        async def test_auth_middleware(request, call_next):
            # Set auth context in request.state (what the router expects)
            request.state.auth_context = self.auth_context
            response = await call_next(request)
            return response
        
        # Override authentication dependency
        def mock_auth_context():
            return self.auth_context
        
        from app.dependencies import get_auth_context
        from app.database import get_db
        from app.routers import chatbot as chatbot_router
        
        # Mock database dependency
        self.mock_db = Mock(spec=Session)
        def mock_get_db():
            return self.mock_db
        
        # Add the chatbot router to test app (it already has /v1/chatbot prefix)
        self.test_app.include_router(chatbot_router.router)
        
        # Override dependencies
        self.test_app.dependency_overrides[get_auth_context] = mock_auth_context
        self.test_app.dependency_overrides[get_db] = mock_get_db
        
        self.client = TestClient(self.test_app)
        
        # Sample flow data
        self.valid_flow_data = {
            "nodes": [
                {
                    "id": "node-1",
                    "name": "start",
                    "type": "question",
                    "position": {"x": 100, "y": 100},
                    "data": {
                        "text": "What is your name?",
                        "options": [
                            {"text": "Continue", "name": "continue", "position": 1}
                        ]
                    },
                    "variableMapping": []
                },
                {
                    "id": "node-2", 
                    "name": "end",
                    "type": "sendMessage",
                    "position": {"x": 300, "y": 100},
                    "data": {
                        "text": "Thank you for your response!"
                    },
                    "variableMapping": []
                }
            ],
            "edges": [
                {
                    "id": "edge-1",
                    "source": "start",
                    "sourceHandle": "continue",
                    "target": "end",
                    "targetHandle": "start"
                }
            ]
        }
        
        # Sample chatbot
        self.sample_chatbot = Chatbot(
            id=self.chatbot_id,
            tenant_id=self.tenant_id,
            name="Test Rule Bot",
            type="RULE",
            status="DRAFT"
        )
    
    def teardown_method(self):
        """Clean up dependency overrides"""
        self.test_app.dependency_overrides.clear()

    @patch('app.services.conversation_event_publisher.ConversationEventPublisher._publish_event')
    @patch('app.routers.chatbot.RedisService')
    @patch('app.services.chatbot_service.ChatbotService.get_chatbot_questions_for_conversation')
    @patch('app.services.chatbot_service.ChatbotService.find_chatbot_by_account')
    def test_start_conversation_ai_based_event_and_200(self, mock_find_cb, mock_get_qs, mock_redis_cls, mock_publish):
        """Verify AI-based start publishes event with message and returns 200-only"""
        # AI chatbot
        ai_chatbot = Chatbot(id=self.chatbot_id, tenant_id=self.tenant_id, name="AI Bot", type="AI", status="ACTIVE")
        mock_find_cb.return_value = ai_chatbot
        # Provide questions so first question exists
        mock_get_qs.return_value = [
            {
                "id": "q1",
                "question": "What's your name?",
                "position": 1,
                "entity_fields": [
                    {"entity_type": "lead", "field_id": 1, "name": "firstName", "display_name": "First Name", "standard": True}
                ]
            }
        ]
        from unittest.mock import AsyncMock
        mock_publish.return_value = AsyncMock(return_value=True)

        # Request body with messageConversationId
        body = {
            "message": "Hi",
            "entityDetails": [{"id": 1, "entityType": "lead"}],
            "connectedAccount": {"id": 10, "name": "acc"},
            "messageConversationId": "msg-123"
        }

        resp = self.client.post("/v1/chatbot/conversations", json=body, headers={"Authorization": "Bearer test-token"})
        assert resp.status_code == 200
        assert resp.content in (b"", b"null")  # FastAPI may send empty

        # Verify payload passed to _publish_event
        assert mock_publish.await_count == 1
        args, kwargs = mock_publish.await_args
        payload = kwargs.get('payload')
        assert payload["message"] == "What's your name?"
        assert payload["completed"] is False
        assert payload["messageConversationId"] == "msg-123"
        assert payload["chatbotType"] == "AI"
        assert payload.get("nodeDetails") is None

    @patch('app.services.conversation_event_publisher.ConversationEventPublisher._publish_event')
    @patch('app.routers.chatbot.RedisService')
    @patch('app.services.chatbot_service.ChatbotService.get_chatbot_questions_for_conversation')
    @patch('app.services.chatbot_service.ChatbotService.find_chatbot_by_account')
    def test_start_conversation_rule_based_event_and_200(self, mock_find_cb, mock_get_qs, mock_redis_cls, mock_publish):
        """Verify RULE-based start publishes event with nodeDetails and message=null"""
        # RULE chatbot
        rule_chatbot = Chatbot(id=self.chatbot_id, tenant_id=self.tenant_id, name="Rule Bot", type="RULE", status="ACTIVE")
        mock_find_cb.return_value = rule_chatbot
        # Questions still needed for the flow; not used in message for rule-based
        mock_get_qs.return_value = [
            {
                "id": "q1",
                "question": "What's your name?",
                "position": 1,
                "entity_fields": [
                    {"entity_type": "lead", "field_id": 1, "name": "firstName", "display_name": "First Name", "standard": True}
                ]
            }
        ]
        from unittest.mock import AsyncMock
        mock_publish.return_value = AsyncMock(return_value=True)

        # DB query side-effects for nodes and edges used to compute nodeDetails
        # Arrange dependency override get_db already returns self.mock_db
        start_node = ChatbotNode(
            id="n-db-1",
            chatbot_id=self.chatbot_id,
            tenant_id=self.tenant_id,
            node_id="123333",
            name="n1",
            type="question",
            position_x=100,
            position_y=100,
            data={"text": "Question to be ask lead first name?", "options": []},
            variable_mapping=None
        )
        # edges empty so start_node is selected
        self.mock_db.query.return_value.filter.return_value.first.return_value = start_node
        self.mock_db.query.return_value.filter.return_value.first.return_value = start_node
        self.mock_db.query.return_value.filter.return_value.all.side_effect = [
            [start_node],  # nodes (first query)
            [],            # edges (first query)
            [start_node],  # nodes (second query for variable substitution)
            []             # edges (second query for variable substitution)
        ]

        body = {
            "message": "Hi",
            "entityDetails": [{"id": 1, "entityType": "lead"}],
            "connectedAccount": {"id": 10, "name": "acc"},
            "messageConversationId": "msg-xyz"
        }

        resp = self.client.post("/v1/chatbot/conversations", json=body, headers={"Authorization": "Bearer test-token"})
        assert resp.status_code == 200

        assert mock_publish.await_count == 1
        args, kwargs = mock_publish.await_args
        payload = kwargs.get('payload')
        assert payload["message"] is None
        assert payload["completed"] is False
        assert payload["messageConversationId"] == "msg-xyz"
        assert payload.get("chatbotType") == "RULE"
        nd = payload.get("nodeDetails")
        assert isinstance(nd, dict)
        assert nd.get("id") == "123333"
        assert nd.get("name") == "n1"
        assert nd.get("type") == "question"
        assert nd.get("data", {}).get("text") == "Question to be ask lead first name?"

    @patch('app.services.conversation_event_publisher.ConversationEventPublisher._publish_event')
    @patch('app.routers.chatbot.charge_calculator')
    @patch('app.routers.chatbot.RedisService')
    @patch('app.services.chatbot_service.ChatbotService.get_chatbot_questions_for_conversation')
    @patch('app.services.chatbot_service.ChatbotService.find_chatbot_by_account')
    def test_start_conversation_exact_json_ai_based(self, mock_find_cb, mock_get_qs, mock_redis_cls, mock_charge_calc, mock_publish):
        """Assert the complete JSON payload for AI-based chatbot start event"""
        from unittest.mock import AsyncMock
        mock_publish.return_value = AsyncMock(return_value=True)

        ai_chatbot = Chatbot(id=self.chatbot_id, tenant_id=self.tenant_id, name="AI Bot", type="AI", status="ACTIVE")
        ai_chatbot.welcome_message = "Hi, Welcome to kylas! How can I help you?"
        mock_find_cb.return_value = ai_chatbot
        mock_get_qs.return_value = [
            {
                "id": "q1",
                "question": "Thank you, John! What's your email address?",
                "position": 1,
                "entity_fields": [
                    {"entity_type": "lead", "field_id": 2, "name": "email", "display_name": "Email", "standard": True}
                ]
            }
        ]
        mock_charge_calc.calculate_question_charge.return_value = 0

        body = {
            "message": "Hi",
            "entityDetails": [{"id": 1, "entityType": "lead"}],
            "connectedAccount": {"id": 10, "name": "acc"},
            "messageConversationId": "msg-conv-uuid"
        }

        resp = self.client.post("/v1/chatbot/conversations", json=body, headers={"Authorization": "Bearer test-token"})
        assert resp.status_code == 200

        # Capture final payload passed to _publish_event
        assert mock_publish.await_count == 1
        args, kwargs = mock_publish.await_args
        # kwargs include routing_key and payload
        payload = kwargs.get('payload')
        assert isinstance(payload, dict)

        # Build expected and normalize dynamic conversation id
        expected = {
            "messageConversationId": "msg-conv-uuid",
            "chatbotConversationId": payload.get("chatbotConversationId"),  # use actual id
            "welcomeMessage": "Hi, Welcome to kylas! How can I help you?",
            "message": "Thank you, John! What's your email address?",
            "completed": False,
            "charge": 0,
            "chatbotType": "AI",
            "nodeDetails": None
        }
        # Assert exact match
        assert payload == expected

    @patch('app.services.conversation_event_publisher.ConversationEventPublisher._publish_event')
    @patch('app.routers.chatbot.charge_calculator')
    @patch('app.routers.chatbot.RedisService')
    @patch('app.services.chatbot_service.ChatbotService.get_chatbot_questions_for_conversation')
    @patch('app.services.chatbot_service.ChatbotService.find_chatbot_by_account')
    def test_start_conversation_exact_json_rule_based(self, mock_find_cb, mock_get_qs, mock_redis_cls, mock_charge_calc, mock_publish):
        """Assert the complete JSON payload for RULE-based chatbot start event including nodeDetails"""
        from unittest.mock import AsyncMock
        mock_publish.return_value = AsyncMock(return_value=True)

        rule_chatbot = Chatbot(id=self.chatbot_id, tenant_id=self.tenant_id, name="Rule Bot", type="RULE", status="ACTIVE")
        rule_chatbot.welcome_message = "Hi, Welcome to kylas! How can I help you?"
        mock_find_cb.return_value = rule_chatbot
        mock_get_qs.return_value = [
            {
                "id": "q1",
                "question": "ignored for rule",
                "position": 1,
                "entity_fields": [
                    {"entity_type": "lead", "field_id": 1, "name": "firstName", "display_name": "First Name", "standard": True}
                ]
            }
        ]
        mock_charge_calc.calculate_question_charge.return_value = 1  # ignored for rule-based

        # DB returns nodes/edges so service can build nodeDetails
        start_node = ChatbotNode(
            id="n-db-1",
            chatbot_id=self.chatbot_id,
            tenant_id=self.tenant_id,
            node_id="123333",
            name="n1",
            type="question",
            position_x=100,
            position_y=100,
            data={"text": "Question to be ask lead first name?", "options": []},
            variable_mapping=None
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = start_node
        self.mock_db.query.return_value.filter.return_value.first.return_value = start_node
        self.mock_db.query.return_value.filter.return_value.all.side_effect = [
            [start_node],  # nodes (first query)
            [],            # edges (first query)
            [start_node],  # nodes (second query for variable substitution)
            []             # edges (second query for variable substitution)
        ]

        body = {
            "message": "Hi",
            "entityDetails": [{"id": 1, "entityType": "lead"}],
            "connectedAccount": {"id": 10, "name": "acc"},
            "messageConversationId": "msg-conv-uuid"
        }

        resp = self.client.post("/v1/chatbot/conversations", json=body, headers={"Authorization": "Bearer test-token"})
        assert resp.status_code == 200

        assert mock_publish.await_count == 1
        args, kwargs = mock_publish.await_args
        payload = kwargs.get('payload')
        assert isinstance(payload, dict)

        expected = {
            "messageConversationId": "msg-conv-uuid",
            "chatbotConversationId": payload.get("chatbotConversationId"),
            "welcomeMessage": "Hi, Welcome to kylas! How can I help you?",
            "message": None,
            "completed": False,
            "charge": 0,
            "chatbotType": "RULE",
            "nodeDetails": {
                "id": "123333",
                "name": "n1",
                "type": "question",
                "isFirstNode": True,
                "data": {
                    "text": "Question to be ask lead first name?"
                }
            }
        }
        assert payload == expected

    @patch('app.services.conversation_event_publisher.ConversationEventPublisher._publish_event')
    @patch('app.routers.chatbot.RedisService')
    @patch('app.services.chatbot_service.ChatbotService.get_chatbot_questions_for_conversation')
    @patch('app.services.chatbot_service.ChatbotService.find_chatbot_by_account')
    def test_start_conversation_rule_based_question_options_combined_text(self, mock_find_cb, mock_get_qs, mock_redis_cls, mock_publish):
        """RULE start: question node should combine options into numbered text lines in nodeDetails.data.text"""
        from unittest.mock import AsyncMock
        mock_publish.return_value = AsyncMock(return_value=True)

        rule_chatbot = Chatbot(id=self.chatbot_id, tenant_id=self.tenant_id, name="Rule Bot", type="RULE", status="ACTIVE")
        mock_find_cb.return_value = rule_chatbot
        mock_get_qs.return_value = []

        # DB returns start question node with options
        start_node = ChatbotNode(
            id="n-db-1",
            chatbot_id=self.chatbot_id,
            tenant_id=self.tenant_id,
            node_id="n1",
            name="ask_document_type",
            type="question",
            position_x=100,
            position_y=100,
            data={
                "text": "What do you want?",
                "options": [
                    {"name": "1", "text": "pdf document"},
                    {"name": "2", "text": "excel document"},
                    {"name": "3", "text": "word document"}
                ]
            },
            variable_mapping=None
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = start_node
        self.mock_db.query.return_value.filter.return_value.all.side_effect = [
            [start_node],  # nodes (first query)
            [],            # edges (first query)
            [start_node],  # nodes (second query for variable substitution)
            []             # edges (second query for variable substitution)
        ]

        body = {
            "message": "Hi",
            "entityDetails": [{"id": 1, "entityType": "lead"}],
            "connectedAccount": {"id": 10, "name": "acc"},
            "messageConversationId": "msg-rule-1"
        }

        resp = self.client.post("/v1/chatbot/conversations", json=body, headers={"Authorization": "Bearer test-token"})
        assert resp.status_code == 200

        # Validate combined text
        assert mock_publish.await_count == 1
        _, kwargs = mock_publish.await_args
        payload = kwargs.get('payload')
        nd = payload.get("nodeDetails")
        assert nd and nd.get("type") == "question"
        combined_text = nd.get("data", {}).get("text")
        assert combined_text is not None
        # Expected numbered options appended
        expected_lines = [
            "What do you want?",
            "1. pdf document",
            "2. excel document",
            "3. word document"
        ]
        assert combined_text == "\n".join(expected_lines)

    @patch('app.services.conversation_event_publisher.ConversationEventPublisher._publish_event')
    @patch('app.routers.chatbot.RedisService')
    @patch('app.services.chatbot_service.ChatbotService.get_chatbot_questions_for_conversation')
    @patch('app.services.chatbot_service.ChatbotService.find_chatbot_by_account')
    def test_start_conversation_rule_based_send_message_blocks(self, mock_find_cb, mock_get_qs, mock_redis_cls, mock_publish):
        """RULE start: sendMessage node should include text and media blocks in nodeDetails.data"""
        from unittest.mock import AsyncMock
        mock_publish.return_value = AsyncMock(return_value=True)

        rule_chatbot = Chatbot(id=self.chatbot_id, tenant_id=self.tenant_id, name="Rule Bot", type="RULE", status="ACTIVE")
        mock_find_cb.return_value = rule_chatbot
        mock_get_qs.return_value = []

        media_payload = {
            "fileId": 14,
            "fileName": "Quotation.pdf",
            "fileSize": 51912,
            "fileType": "document",
            "fileCaption": "Sample Document"
        }

        start_node = ChatbotNode(
            id="n-db-2",
            chatbot_id=self.chatbot_id,
            tenant_id=self.tenant_id,
            node_id="123333",
            name="send_pdf",
            type="sendMessage",
            position_x=400,
            position_y=50,
            data={
                "options": [
                    {"type": "text", "text": "Here is your document"},
                    {"type": "media", "mediaFile": media_payload}
                ]
            },
            variable_mapping=None
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = start_node
        self.mock_db.query.return_value.filter.return_value.all.side_effect = [
            [start_node],  # nodes (first query)
            [],            # edges (first query)
            [start_node],  # nodes (second query for variable substitution)
            []             # edges (second query for variable substitution)
        ]

        body = {
            "message": "Hi",
            "entityDetails": [{"id": 1, "entityType": "lead"}],
            "connectedAccount": {"id": 10, "name": "acc"},
            "messageConversationId": "msg-rule-2"
        }

        resp = self.client.post("/v1/chatbot/conversations", json=body, headers={"Authorization": "Bearer test-token"})
        assert resp.status_code == 200

        # Validate blocks structure
        assert mock_publish.await_count == 1
        _, kwargs = mock_publish.await_args
        payload = kwargs.get('payload')
        nd = payload.get("nodeDetails")
        assert nd and nd.get("type") == "sendMessage"
        blocks = nd.get("data")
        assert isinstance(blocks, list)
        assert {"type": "text", "text": "Here is your document"} in blocks
        assert {"type": "media", "mediaFile": media_payload} in blocks

    @patch('app.routers.chatbot.RuleBasedChatbotService')
    def test_create_rule_based_flow_success(self, mock_service_class):
        """Test successful creation of rule-based flow"""
        # Setup mocks
        
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        # Mock chatbot exists
        self.mock_db.query.return_value.filter.return_value.first.return_value = self.sample_chatbot
        
        # Mock flow creation success (validation happens inside create_flow)
        mock_service.create_flow.return_value = {
            "message": "Rule-based chatbot flow created successfully",
            "chatbot_id": self.chatbot_id,
            "nodes_count": 2,
            "edges_count": 1
        }
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Rule-based chatbot flow created successfully"
        assert data["chatbot_id"] == self.chatbot_id
        assert data["nodes_count"] == 2
        assert data["edges_count"] == 1
        
        # Verify service methods were called (validate_flow is called inside create_flow)
        mock_service.create_flow.assert_called_once()

    def test_create_rule_based_flow_chatbot_not_found(self):
        """Test flow creation when chatbot doesn't exist"""
        # Setup mocks
        # Mock chatbot not found
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions
        assert response.status_code == 404
        assert "Chatbot not found" in response.json()["detail"]

    def test_create_rule_based_flow_wrong_chatbot_type(self):
        """Test flow creation for non-rule-based chatbot"""
        # Setup mocks
        # Mock AI chatbot (not RULE type)
        ai_chatbot = Chatbot(
            id=self.chatbot_id,
            tenant_id=self.tenant_id,
            name="Test AI Bot",
            type="AI",
            status="DRAFT"
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = ai_chatbot
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions
        assert response.status_code == 400
        assert "only for rule-based chatbots" in response.json()["detail"]

    @patch('app.routers.chatbot.RuleBasedChatbotService')
    def test_create_rule_based_flow_validation_failure(self, mock_service_class):
        """Test flow creation with validation failures"""
        # Setup mocks
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        # Mock chatbot exists
        self.mock_db.query.return_value.filter.return_value.first.return_value = self.sample_chatbot
        
        # Mock validation failure - service will raise exception now
        mock_service.create_flow.side_effect = RuleBasedFlowValidationError(
            validation_errors=["Duplicate node name: start"],
            validation_warnings=[],
            message="Rule-based chatbot flow validation failed"
        )
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions - should return proper error format through error handler
        assert response.status_code == 400  # Bad Request for validation errors
        data = response.json()
        
        # Verify exact JSON structure matches expected format
        assert "errorCode" in data
        assert data["errorCode"] == "041120"
        assert "message" in data
        assert data["message"] == "Rule-based chatbot flow validation failed"
        assert "fieldErrors" in data
        assert isinstance(data["fieldErrors"], list)
        
        # Verify field errors have correct structure
        field_errors = data["fieldErrors"]
        assert len(field_errors) > 0
        
        for fe in field_errors:
            assert "field" in fe
            assert "message" in fe
            assert isinstance(fe["field"], str)
            assert isinstance(fe["message"], str)
            # Should have node-specific field names
            assert fe["field"].startswith(("node.", "edge.", "flow."))
        
        print(f"✅ API returns {len(field_errors)} properly structured field errors")

    @patch('app.routers.chatbot.RuleBasedChatbotService')
    def test_create_rule_based_flow_service_exception(self, mock_service_class):
        """Test flow creation when service throws exception"""
        # Setup mocks
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        # Mock chatbot exists
        self.mock_db.query.return_value.filter.return_value.first.return_value = self.sample_chatbot
        
        # Mock service exception
        mock_service.create_flow.side_effect = RuleBasedFlowCreationError(
            chatbot_id=self.chatbot_id,
            operation="flow_creation",
            message="An unexpected error occurred while creating the rule-based chatbot flow"
        )
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions - should return proper error format through error handler
        assert response.status_code == 500  # Internal Server Error for creation errors
        data = response.json()
        assert "errorCode" in data
        assert data["errorCode"] == "041121"
        assert "message" in data
        assert "creation failed" in data["message"].lower() or "error occurred" in data["message"].lower()

    def test_create_rule_based_flow_chatbot_not_found(self):
        """Test flow creation when chatbot doesn't exist"""
        # Setup mocks
        # Mock chatbot not found
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions - should return proper error format through error handler
        assert response.status_code == 404  # Not Found
        data = response.json()
        assert "errorCode" in data
        assert data["errorCode"] == "041011"  # ChatbotNotFoundException
        assert "message" in data
        assert "not found" in data["message"].lower()

    @patch('app.routers.chatbot.RuleBasedChatbotService')
    @patch('app.routers.chatbot.ChatbotEventPublisher')
    def test_create_rule_based_flow_with_active_chatbot_conflict(self, mock_event_publisher_class, mock_service_class):
        """Test rule-based flow creation when another chatbot is active for same connected account"""
        # Setup mocks
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        # Mock chatbot exists with connected account
        rule_chatbot = Chatbot(
            id=self.chatbot_id,
            tenant_id=self.tenant_id,
            name="Test Rule Bot",
            type="RULE",
            status="DRAFT",
            connected_account_id=123,
            connected_account_display_name="Test Account"
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = rule_chatbot
        
        # Mock existing active chatbot for same connected account
        active_chatbot = Chatbot(
            id="active-chatbot-789",
            tenant_id=self.tenant_id,
            name="Active AI Bot",
            type="AI",
            status="ACTIVE",
            connected_account_id=123,
            connected_account_display_name="Test Account"
        )
        
        # Setup query chain to return active chatbot when checking for conflicts
        def mock_query_side_effect(*args, **kwargs):
            if "status" in str(args) and "ACTIVE" in str(args):
                # This is the active chatbot check query
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = active_chatbot
                return mock_query
            else:
                # This is the original chatbot lookup query
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = rule_chatbot
                return mock_query
        
        self.mock_db.query.side_effect = mock_query_side_effect
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions
        assert response.status_code == 409  # Conflict
        data = response.json()
        assert "errorCode" in data
        assert data["errorCode"] == "041097"  # ActiveChatbotExistsError
        assert "active chatbot already exists" in data["message"].lower()
        assert data["details"]["connectedAccountId"] == 123
        assert data["details"]["existingChatbotName"] == "Active AI Bot"
        assert data["details"]["existingChatbotId"] == "active-chatbot-789"
        
        # Verify service was not called due to early conflict detection
        mock_service.create_flow.assert_not_called()
        # Verify event publisher was not called
        mock_event_publisher_class.assert_not_called()

    @patch('app.routers.chatbot.RuleBasedChatbotService')
    @patch('app.routers.chatbot.ChatbotEventPublisher')
    def test_create_rule_based_flow_auto_activation_success(self, mock_event_publisher_class, mock_service_class):
        """Test rule-based flow creation with automatic activation when no conflicts"""
        # Setup mocks
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        mock_event_publisher = Mock()
        mock_event_publisher_class.return_value = mock_event_publisher
        
        # Mock chatbot exists with connected account in DRAFT status
        rule_chatbot = Chatbot(
            id=self.chatbot_id,
            tenant_id=self.tenant_id,
            name="Test Rule Bot",
            type="RULE",
            status="DRAFT",
            connected_account_id=123,
            connected_account_display_name="Test Account"
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = rule_chatbot
        
        # Mock no active chatbot exists (None returned for active chatbot check)
        def mock_query_side_effect(*args, **kwargs):
            if "status" in str(args) and "ACTIVE" in str(args):
                # This is the active chatbot check query - return None (no active chatbot)
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = None
                return mock_query
            else:
                # This is the original chatbot lookup query
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = rule_chatbot
                return mock_query
        
        self.mock_db.query.side_effect = mock_query_side_effect
        
        # Mock flow creation success
        mock_service.create_flow.return_value = {
            "message": "Rule-based chatbot flow created successfully",
            "chatbot_id": self.chatbot_id,
            "nodes_count": 2,
            "edges_count": 1
        }
        
        # Mock event publisher methods
        mock_event_publisher.ensure_exchange_exists.return_value = None
        mock_event_publisher.publish_status_updated_event.return_value = None
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Rule-based chatbot flow created successfully"
        assert data["chatbot_id"] == self.chatbot_id
        
        # Verify service was called
        mock_service.create_flow.assert_called_once()
        
        # Verify chatbot status was updated to ACTIVE
        assert rule_chatbot.status == "ACTIVE"
        assert rule_chatbot.updated_by == self.user_id
        
        # Verify database commit was called
        self.mock_db.commit.assert_called()
        self.mock_db.refresh.assert_called_with(rule_chatbot)
        
        # Verify event publisher was called
        mock_event_publisher_class.assert_called_once()
        mock_event_publisher.ensure_exchange_exists.assert_called_once()
        mock_event_publisher.publish_status_updated_event.assert_called_once_with(
            status="ACTIVE",
            connected_account_id=123,
            connected_account_name="Test Account",
            chatbot_id=self.chatbot_id,
            tenant_id=self.tenant_id
        )

    @patch('app.routers.chatbot.RuleBasedChatbotService')
    @patch('app.routers.chatbot.ChatbotEventPublisher')
    def test_create_rule_based_flow_auto_activation_with_event_publish_failure(self, mock_event_publisher_class, mock_service_class):
        """Test rule-based flow creation with automatic activation when event publishing fails"""
        # Setup mocks
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        mock_event_publisher = Mock()
        mock_event_publisher_class.return_value = mock_event_publisher
        
        # Mock chatbot exists with connected account in DRAFT status
        rule_chatbot = Chatbot(
            id=self.chatbot_id,
            tenant_id=self.tenant_id,
            name="Test Rule Bot",
            type="RULE",
            status="DRAFT",
            connected_account_id=123,
            connected_account_display_name="Test Account"
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = rule_chatbot
        
        # Mock no active chatbot exists
        def mock_query_side_effect(*args, **kwargs):
            if "status" in str(args) and "ACTIVE" in str(args):
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = None
                return mock_query
            else:
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = rule_chatbot
                return mock_query
        
        self.mock_db.query.side_effect = mock_query_side_effect
        
        # Mock flow creation success
        mock_service.create_flow.return_value = {
            "message": "Rule-based chatbot flow created successfully",
            "chatbot_id": self.chatbot_id,
            "nodes_count": 2,
            "edges_count": 1
        }
        
        # Mock event publisher failure
        mock_event_publisher.ensure_exchange_exists.side_effect = Exception("Event publishing failed")
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions - should still succeed despite event publishing failure
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Rule-based chatbot flow created successfully"
        
        # Verify chatbot status was still updated to ACTIVE
        assert rule_chatbot.status == "ACTIVE"
        assert rule_chatbot.updated_by == self.user_id
        
        # Verify database operations were still performed
        self.mock_db.commit.assert_called()
        self.mock_db.refresh.assert_called_with(rule_chatbot)
        
        # Verify event publisher was called but failed gracefully
        mock_event_publisher_class.assert_called_once()
        mock_event_publisher.ensure_exchange_exists.assert_called_once()

    @patch('app.routers.chatbot.RuleBasedChatbotService')
    def test_create_rule_based_flow_no_connected_account(self, mock_service_class):
        """Test rule-based flow creation when chatbot has no connected account"""
        # Setup mocks
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        # Mock chatbot exists without connected account
        rule_chatbot = Chatbot(
            id=self.chatbot_id,
            tenant_id=self.tenant_id,
            name="Test Rule Bot",
            type="RULE",
            status="DRAFT",
            connected_account_id=None,
            connected_account_display_name=None
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = rule_chatbot
        
        # Mock flow creation success
        mock_service.create_flow.return_value = {
            "message": "Rule-based chatbot flow created successfully",
            "chatbot_id": self.chatbot_id,
            "nodes_count": 2,
            "edges_count": 1
        }
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Rule-based chatbot flow created successfully"
        
        # Verify service was called
        mock_service.create_flow.assert_called_once()
        
        # Verify chatbot status remains DRAFT (no auto-activation without connected account)
        assert rule_chatbot.status == "DRAFT"
        
        # Verify no database commit for status update
        self.mock_db.commit.assert_not_called()

    @patch('app.routers.chatbot.RuleBasedChatbotService')
    def test_create_rule_based_flow_already_active(self, mock_service_class):
        """Test rule-based flow creation when chatbot is already active"""
        # Setup mocks
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        # Mock chatbot exists and is already ACTIVE
        rule_chatbot = Chatbot(
            id=self.chatbot_id,
            tenant_id=self.tenant_id,
            name="Test Rule Bot",
            type="RULE",
            status="ACTIVE",
            connected_account_id=123,
            connected_account_display_name="Test Account"
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = rule_chatbot
        
        # Mock no other active chatbot exists
        def mock_query_side_effect(*args, **kwargs):
            if "status" in str(args) and "ACTIVE" in str(args):
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = None
                return mock_query
            else:
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = rule_chatbot
                return mock_query
        
        self.mock_db.query.side_effect = mock_query_side_effect
        
        # Mock flow creation success
        mock_service.create_flow.return_value = {
            "message": "Rule-based chatbot flow created successfully",
            "chatbot_id": self.chatbot_id,
            "nodes_count": 2,
            "edges_count": 1
        }
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Rule-based chatbot flow created successfully"
        
        # Verify service was called
        mock_service.create_flow.assert_called_once()
        
        # Verify chatbot status remains ACTIVE (no change needed)
        assert rule_chatbot.status == "ACTIVE"
        
        # Verify no additional database commit for status update
        self.mock_db.commit.assert_not_called()

    @patch('app.routers.chatbot.RuleBasedChatbotService')
    def test_create_rule_based_flow_ai_chatbot_no_auto_activation(self, mock_service_class):
        """Test that AI-based chatbots don't get auto-activated in rule-based endpoint"""
        # Setup mocks
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        # Mock AI chatbot (this should not happen in real scenario, but testing robustness)
        ai_chatbot = Chatbot(
            id=self.chatbot_id,
            tenant_id=self.tenant_id,
            name="Test AI Bot",
            type="AI",  # AI type, not RULE
            status="DRAFT",
            connected_account_id=123,
            connected_account_display_name="Test Account"
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = ai_chatbot
        
        # Mock no active chatbot exists
        def mock_query_side_effect(*args, **kwargs):
            if "status" in str(args) and "ACTIVE" in str(args):
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = None
                return mock_query
            else:
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = ai_chatbot
                return mock_query
        
        self.mock_db.query.side_effect = mock_query_side_effect
        
        # Mock flow creation success
        mock_service.create_flow.return_value = {
            "message": "Rule-based chatbot flow created successfully",
            "chatbot_id": self.chatbot_id,
            "nodes_count": 2,
            "edges_count": 1
        }
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions - should fail because this endpoint is only for RULE-based chatbots
        assert response.status_code == 400
        assert "only for rule-based chatbots" in response.json()["detail"]
        
        # Verify service was not called due to early type check
        mock_service.create_flow.assert_not_called()

    @patch('app.routers.chatbot.RuleBasedChatbotService')
    def test_create_rule_based_flow_rule_type_explicit_check(self, mock_service_class):
        """Test that the explicit RULE type check works correctly"""
        # Setup mocks
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        # Mock RULE chatbot
        rule_chatbot = Chatbot(
            id=self.chatbot_id,
            tenant_id=self.tenant_id,
            name="Test Rule Bot",
            type="RULE",  # Explicitly RULE type
            status="DRAFT",
            connected_account_id=123,
            connected_account_display_name="Test Account"
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = rule_chatbot
        
        # Mock no active chatbot exists
        def mock_query_side_effect(*args, **kwargs):
            if "status" in str(args) and "ACTIVE" in str(args):
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = None
                return mock_query
            else:
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = rule_chatbot
                return mock_query
        
        self.mock_db.query.side_effect = mock_query_side_effect
        
        # Mock flow creation success
        mock_service.create_flow.return_value = {
            "message": "Rule-based chatbot flow created successfully",
            "chatbot_id": self.chatbot_id,
            "nodes_count": 2,
            "edges_count": 1
        }
        
        # Make request
        response = self.client.post(
            f"/v1/chatbot/{self.chatbot_id}/rules",
            json=self.valid_flow_data,
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Assertions - should succeed and auto-activate because it's RULE type
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Rule-based chatbot flow created successfully"
        
        # Verify chatbot was auto-activated because it's RULE type
        assert rule_chatbot.status == "ACTIVE"
        assert rule_chatbot.updated_by == self.user_id
        
        # Verify database operations were performed
        self.mock_db.commit.assert_called()
        self.mock_db.refresh.assert_called_with(rule_chatbot)


class TestRuleBasedChatbotService:
    """Test cases for RuleBasedChatbotService methods"""
    
    def setup_method(self):
        """Setup test data"""
        self.service = RuleBasedChatbotService()
        self.tenant_id = 12345
        self.chatbot_id = "chatbot-456"
        self.user_id = "user-123"

        # Auth context same as other router tests
        from app.dependencies import AuthContext, get_auth_context
        self.auth_context = AuthContext(
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            source_type="test",
            source_id="test-source-id",
            source_name="test-source",
            permissions=[]
        )
        self.mock_db = Mock(spec=Session)
        
        # Sample flow data
        self.valid_flow_data = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        text="What is your name?",
                        options=[NodeOption(text="Continue", name="continue", position=1)]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-2",
                    name="end", 
                    type="sendMessage",
                    position=NodePosition(x=300, y=100),
                    data=NodeData(text="Thank you for your response!"),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge-1",
                    source="start",
                    sourceHandle="continue",
                    target="end",
                    targetHandle="start"
                )
            ]
        )

    def test_validate_flow_success(self):
        """Test successful flow validation"""
        result = self.service.validate_flow(self.valid_flow_data)
        
        assert result["valid"] is True
        assert "warnings" in result
        assert "summary" in result
        assert result["summary"]["total_nodes"] == 2
        assert result["summary"]["total_edges"] == 1

    def test_validate_flow_duplicate_node_names(self):
        """Test validation with duplicate node names"""
        # Create flow with duplicate node names
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(text="Question 1"),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-2",
                    name="start",  # Duplicate name
                    type="question",
                    position=NodePosition(x=200, y=100),
                    data=NodeData(text="Question 2"),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        with pytest.raises(RuleBasedFlowValidationError):
            self.service.validate_flow(invalid_flow)


    def test_validate_flow_invalid_node_type(self):
        """Test validation with invalid node type"""
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="invalid_type",  # Invalid type
                    position=NodePosition(x=100, y=100),
                    data=NodeData(text="Question"),
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        with pytest.raises(RuleBasedFlowValidationError):
            self.service.validate_flow(invalid_flow)


    def test_validate_flow_question_node_missing_text(self):
        """Test validation of question node without text"""
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(text=None),  # Missing text
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        with pytest.raises(RuleBasedFlowValidationError):
            self.service.validate_flow(invalid_flow)


    def test_validate_flow_question_node_without_options_passes(self):
        """Test validation of question node without options - should pass since options are optional"""
        valid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(text="Question", options=None),  # Missing options - should be OK
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        # This should not raise an exception since options are optional for question nodes
        result = self.service.validate_flow(valid_flow)
        assert result["valid"] is True


    def test_validate_flow_button_node_missing_buttons(self):
        """Test validation of button node without buttons"""
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="button",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(buttons=None),  # Missing buttons
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        with pytest.raises(RuleBasedFlowValidationError):
            self.service.validate_flow(invalid_flow)


    def test_validate_flow_list_node_missing_sections(self):
        """Test validation of list node without sections"""
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="list",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(sections=None),  # Missing sections
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        with pytest.raises(RuleBasedFlowValidationError):
            self.service.validate_flow(invalid_flow)


    def test_validate_flow_sendmessage_node_missing_content(self):
        """Test validation of sendMessage node without text or options"""
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="sendMessage",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(text=None, options=None),  # Missing both
                    variableMapping=[]
                )
            ],
            edges=[]
        )
        
        with pytest.raises(RuleBasedFlowValidationError):
            self.service.validate_flow(invalid_flow)


    def test_validate_flow_edge_references_nonexistent_node(self):
        """Test validation with edge referencing non-existent node"""
        invalid_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(text="Question"),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge-1",
                    source="start",
                    sourceHandle="continue",
                    target="nonexistent",  # Non-existent node
                    targetHandle="start"
                )
            ]
        )
        
        with pytest.raises(RuleBasedFlowValidationError):
            self.service.validate_flow(invalid_flow)


    def test_validate_flow_orphaned_nodes_warning(self):
        """Test validation with orphaned nodes (warning)"""
        flow_with_orphaned = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        text="Question",
                        options=[NodeOption(text="Option 1", name="opt1")]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-2",
                    name="orphaned",
                    type="question",
                    position=NodePosition(x=200, y=100),
                    data=NodeData(
                        text="Orphaned question",
                        options=[NodeOption(text="Option 1", name="opt1")]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]  # No edges, so 'orphaned' node is disconnected
        )
        
        # Since orphaned nodes are only warnings, this should not raise an exception
        try:
            self.service.validate_flow(flow_with_orphaned)
            # If we get here, validation passed (warnings only, no errors)
            success = True
        except RuleBasedFlowValidationError:
            success = False
        
        assert success, "Flow with only orphaned nodes should only generate warnings, not errors"

    def test_validate_flow_node_without_outgoing_edges_warning(self):
        """Test validation with node without outgoing edges (warning)"""
        flow_without_outgoing = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="start",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        text="Question",
                        options=[NodeOption(text="Option 1", name="opt1")]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-2",
                    name="end",
                    type="question",  # Not sendMessage or condition
                    position=NodePosition(x=200, y=100),
                    data=NodeData(
                        text="End question",
                        options=[NodeOption(text="Option 1", name="opt1")]
                    ),
                    variableMapping=[]
                )
            ],
            edges=[]  # No outgoing edges from 'end' node
        )
        
        # Since missing outgoing edges are only warnings, this should not raise an exception
        try:
            self.service.validate_flow(flow_without_outgoing)
            # If we get here, validation passed (warnings only, no errors)
            success = True
        except RuleBasedFlowValidationError:
            success = False
        
        assert success, "Flow with nodes missing outgoing edges should only generate warnings, not errors"

    @patch('app.services.rule_based_chatbot_service.ChatbotNode')
    @patch('app.services.rule_based_chatbot_service.ChatbotNodeEntityField')
    def test_create_nodes_success(self, mock_entity_field_class, mock_node_class):
        """Test successful node creation"""
        # Setup mocks
        mock_node = Mock()
        mock_node.node_id = "node-1"
        mock_node_class.return_value = mock_node
        
        mock_entity_field = Mock()
        mock_entity_field_class.return_value = mock_entity_field
        
        # Test data with entity fields
        nodes_data = [
            ChatbotNodeCreate(
                id="node-1",
                name="start",
                type="question",
                position=NodePosition(x=100, y=100),
                data=NodeData(
                    text="What is your name?",
                    entityFields=[
                        {
                            "entityType": "Lead",
                            "fieldId": 1,
                            "name": "firstName",
                            "displayName": "First Name",
                            "standard": True
                        }
                    ]
                ),
                variableMapping=[]
            )
        ]
        
        # Call method
        result = self.service._create_nodes(self.mock_db, self.chatbot_id, self.tenant_id, nodes_data)
        
        # Assertions
        assert len(result) == 1
        self.mock_db.add.assert_called()
        self.mock_db.flush.assert_called()
        
        # Verify node creation
        mock_node_class.assert_called_once()
        call_args = mock_node_class.call_args
        assert call_args[1]["chatbot_id"] == self.chatbot_id
        assert call_args[1]["tenant_id"] == self.tenant_id
        assert call_args[1]["node_id"] == "node-1"
        assert call_args[1]["name"] == "start"
        assert call_args[1]["type"] == "question"

    @patch('app.services.rule_based_chatbot_service.ChatbotEdge')
    def test_create_edges_success(self, mock_edge_class):
        """Test successful edge creation"""
        # Setup mocks
        mock_edge = Mock()
        mock_edge_class.return_value = mock_edge
        
        # Node name to ID mapping
        node_name_to_id_map = {
            "start": "node-1",
            "end": "node-2"
        }
        
        # Test data
        edges_data = [
            ChatbotEdgeCreate(
                id="edge-1",
                source="start",
                sourceHandle="continue",
                target="end",
                targetHandle="start"
            )
        ]
        
        # Call method
        result = self.service._create_edges(self.mock_db, self.chatbot_id, self.tenant_id, edges_data, node_name_to_id_map)
        
        # Assertions
        assert len(result) == 1
        self.mock_db.add.assert_called()
        
        # Verify edge creation
        mock_edge_class.assert_called_once()
        call_args = mock_edge_class.call_args
        assert call_args[1]["chatbot_id"] == self.chatbot_id
        assert call_args[1]["tenant_id"] == self.tenant_id
        assert call_args[1]["edge_id"] == "edge-1"
        assert call_args[1]["source_node"] == "node-1"
        assert call_args[1]["target_node"] == "node-2"

    def test_create_edges_missing_source_node(self):
        """Test edge creation with missing source node"""
        
        # Node name to ID mapping (missing source node)
        node_name_to_id_map = {
            "end": "node-2"
        }
        
        edges_data = [
            ChatbotEdgeCreate(
                id="edge-1",
                source="start",  # Not in mapping
                sourceHandle="continue",
                target="end",
                targetHandle="start"
            )
        ]
        
        # Call method and expect exception
        with pytest.raises(RuleBasedEdgeValidationError):
            self.service._create_edges(self.mock_db, self.chatbot_id, self.tenant_id, edges_data, node_name_to_id_map)

    def test_create_edges_missing_target_node(self):
        """Test edge creation with missing target node"""
        
        # Node name to ID mapping (missing target node)
        node_name_to_id_map = {
            "start": "node-1"
        }
        
        edges_data = [
            ChatbotEdgeCreate(
                id="edge-1",
                source="start",
                sourceHandle="continue",
                target="end",  # Not in mapping
                targetHandle="start"
            )
        ]
        
        # Call method and expect exception
        with pytest.raises(RuleBasedEdgeValidationError):
            self.service._create_edges(self.mock_db, self.chatbot_id, self.tenant_id, edges_data, node_name_to_id_map)

    def test_delete_existing_flow(self):
        """Test deletion of existing flow"""
        
        # Call method
        self.service._delete_existing_flow(self.mock_db, self.chatbot_id)
        
        # Verify database queries were made
        assert self.mock_db.query.call_count == 2  # Once for edges, once for nodes
        
        # Verify delete was called on both queries
        self.mock_db.query.return_value.filter.return_value.delete.assert_called()

    @patch('app.services.rule_based_chatbot_service.RuleBasedChatbotService._delete_existing_flow')
    @patch('app.services.rule_based_chatbot_service.RuleBasedChatbotService._create_nodes')
    @patch('app.services.rule_based_chatbot_service.RuleBasedChatbotService._create_edges')
    def test_create_flow_success(self, mock_create_edges, mock_create_nodes, mock_delete_existing):
        """Test successful flow creation"""
        # Setup mocks
        
        # Mock created nodes
        mock_node1 = Mock()
        mock_node1.name = "start"
        mock_node1.node_id = "node-1"
        mock_node2 = Mock()
        mock_node2.name = "end"
        mock_node2.node_id = "node-2"
        mock_create_nodes.return_value = [mock_node1, mock_node2]
        
        # Mock created edges
        mock_edge = Mock()
        mock_create_edges.return_value = [mock_edge]
        
        # Call method
        result = self.service.create_flow(self.mock_db, self.chatbot_id, self.tenant_id, self.valid_flow_data)
        
        # Assertions
        assert result["message"] == "Rule-based chatbot flow created successfully"
        assert result["chatbot_id"] == self.chatbot_id
        assert result["nodes_count"] == 2
        assert result["edges_count"] == 1
        
        # Verify methods were called
        mock_delete_existing.assert_called_once_with(self.mock_db, self.chatbot_id)
        mock_create_nodes.assert_called_once()
        mock_create_edges.assert_called_once()
        self.mock_db.commit.assert_called_once()

    @patch('app.services.rule_based_chatbot_service.RuleBasedChatbotService._delete_existing_flow')
    def test_create_flow_exception_rollback(self, mock_delete_existing):
        """Test flow creation with exception and rollback"""
        # Setup mocks
        mock_delete_existing.side_effect = Exception("Database error")
        
        # Call method and expect exception
        with pytest.raises(RuleBasedFlowCreationError):
            self.service.create_flow(self.mock_db, self.chatbot_id, self.tenant_id, self.valid_flow_data)
        
        # Verify rollback was called
        self.mock_db.rollback.assert_called_once()


class TestRuleBasedChatbotIntegration:
    """Integration tests for rule-based chatbot functionality"""
    
    def setup_method(self):
        """Setup test data"""
        self.service = RuleBasedChatbotService()
        self.tenant_id = 12345
        self.chatbot_id = "chatbot-456"
        from sqlalchemy.orm import Session
        from unittest.mock import Mock
        self.mock_db = Mock(spec=Session)
        def mock_get_db():
            return self.mock_db
        from app.database import get_db
        from app.dependencies import get_auth_context
        # Mount router app to use dependency overrides similar to previous class
        from fastapi import FastAPI
        from app.routers import chatbot as chatbot_router
        self.test_app = FastAPI()
        from app.error_handlers import register_error_handlers
        register_error_handlers(self.test_app)

        # Middleware to set auth context on request.state
        @self.test_app.middleware("http")
        async def test_auth_middleware(request, call_next):
            request.state.auth_context = self.auth_context
            response = await call_next(request)
            return response

        # Create auth context
        from app.dependencies import AuthContext
        self.auth_context = AuthContext(
            user_id="user-123",
            tenant_id=12345,
            source_type="test",
            source_id="test-source",
            source_name="Test Source",
            permissions=[]
        )

        self.test_app.include_router(chatbot_router.router)
        self.test_app.dependency_overrides[get_db] = mock_get_db
        self.test_app.dependency_overrides[get_auth_context] = lambda: self.auth_context
        from fastapi.testclient import TestClient
        self.client = TestClient(self.test_app)

    def test_complete_flow_validation_and_creation(self):
        """Test complete flow from validation to creation"""
        # Complex flow with multiple node types
        complex_flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="welcome",
                    type="sendMessage",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(text="Welcome! Let's get started."),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-2",
                    name="ask_name",
                    type="question",
                    position=NodePosition(x=100, y=200),
                    data=NodeData(
                        text="What is your name?",
                        options=[NodeOption(text="Continue", name="continue", position=1)]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-3",
                    name="ask_email",
                    type="question",
                    position=NodePosition(x=100, y=300),
                    data=NodeData(
                        text="What is your email?",
                        options=[NodeOption(text="Continue", name="continue", position=1)]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-4",
                    name="show_buttons",
                    type="button",
                    position=NodePosition(x=100, y=400),
                    data=NodeData(
                        buttons=[
                            NodeButton(name="yes", text="Yes", position=1),
                            NodeButton(name="no", text="No", position=2)
                        ]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-5",
                    name="show_list",
                    type="list",
                    position=NodePosition(x=100, y=500),
                    data=NodeData(
                        sections=[
                            NodeSection(
                                title="Options",
                                rows=[{"id": "1", "text": "Option 1"}]
                            )
                        ]
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-6",
                    name="condition",
                    type="condition",
                    position=NodePosition(x=100, y=600),
                    data=NodeData(
                        conditions=[{"field": "name", "operator": "equals", "value": "John"}],
                        operator="AND"
                    ),
                    variableMapping=[]
                ),
                ChatbotNodeCreate(
                    id="node-7",
                    name="thank_you",
                    type="sendMessage",
                    position=NodePosition(x=300, y=400),
                    data=NodeData(text="Thank you for your responses!"),
                    variableMapping=[]
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge-1",
                    source="welcome",
                    sourceHandle="default",
                    target="ask_name",
                    targetHandle="start"
                ),
                ChatbotEdgeCreate(
                    id="edge-2",
                    source="ask_name",
                    sourceHandle="continue",
                    target="ask_email",
                    targetHandle="start"
                ),
                ChatbotEdgeCreate(
                    id="edge-3",
                    source="ask_email",
                    sourceHandle="continue",
                    target="show_buttons",
                    targetHandle="start"
                ),
                ChatbotEdgeCreate(
                    id="edge-4",
                    source="show_buttons",
                    sourceHandle="yes",
                    target="show_list",
                    targetHandle="start"
                ),
                ChatbotEdgeCreate(
                    id="edge-5",
                    source="show_list",
                    sourceHandle="default",
                    target="condition",
                    targetHandle="start"
                ),
                ChatbotEdgeCreate(
                    id="edge-6",
                    source="condition",
                    sourceHandle="true",
                    target="thank_you",
                    targetHandle="start"
                )
            ]
        )
        
        # Test validation
        validation_result = self.service.validate_flow(complex_flow)
        
        assert validation_result["valid"] is True
        assert validation_result["summary"]["total_nodes"] == 7
        assert validation_result["summary"]["total_edges"] == 6
        assert "question" in validation_result["summary"]["node_types"]
        assert validation_result["summary"]["node_types"]["question"] == 2
        assert validation_result["summary"]["node_types"]["sendMessage"] == 2
        assert validation_result["summary"]["node_types"]["button"] == 1
        assert validation_result["summary"]["node_types"]["list"] == 1
        assert validation_result["summary"]["node_types"]["condition"] == 1

    def test_flow_with_entity_fields(self):
        """Test flow with entity field mappings"""
        flow_with_entities = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="node-1",
                    name="collect_lead_info",
                    type="question",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        text="What is your first name?",
                        options=[NodeOption(text="Continue", name="continue", position=1)],
                        entityFields=[
                            {
                                "id": "field-1",
                                "entityType": "Lead",
                                "fieldId": 1,
                                "name": "firstName",
                                "displayName": "First Name",
                                "standard": True
                            }
                        ]
                    ),
                    variableMapping=[
                        {
                            "id": "1",
                            "componentType": "BODY",
                            "variable": "1",
                            "entity": "lead",
                            "internalName": "firstName",
                            "fallbackValue": "",
                            "fieldType": "TEXT_FIELD"
                        }
                    ]
                )
            ],
            edges=[]
        )
        
        # Test validation - should not raise exception for valid flow
        try:
            self.service.validate_flow(flow_with_entities)
            # If we get here, validation passed (no exception raised)
            success = True
        except RuleBasedFlowValidationError:
            success = False
        
        assert success, "Valid flow with entity fields should not raise validation exceptions"


    @patch('app.services.conversation_event_publisher.ConversationEventPublisher._publish_event')
    @patch('app.routers.chatbot.RedisService')
    @patch('app.services.chatbot_service.ChatbotService.get_chatbot_questions_for_conversation')
    @patch('app.services.chatbot_service.ChatbotService.find_chatbot_by_account')
    def test_start_conversation_rule_based_question_options_combined_text(self, mock_find_cb, mock_get_qs, mock_redis_cls, mock_publish):
        """RULE start: question node should combine options into numbered text lines in nodeDetails.data.text"""
        from unittest.mock import AsyncMock
        mock_publish.return_value = AsyncMock(return_value=True)

        rule_chatbot = Chatbot(id=self.chatbot_id, tenant_id=self.tenant_id, name="Rule Bot", type="RULE", status="ACTIVE")
        mock_find_cb.return_value = rule_chatbot
        mock_get_qs.return_value = []

        # DB returns start question node with options
        start_node = ChatbotNode(
            id="n-db-1",
            chatbot_id=self.chatbot_id,
            tenant_id=self.tenant_id,
            node_id="n1",
            name="ask_document_type",
            type="question",
            position_x=100,
            position_y=100,
            data={
                "text": "What do you want?",
                "options": [
                    {"name": "1", "text": "pdf document"},
                    {"name": "2", "text": "excel document"},
                    {"name": "3", "text": "word document"}
                ]
            },
            variable_mapping=None
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = start_node
        self.mock_db.query.return_value.filter.return_value.all.side_effect = [
            [start_node],  # nodes (first query)
            [],            # edges (first query)
            [start_node],  # nodes (second query for variable substitution)
            []             # edges (second query for variable substitution)
        ]

        body = {
            "message": "Hi",
            "entityDetails": [{"id": 1, "entityType": "lead"}],
            "connectedAccount": {"id": 10, "name": "acc"},
            "messageConversationId": "msg-rule-1"
        }

        resp = self.client.post("/v1/chatbot/conversations", json=body, headers={"Authorization": "Bearer test-token"})
        assert resp.status_code == 200

        # Validate combined text
        assert mock_publish.await_count == 1
        _, kwargs = mock_publish.await_args
        payload = kwargs.get('payload')
        nd = payload.get("nodeDetails")
        assert nd and nd.get("type") == "question"
        combined_text = nd.get("data", {}).get("text")
        assert combined_text is not None
        # Expected numbered options appended
        expected_lines = [
            "What do you want?",
            "1. pdf document",
            "2. excel document",
            "3. word document"
        ]
        assert combined_text == "\n".join(expected_lines)


    @patch('app.services.conversation_event_publisher.ConversationEventPublisher._publish_event')
    @patch('app.routers.chatbot.RedisService')
    @patch('app.services.chatbot_service.ChatbotService.get_chatbot_questions_for_conversation')
    @patch('app.services.chatbot_service.ChatbotService.find_chatbot_by_account')
    def test_start_conversation_rule_based_send_message_blocks(self, mock_find_cb, mock_get_qs, mock_redis_cls, mock_publish):
        """RULE start: sendMessage node should include text and media blocks in nodeDetails.data"""
        from unittest.mock import AsyncMock
        mock_publish.return_value = AsyncMock(return_value=True)

        rule_chatbot = Chatbot(id=self.chatbot_id, tenant_id=self.tenant_id, name="Rule Bot", type="RULE", status="ACTIVE")
        mock_find_cb.return_value = rule_chatbot
        mock_get_qs.return_value = []

        media_payload = {
            "fileId": 14,
            "fileName": "Quotation.pdf",
            "fileSize": 51912,
            "fileType": "document",
            "fileCaption": "Sample Document"
        }

        start_node = ChatbotNode(
            id="n-db-2",
            chatbot_id=self.chatbot_id,
            tenant_id=self.tenant_id,
            node_id="123333",
            name="send_pdf",
            type="sendMessage",
            position_x=400,
            position_y=50,
            data={
                "options": [
                    {"type": "text", "text": "Here is your document"},
                    {"type": "media", "mediaFile": media_payload}
                ]
            },
            variable_mapping=None
        )
        self.mock_db.query.return_value.filter.return_value.first.return_value = start_node
        self.mock_db.query.return_value.filter.return_value.all.side_effect = [
            [start_node],  # nodes (first query)
            [],            # edges (first query)
            [start_node],  # nodes (second query for variable substitution)
            []             # edges (second query for variable substitution)
        ]

        body = {
            "message": "Hi",
            "entityDetails": [{"id": 1, "entityType": "lead"}],
            "connectedAccount": {"id": 10, "name": "acc"},
            "messageConversationId": "msg-rule-2"
        }

        resp = self.client.post("/v1/chatbot/conversations", json=body, headers={"Authorization": "Bearer test-token"})
        assert resp.status_code == 200

        # Validate blocks structure
        assert mock_publish.await_count == 1
        _, kwargs = mock_publish.await_args
        payload = kwargs.get('payload')
        nd = payload.get("nodeDetails")
        assert nd and nd.get("type") == "sendMessage"
        blocks = nd.get("data")
        assert isinstance(blocks, list)
        assert {"type": "text", "text": "Here is your document"} in blocks
        assert {"type": "media", "mediaFile": media_payload} in blocks

    @pytest.mark.asyncio
    async def test_complete_conversation_with_new_payload_format(self):
        """Test conversation completion with new payload format"""
        
        # Mock conversation state in Redis
        conversation_state = {
            "entity_details": [
                {"id": 213452, "entityType": "LEAD"},
                {"id": 312662, "entityType": "CONTACT"}
            ],
            "answers": [
                {"question": "What's your name?", "answer": "John Doe", "field_name": "firstName", "entity_type": "LEAD"},
                {"question": "What's your email?", "answer": "<EMAIL>", "field_name": "email", "entity_type": "CONTACT"}
            ],
            "chatbot_id": "test-chatbot-123",
            "current_node": {
                "id": "n_pdf",
                "name": "send_pdf",
                "type": "sendMessage",
                "data": [
                    {
                        "type": "media",
                        "mediaFile": {
                            "fileId": 14,
                            "fileName": "Quotation_acne monty (1).pdf",
                            "fileSize": 51912,
                            "fileType": "document",
                            "fileCaption": "Sample Document"
                        }
                    }
                ]
            }
        }
        
        with patch('app.services.redis_service.RedisService.get_conversation_state') as mock_get_state, \
             patch('app.services.redis_service.RedisService.clear_conversation_state') as mock_clear_state, \
             patch('app.services.chatbot_service.ChatbotService.update_entities_after_conversation') as mock_update_entities, \
             patch('app.services.chatbot_service.ChatbotService.prepare_conversation_completion_payload') as mock_prepare_payload, \
             patch('app.services.conversation_event_publisher.ConversationEventPublisher.publish_conversation_completion') as mock_publish_completion:
            
            # Mock Redis state retrieval
            mock_get_state.return_value = conversation_state
            
            # Mock entity update results
            mock_update_entities.return_value = {
                "successful_updates": 2,
                "failed_updates": 0,
                "total_entities": 2
            }
            
            # Mock payload preparation
            mock_prepare_payload.return_value = {
                "chatbotConversationId": "test-conversation-123",
                "message": "Conversation completed successfully",
                "completed": True,
                "charge": 0,
                "chatbotType": "RULE",
                "entityDetails": [
                    {"entityId": 213452, "entityType": "LEAD", "ownerId": 159},
                    {"entityId": 312662, "entityType": "CONTACT", "ownerId": 200}
                ],
                "nodeDetails": {
                    "id": "n_pdf",
                    "name": "send_pdf",
                    "type": "sendMessage",
                    "data": [
                        {
                            "type": "media",
                            "mediaFile": {
                                "fileId": 14,
                                "fileName": "Quotation_acne monty (1).pdf",
                                "fileSize": 51912,
                                "fileType": "document",
                                "fileCaption": "Sample Document"
                            }
                        }
                    ]
                }
            }
            
            # Mock conversation completion publishing
            mock_publish_completion.return_value = True
            
            # Mock database conversation
            mock_conversation = Mock()
            mock_conversation.completed = False
            mock_conversation.entity_update_status = None
            mock_conversation.conversation_data = None
            
            with patch('app.database.get_db') as mock_get_db:
                mock_db = Mock()
                mock_db.query.return_value.filter.return_value.first.return_value = mock_conversation
                mock_get_db.return_value.__next__.return_value = mock_db
                
                # Make the request
                response = self.client.post(
                    "/v1/chatbot/conversations/test-conversation-123/complete",
                    headers={"Authorization": "Bearer test-token"}
                )
                
                # Verify response
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["message"] == "Conversation completed successfully"
                assert response_data["conversation_id"] == "test-conversation-123"
                assert response_data["entities_updated"] == 2
                assert response_data["entities_failed"] == 0
                assert response_data["total_entities"] == 2
                
                # Verify that payload preparation was called
                mock_prepare_payload.assert_called_once()
                call_args = mock_prepare_payload.call_args
                assert call_args[1]["conversation_id"] == "test-conversation-123"
                assert call_args[1]["entity_details"] == conversation_state["entity_details"]
                assert call_args[1]["conversation_state"] == conversation_state
                assert call_args[1]["token"] == "test-token"
                
                # Verify that conversation completion was published
                mock_publish_completion.assert_called_once()
                publish_args = mock_publish_completion.call_args
                assert publish_args[1]["chatbot_conversation_id"] == "test-conversation-123"
                assert publish_args[1]["completion_message"] == "Conversation completed successfully"
                assert publish_args[1]["charge"] == 0
                assert publish_args[1]["chatbot_type"] == "RULE"
                assert len(publish_args[1]["entity_details"]) == 2
                assert publish_args[1]["node_details"]["id"] == "n_pdf"
                
                # Verify Redis state was cleared
                mock_clear_state.assert_called_once_with("test-conversation-123")
        
        print("✓ Conversation completion with new payload format test completed")

    @pytest.mark.asyncio
    async def test_complete_conversation_publish_failure(self):
        """Test conversation completion when event publishing fails"""
        
        conversation_state = {
            "entity_details": [{"id": 213452, "entityType": "LEAD"}],
            "answers": [{"question": "What's your name?", "answer": "John Doe", "field_name": "firstName", "entity_type": "LEAD"}],
            "chatbot_id": "test-chatbot-123"
        }
        
        with patch('app.services.redis_service.RedisService.get_conversation_state') as mock_get_state, \
             patch('app.services.redis_service.RedisService.clear_conversation_state') as mock_clear_state, \
             patch('app.services.chatbot_service.ChatbotService.update_entities_after_conversation') as mock_update_entities, \
             patch('app.services.chatbot_service.ChatbotService.prepare_conversation_completion_payload') as mock_prepare_payload, \
             patch('app.services.conversation_event_publisher.ConversationEventPublisher.publish_conversation_completion') as mock_publish_completion:
            
            mock_get_state.return_value = conversation_state
            mock_update_entities.return_value = {"successful_updates": 1, "failed_updates": 0, "total_entities": 1}
            mock_prepare_payload.return_value = {
                "chatbotConversationId": "test-conversation-123",
                "message": "Done",
                "completed": True,
                "charge": 0,
                "chatbotType": "RULE",
                "entityDetails": [{"entityId": 213452, "entityType": "LEAD", "ownerId": 159}]
            }
            
            # Mock publishing failure
            mock_publish_completion.side_effect = Exception("Publish failed")
            
            mock_conversation = Mock()
            mock_conversation.completed = False
            mock_conversation.entity_update_status = None
            mock_conversation.conversation_data = None
            
            with patch('app.database.get_db') as mock_get_db:
                mock_db = Mock()
                mock_db.query.return_value.filter.return_value.first.return_value = mock_conversation
                mock_get_db.return_value.__next__.return_value = mock_db
                
                # Make the request - should still succeed even if publishing fails
                response = self.client.post(
                    "/v1/chatbot/conversations/test-conversation-123/complete",
                    headers={"Authorization": "Bearer test-token"}
                )
                
                # Verify response still succeeds
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["message"] == "Conversation completed successfully"
                
                # Verify that publishing was attempted
                mock_publish_completion.assert_called_once()
        
        print("✓ Conversation completion with publish failure test completed")

    @pytest.mark.asyncio
    async def test_complete_conversation_no_entities(self):
        """Test conversation completion when no entities are found"""
        
        conversation_state = {
            "entity_details": [],
            "answers": [],
            "chatbot_id": "test-chatbot-123"
        }
        
        with patch('app.services.redis_service.RedisService.get_conversation_state') as mock_get_state:
            mock_get_state.return_value = conversation_state
            
            response = self.client.post(
                "/v1/chatbot/conversations/test-conversation-123/complete",
                headers={"Authorization": "Bearer test-token"}
            )
            
            # Should return 400 error for no entities
            assert response.status_code == 400
            response_data = response.json()
            assert "No entities found" in response_data["detail"]
        
        print("✓ Conversation completion with no entities test completed")

    @pytest.mark.asyncio
    async def test_complete_conversation_not_found(self):
        """Test conversation completion when conversation is not found"""
        
        with patch('app.services.redis_service.RedisService.get_conversation_state') as mock_get_state:
            mock_get_state.return_value = None
            
            response = self.client.post(
                "/v1/chatbot/conversations/nonexistent-conversation/complete",
                headers={"Authorization": "Bearer test-token"}
            )
            
            # Should return 404 error
            assert response.status_code == 404
            response_data = response.json()
            assert "Conversation not found" in response_data["detail"]
        
        print("✓ Conversation completion not found test completed")

    @pytest.mark.asyncio
    async def test_complete_conversation_with_workflow_headers(self):
        """Test conversation completion when workflow reply headers are present"""
        
        conversation_state = {
            "conversation_id": "test-conversation-123",
            "chatbot_id": "test-chatbot-123",
            "tenant_id": 12345,
            "user_id": "user-123",
            "message_conversation_id": 3390,
            "answers": [
                {"question": "What is your name?", "answer": "John Doe"},
                {"question": "What is your email?", "answer": "<EMAIL>"}
            ],
            "entity_details": [
                {"id": 1, "name": "Lead 1", "entity": "lead", "ownerId": 123}
            ],
            "workflow_reply_headers": {
                "replyToExchange": "ex.workflow.response",
                "replyToEvent": "workflow.response.chatbot",
                "eventId": 80
            }
        }
        
        with patch('app.services.redis_service.RedisService.get_conversation_state') as mock_get_state, \
             patch('app.services.chatbot_service.ChatbotService') as mock_chatbot_service, \
             patch('app.services.conversation_event_publisher.ConversationEventPublisher') as mock_event_publisher, \
             patch('app.services.workflow_event_listener.workflow_event_listener') as mock_workflow_listener, \
             patch('app.database.get_db') as mock_get_db:
            
            # Setup mocks
            mock_get_state.return_value = conversation_state
            
            mock_chatbot_service_instance = Mock()
            mock_chatbot_service.return_value = mock_chatbot_service_instance
            mock_chatbot_service_instance.update_entities_after_conversation = Mock(return_value={
                "successful_updates": 1,
                "failed_updates": 0,
                "total_entities": 1
            })
            mock_chatbot_service_instance.prepare_conversation_completion_payload = Mock(return_value={
                "message": "Conversation completed successfully",
                "charge": 0,
                "chatbotType": "RULE",
                "entityDetails": conversation_state["entity_details"],
                "nodeDetails": {}
            })
            
            mock_event_publisher_instance = Mock()
            mock_event_publisher.return_value = mock_event_publisher_instance
            mock_event_publisher_instance.publish_conversation_completion = AsyncMock()
            
            mock_workflow_listener.publish_workflow_completion_success = AsyncMock()
            
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock conversation record
            mock_conversation = Mock()
            mock_conversation.completed = False
            mock_db.query.return_value.filter.return_value.first.return_value = mock_conversation
            
            # Make the request
            response = self.client.post(
                "/v1/chatbot/conversations/test-conversation-123/complete",
                headers={"Authorization": "Bearer test-token"}
            )
            
            # Verify response
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["message"] == "Conversation completed successfully"
            assert response_data["conversation_id"] == "test-conversation-123"
            
            # Verify workflow success event was published
            mock_workflow_listener.publish_workflow_completion_success.assert_called_once()
            workflow_call_args = mock_workflow_listener.publish_workflow_completion_success.call_args[1]
            
            assert workflow_call_args["conversation_id"] == "test-conversation-123"
            assert workflow_call_args["message_conversation_id"] == 3390
            assert workflow_call_args["chatbot_id"] == "test-chatbot-123"
            assert workflow_call_args["tenant_id"] == 12345
            assert workflow_call_args["reply_headers"] == conversation_state["workflow_reply_headers"]
            assert len(workflow_call_args["execution_details"]["entitiesUpdated"]) == 1
            assert len(workflow_call_args["execution_details"]["entitiesFailed"]) == 0
            assert workflow_call_args["execution_details"]["totalEntities"] == 1
        
        print("✓ Conversation completion with workflow headers test completed")

    @pytest.mark.asyncio
    async def test_complete_conversation_without_workflow_headers(self):
        """Test conversation completion when no workflow reply headers are present"""
        
        conversation_state = {
            "conversation_id": "test-conversation-123",
            "chatbot_id": "test-chatbot-123",
            "tenant_id": 12345,
            "user_id": "user-123",
            "message_conversation_id": 3390,
            "answers": [
                {"question": "What is your name?", "answer": "John Doe"}
            ],
            "entity_details": [
                {"id": 1, "name": "Lead 1", "entity": "lead", "ownerId": 123}
            ]
            # No workflow_reply_headers
        }
        
        with patch('app.services.redis_service.RedisService.get_conversation_state') as mock_get_state, \
             patch('app.services.chatbot_service.ChatbotService') as mock_chatbot_service, \
             patch('app.services.conversation_event_publisher.ConversationEventPublisher') as mock_event_publisher, \
             patch('app.services.workflow_event_listener.workflow_event_listener') as mock_workflow_listener, \
             patch('app.database.get_db') as mock_get_db:
            
            # Setup mocks
            mock_get_state.return_value = conversation_state
            
            mock_chatbot_service_instance = Mock()
            mock_chatbot_service.return_value = mock_chatbot_service_instance
            mock_chatbot_service_instance.update_entities_after_conversation = Mock(return_value={
                "successful_updates": 1,
                "failed_updates": 0,
                "total_entities": 1
            })
            mock_chatbot_service_instance.prepare_conversation_completion_payload = Mock(return_value={
                "message": "Conversation completed successfully",
                "charge": 0,
                "chatbotType": "RULE",
                "entityDetails": conversation_state["entity_details"],
                "nodeDetails": {}
            })
            
            mock_event_publisher_instance = Mock()
            mock_event_publisher.return_value = mock_event_publisher_instance
            mock_event_publisher_instance.publish_conversation_completion = AsyncMock()
            
            mock_workflow_listener.publish_workflow_completion_success = AsyncMock()
            
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock conversation record
            mock_conversation = Mock()
            mock_conversation.completed = False
            mock_db.query.return_value.filter.return_value.first.return_value = mock_conversation
            
            # Make the request
            response = self.client.post(
                "/v1/chatbot/conversations/test-conversation-123/complete",
                headers={"Authorization": "Bearer test-token"}
            )
            
            # Verify response
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["message"] == "Conversation completed successfully"
            assert response_data["conversation_id"] == "test-conversation-123"
            
            # Verify workflow success event was NOT published (no reply headers)
            mock_workflow_listener.publish_workflow_completion_success.assert_not_called()
        
        print("✓ Conversation completion without workflow headers test completed")

    @pytest.mark.asyncio
    async def test_complete_conversation_workflow_event_publishing_failure(self):
        """Test conversation completion when workflow event publishing fails"""
        
        conversation_state = {
            "conversation_id": "test-conversation-123",
            "chatbot_id": "test-chatbot-123",
            "tenant_id": 12345,
            "user_id": "user-123",
            "message_conversation_id": 3390,
            "answers": [
                {"question": "What is your name?", "answer": "John Doe"}
            ],
            "entity_details": [
                {"id": 1, "name": "Lead 1", "entity": "lead", "ownerId": 123}
            ],
            "workflow_reply_headers": {
                "replyToExchange": "ex.workflow.response",
                "replyToEvent": "workflow.response.chatbot",
                "eventId": 80
            }
        }
        
        with patch('app.services.redis_service.RedisService.get_conversation_state') as mock_get_state, \
             patch('app.services.chatbot_service.ChatbotService') as mock_chatbot_service, \
             patch('app.services.conversation_event_publisher.ConversationEventPublisher') as mock_event_publisher, \
             patch('app.services.workflow_event_listener.workflow_event_listener') as mock_workflow_listener, \
             patch('app.database.get_db') as mock_get_db:
            
            # Setup mocks
            mock_get_state.return_value = conversation_state
            
            mock_chatbot_service_instance = Mock()
            mock_chatbot_service.return_value = mock_chatbot_service_instance
            mock_chatbot_service_instance.update_entities_after_conversation = Mock(return_value={
                "successful_updates": 1,
                "failed_updates": 0,
                "total_entities": 1
            })
            mock_chatbot_service_instance.prepare_conversation_completion_payload = Mock(return_value={
                "message": "Conversation completed successfully",
                "charge": 0,
                "chatbotType": "RULE",
                "entityDetails": conversation_state["entity_details"],
                "nodeDetails": {}
            })
            
            mock_event_publisher_instance = Mock()
            mock_event_publisher.return_value = mock_event_publisher_instance
            mock_event_publisher_instance.publish_conversation_completion = AsyncMock()
            
            # Mock workflow event publishing to fail
            mock_workflow_listener.publish_workflow_completion_success = AsyncMock(side_effect=Exception("Workflow event publishing failed"))
            
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock conversation record
            mock_conversation = Mock()
            mock_conversation.completed = False
            mock_db.query.return_value.filter.return_value.first.return_value = mock_conversation
            
            # Make the request - should still succeed even if workflow event publishing fails
            response = self.client.post(
                "/v1/chatbot/conversations/test-conversation-123/complete",
                headers={"Authorization": "Bearer test-token"}
            )
            
            # Verify response - should still succeed despite workflow event failure
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["message"] == "Conversation completed successfully"
            assert response_data["conversation_id"] == "test-conversation-123"
            
            # Verify workflow success event was attempted
            mock_workflow_listener.publish_workflow_completion_success.assert_called_once()
        
        print("✓ Conversation completion with workflow event publishing failure test completed")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
