"""
Shared test fixtures and configuration for pytest
"""

import pytest
import sys
import os
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from testcontainers.postgres import PostgresContainer
from testcontainers.elasticsearch import ElasticSearchContainer
from testcontainers.redis import RedisContainer

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure asyncio for pytest
pytest_plugins = ('pytest_asyncio',)


@pytest.fixture
def mock_database():
    """Mock database session for unit tests"""
    mock_db = Mock()
    mock_db.query.return_value = Mock()
    mock_db.add.return_value = None
    mock_db.commit.return_value = None
    mock_db.rollback.return_value = None
    mock_db.close.return_value = None
    return mock_db


@pytest.fixture
def mock_elasticsearch():
    """Mock Elasticsearch client for unit tests"""
    mock_es = Mock()
    mock_es.indices.exists.return_value = True
    mock_es.indices.create.return_value = {"acknowledged": True}
    mock_es.indices.put_alias.return_value = {"acknowledged": True}
    mock_es.index.return_value = {"_id": "test_id", "result": "created"}
    mock_es.search.return_value = {
        "hits": {
            "total": {"value": 0},
            "hits": []
        }
    }
    return mock_es


@pytest.fixture
def mock_s3_service():
    """Mock S3 service for unit tests"""
    mock_s3 = Mock()
    mock_s3.upload_file.return_value = "test/s3/key.pdf"
    return mock_s3


@pytest.fixture
def mock_openai_service():
    """Mock OpenAI service for unit tests"""
    mock_openai = Mock()
    mock_openai.generate_embedding.return_value = [0.1] * 1024  # Mock embedding vector
    mock_openai.chat_completion.return_value = "Mock AI response"
    return mock_openai


@pytest.fixture
def sample_tenant_id():
    """Sample tenant ID for tests"""
    return 12345


@pytest.fixture
def sample_chatbot_data():
    """Sample chatbot data for tests"""
    return {
        "name": "Test Chatbot",
        "type": "AI",
        "description": "Test chatbot description",
        "status": "DRAFT",
        "welcome_message": "Welcome to test chatbot",
        "thank_you_message": "Thank you for using test chatbot",
        "entity_type": "LEAD",
        "connected_account_id": 123,
        "trigger": "NEW_ENTITY"
    }


@pytest.fixture
def sample_question_data():
    """Sample question data for tests"""
    return {
        "question": "What is your name?",
        "fieldId": 1,
        "displayName": "Name"
    }


@pytest.fixture
def sample_upload_file():
    """Mock upload file for tests"""
    from unittest.mock import AsyncMock
    
    mock_file = AsyncMock()
    mock_file.filename = "test_document.pdf"
    mock_file.size = 1024 * 1024  # 1MB
    mock_file.read.return_value = b"Mock PDF content"
    return mock_file


@pytest.fixture
def mock_auth_context():
    """Mock authentication context for tests"""
    mock_context = Mock()
    mock_context.tenant_id = 12345
    mock_context.user_id = "test_user_123"
    return mock_context


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment variables"""
    test_env_vars = {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_PORT': '5432',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_password',
        'ELASTICSEARCH_URL': 'http://localhost:9200',
        'OPENAI_API_KEY': 'test_openai_key',
        'S3_ENDPOINT_URL': 'https://test.s3.endpoint.com',
        'S3_ACCESS_KEY_ID': 'test_access_key',
        'S3_SECRET_ACCESS_KEY': 'test_secret_key',
        'S3_BUCKET_NAME': 'test-bucket',
        'RABBITMQ_HOST': 'localhost',
        'RABBITMQ_PORT': '5672',
        'RABBITMQ_USER': 'test_user',
        'RABBITMQ_PASSWORD': 'test_password'
    }
    
    with patch.dict(os.environ, test_env_vars):
        yield


@pytest.fixture
def mock_file_content():
    """Mock file content for upload tests"""
    return b"Mock file content for testing file uploads and processing"


@pytest.fixture
def mock_pdf_content():
    """Mock PDF content for document processing tests"""
    return "Mock extracted text from PDF document for testing purposes"


# Test Container Fixtures
@pytest.fixture(scope="session")
def postgres_container():
    """
    PostgreSQL test container for database integration tests
    """
    postgres = PostgresContainer("postgres:15", 
                                 dbname="test_chatbot_db",
                                 username="test_user", 
                                 password="test_password")
    postgres.start()
    yield postgres
    postgres.stop()


@pytest.fixture(scope="session")
def elasticsearch_container():
    """
    Elasticsearch test container for search integration tests
    """
    es = ElasticSearchContainer("elasticsearch:8.12.0")
    es.start()
    yield es
    es.stop()


@pytest.fixture(scope="session")
def redis_container():
    """
    Redis test container for caching integration tests
    """
    redis = RedisContainer("redis:7-alpine")
    redis.start()
    yield redis
    redis.stop()


@pytest.fixture(scope="session") 
def test_database_engine(postgres_container):
    """
    Create a SQLAlchemy engine connected to the test database container
    """
    connection_url = postgres_container.get_connection_url()
    engine = create_engine(connection_url, echo=False)
    
    # Create a simpler schema for testing - avoid complex foreign key dependencies
    with engine.begin() as conn:
        # Create basic tables for testing
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS chatbots (
                id VARCHAR PRIMARY KEY,
                name VARCHAR,
                type VARCHAR DEFAULT 'AI',
                tenant_id BIGINT,
                description VARCHAR,
                status VARCHAR DEFAULT 'DRAFT',
                welcome_message VARCHAR,
                thank_you_message VARCHAR,
                connected_account_display_name VARCHAR,
                connected_account_id INTEGER,
                trigger VARCHAR,
                created_by VARCHAR,
                updated_by VARCHAR,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        """))
        
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS users (
                id VARCHAR PRIMARY KEY,
                email VARCHAR UNIQUE,
                tenant_id BIGINT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS conversations (
                id VARCHAR PRIMARY KEY,
                chatbot_id VARCHAR,
                user_id VARCHAR,
                tenant_id BIGINT NOT NULL,
                status VARCHAR,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS chatbot_questions (
                id VARCHAR PRIMARY KEY,
                chatbot_id VARCHAR,
                question TEXT,
                field_id INTEGER,
                display_name VARCHAR,
                position INTEGER,
                tenant_id BIGINT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
    
    yield engine
    engine.dispose()


@pytest.fixture
def test_database_session(test_database_engine):
    """
    Create a database session for individual tests with rollback
    """
    connection = test_database_engine.connect()
    transaction = connection.begin()
    
    Session = sessionmaker(bind=connection)
    session = Session()
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def test_elasticsearch_client(elasticsearch_container):
    """
    Create an Elasticsearch client connected to the test container
    """
    from elasticsearch import Elasticsearch
    
    es_url = f"http://{elasticsearch_container.get_container_host_ip()}:{elasticsearch_container.get_exposed_port(9200)}"
    client = Elasticsearch([es_url])
    
    yield client


@pytest.fixture
def test_redis_client(redis_container):
    """
    Create a Redis client connected to the test container
    """
    import redis
    
    redis_host = redis_container.get_container_host_ip()
    redis_port = redis_container.get_exposed_port(6379)
    
    client = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)
    
    yield client
    client.flushall()  # Clean up after test


@pytest.fixture
def test_environment_with_containers(postgres_container, elasticsearch_container, redis_container):
    """
    Set up environment variables to use test containers
    """
    postgres_url = postgres_container.get_connection_url()
    es_url = f"http://{elasticsearch_container.get_container_host_ip()}:{elasticsearch_container.get_exposed_port(9200)}"
    redis_host = redis_container.get_container_host_ip()
    redis_port = redis_container.get_exposed_port(6379)
    
    test_env_vars = {
        'DATABASE_URL': postgres_url,
        'POSTGRES_HOST': postgres_container.get_container_host_ip(),
        'POSTGRES_PORT': str(postgres_container.get_exposed_port(5432)),
        'POSTGRES_DB': 'test_chatbot_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_password',
        'ELASTICSEARCH_URL': es_url,
        'REDIS_HOST': redis_host,
        'REDIS_PORT': str(redis_port),
        'REDIS_URL': f"redis://{redis_host}:{redis_port}/0",
        # Keep other non-container env vars
        'OPENAI_API_KEY': 'test_openai_key',
        'S3_ENDPOINT_URL': 'https://test.s3.endpoint.com',
        'S3_ACCESS_KEY_ID': 'test_access_key',
        'S3_SECRET_ACCESS_KEY': 'test_secret_key',
        'S3_BUCKET_NAME': 'test-bucket',
        'RABBITMQ_HOST': 'localhost',
        'RABBITMQ_PORT': '5672',
        'RABBITMQ_USER': 'test_user',
        'RABBITMQ_PASSWORD': 'test_password'
    }
    
    with patch.dict(os.environ, test_env_vars):
        yield


# Pytest markers for test categorization
pytest_plugins = []

def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names"""
    for item in items:
        # Add unit marker to unit tests
        if "unit" in item.nodeid:
            item.add_marker(pytest.mark.unit)
        
        # Add integration marker to integration tests
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Add slow marker to migration and comprehensive tests
        if any(keyword in item.nodeid for keyword in ["migration", "comprehensive", "api_integration"]):
            item.add_marker(pytest.mark.slow)
