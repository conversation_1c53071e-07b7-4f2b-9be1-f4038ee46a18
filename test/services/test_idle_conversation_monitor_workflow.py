"""
Test cases for Idle Conversation Monitor Service workflow failure events
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from app.services.idle_conversation_monitor import IdleConversationMonitor


class TestIdleConversationMonitorWorkflow:
    """Test cases for idle conversation monitor workflow failure events"""

    @pytest.fixture
    def idle_monitor(self):
        """Create an IdleConversationMonitor instance for testing"""
        return IdleConversationMonitor()

    @pytest.fixture
    def mock_conversation_state_with_workflow_headers(self):
        """Create mock conversation state with workflow reply headers"""
        return {
            "conversation_id": "test-conversation-id",
            "chatbot_id": "test-chatbot-id",
            "tenant_id": 478,
            "user_id": "794",
            "message_conversation_id": 3390,
            "answers": [
                {"question": "What is your name?", "answer": "<PERSON> Doe"},
                {"question": "What is your email?", "answer": "<EMAIL>"}
            ],
            "entity_details": [
                {"id": 1, "name": "Lead 1", "entity": "lead", "ownerId": 123}
            ],
            "workflow_reply_headers": {
                "replyToExchange": "ex.workflow.response",
                "replyToEvent": "workflow.response.chatbot",
                "eventId": 80
            }
        }

    @pytest.fixture
    def mock_conversation_state_without_workflow_headers(self):
        """Create mock conversation state without workflow reply headers"""
        return {
            "conversation_id": "test-conversation-id",
            "chatbot_id": "test-chatbot-id",
            "tenant_id": 478,
            "user_id": "794",
            "message_conversation_id": 3390,
            "answers": [
                {"question": "What is your name?", "answer": "John Doe"}
            ],
            "entity_details": [
                {"id": 1, "name": "Lead 1", "entity": "lead", "ownerId": 123}
            ]
            # No workflow_reply_headers
        }

    @pytest.mark.asyncio
    async def test_handle_idle_conversation_with_workflow_headers(self, idle_monitor, mock_conversation_state_with_workflow_headers):
        """Test handling idle conversation when workflow reply headers are present"""
        with patch('app.services.idle_conversation_monitor.get_db') as mock_get_db, \
             patch('app.services.idle_conversation_monitor.update_conversation_in_db') as mock_update_conversation, \
             patch.object(idle_monitor, '_handle_entity_updates') as mock_handle_entity_updates, \
             patch.object(idle_monitor, '_clear_redis_state') as mock_clear_redis, \
             patch.object(idle_monitor, '_publish_completion_event') as mock_publish_completion_event, \
             patch.object(idle_monitor, '_publish_workflow_failure_event') as mock_publish_workflow_failure:
            
            # Setup mocks
            mock_db = Mock()
            mock_get_db.return_value = iter([mock_db])
            mock_update_conversation.return_value = True
            
            # Mock Redis service
            idle_monitor.redis_service.get_conversation_state = Mock(return_value=mock_conversation_state_with_workflow_headers)
            
            # Execute
            await idle_monitor._handle_idle_conversation("test-conversation-id")
            
            # Verify conversation was updated in database
            mock_update_conversation.assert_called_once()
            
            # Verify entity updates were handled
            mock_handle_entity_updates.assert_called_once_with(mock_conversation_state_with_workflow_headers, "test-conversation-id")
            
            # Verify Redis state was cleared
            mock_clear_redis.assert_called_once_with("test-conversation-id")
            
            # Verify completion event was published
            mock_publish_completion_event.assert_called_once_with("test-conversation-id", mock_conversation_state_with_workflow_headers)
            
            # Verify workflow failure event was published
            mock_publish_workflow_failure.assert_called_once()
            call_args = mock_publish_workflow_failure.call_args[0]  # Positional arguments
            
            assert call_args[0] == "test-conversation-id"  # conversation_id
            assert call_args[1] == mock_conversation_state_with_workflow_headers  # state
            assert call_args[2] == "041202"  # error_code
            assert call_args[3] == "Conversation timed out due to inactivity"  # error_message

    @pytest.mark.asyncio
    async def test_handle_idle_conversation_without_workflow_headers(self, idle_monitor, mock_conversation_state_without_workflow_headers):
        """Test handling idle conversation when no workflow reply headers are present"""
        with patch('app.services.idle_conversation_monitor.get_db') as mock_get_db, \
             patch('app.services.idle_conversation_monitor.update_conversation_in_db') as mock_update_conversation, \
             patch.object(idle_monitor, '_handle_entity_updates') as mock_handle_entity_updates, \
             patch.object(idle_monitor, '_clear_redis_state') as mock_clear_redis, \
             patch.object(idle_monitor, '_publish_completion_event') as mock_publish_completion_event, \
             patch.object(idle_monitor, '_publish_workflow_failure_event') as mock_publish_workflow_failure:
            
            # Setup mocks
            mock_db = Mock()
            mock_get_db.return_value = iter([mock_db])
            mock_update_conversation.return_value = True
            
            # Mock Redis service
            idle_monitor.redis_service.get_conversation_state = Mock(return_value=mock_conversation_state_without_workflow_headers)
            
            # Execute
            await idle_monitor._handle_idle_conversation("test-conversation-id")
            
            # Verify conversation was updated in database
            mock_update_conversation.assert_called_once()
            
            # Verify entity updates were handled
            mock_handle_entity_updates.assert_called_once_with(mock_conversation_state_without_workflow_headers, "test-conversation-id")
            
            # Verify Redis state was cleared
            mock_clear_redis.assert_called_once_with("test-conversation-id")
            
            # Verify completion event was published
            mock_publish_completion_event.assert_called_once_with("test-conversation-id", mock_conversation_state_without_workflow_headers)
            
            # Verify workflow failure event was called but will skip internally (no reply headers)
            mock_publish_workflow_failure.assert_called_once()

    @pytest.mark.asyncio
    async def test_publish_workflow_failure_event_success(self, idle_monitor, mock_conversation_state_with_workflow_headers):
        """Test publishing workflow failure event successfully"""
        with patch('app.services.workflow_event_listener.workflow_event_listener') as mock_workflow_listener, \
             patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq:
            
            # Mock RabbitMQ service
            mock_rabbitmq.is_healthy = AsyncMock(return_value=True)
            mock_channel = Mock()
            mock_exchange = Mock()
            mock_exchange.publish = AsyncMock()
            mock_channel.declare_exchange = AsyncMock(return_value=mock_exchange)
            mock_rabbitmq.get_channel = AsyncMock(return_value=mock_channel)
            
            # Mock workflow event listener
            mock_workflow_listener.publish_workflow_completion_failure = AsyncMock()
            
            # Execute
            await idle_monitor._publish_workflow_failure_event(
                conversation_id="test-conversation-id",
                state=mock_conversation_state_with_workflow_headers,
                error_code="041202",
                error_message="Conversation timed out due to inactivity"
            )
            
            # Verify workflow failure event was published
            mock_workflow_listener.publish_workflow_completion_failure.assert_called_once()
            call_args = mock_workflow_listener.publish_workflow_completion_failure.call_args[1]
            
            assert call_args["conversation_id"] == "test-conversation-id"
            assert call_args["message_conversation_id"] == 3390
            assert call_args["error_code"] == "041202"
            assert call_args["error_message"] == "Conversation timed out due to inactivity"
            assert call_args["reply_headers"] == mock_conversation_state_with_workflow_headers["workflow_reply_headers"]

    @pytest.mark.asyncio
    async def test_publish_workflow_failure_event_no_reply_headers(self, idle_monitor, mock_conversation_state_without_workflow_headers):
        """Test publishing workflow failure event when no reply headers are present"""
        with patch('app.services.idle_conversation_monitor.workflow_event_listener') as mock_workflow_listener:
            mock_workflow_listener.publish_workflow_completion_failure = AsyncMock()
            
            # Execute
            await idle_monitor._publish_workflow_failure_event(
                conversation_id="test-conversation-id",
                state=mock_conversation_state_without_workflow_headers,
                error_code="041202",
                error_message="Conversation timed out due to inactivity"
            )
            
            # Verify workflow failure event was NOT published (no reply headers)
            mock_workflow_listener.publish_workflow_completion_failure.assert_not_called()

    @pytest.mark.asyncio
    async def test_publish_workflow_failure_event_exception_handling(self, idle_monitor, mock_conversation_state_with_workflow_headers):
        """Test that exceptions in workflow failure event publishing are handled gracefully"""
        with patch('app.services.workflow_event_listener.workflow_event_listener') as mock_workflow_listener, \
             patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq:
            
            # Mock RabbitMQ service
            mock_rabbitmq.is_healthy = AsyncMock(return_value=True)
            mock_channel = Mock()
            mock_exchange = Mock()
            mock_exchange.publish = AsyncMock()
            mock_channel.declare_exchange = AsyncMock(return_value=mock_exchange)
            mock_rabbitmq.get_channel = AsyncMock(return_value=mock_channel)
            
            # Mock workflow event publishing to fail
            mock_workflow_listener.publish_workflow_completion_failure = AsyncMock(side_effect=Exception("Workflow event publishing failed"))
            
            # Execute - should not raise exception
            await idle_monitor._publish_workflow_failure_event(
                conversation_id="test-conversation-id",
                state=mock_conversation_state_with_workflow_headers,
                error_code="041202",
                error_message="Conversation timed out due to inactivity"
            )
            
            # Verify workflow failure event was attempted
            mock_workflow_listener.publish_workflow_completion_failure.assert_called_once()

    @pytest.mark.asyncio
    async def test_publish_workflow_failure_event_none_reply_headers(self, idle_monitor):
        """Test publishing workflow failure event when reply headers are None"""
        conversation_state = {
            "conversation_id": "test-conversation-id",
            "workflow_reply_headers": None  # None reply headers
        }
        
        with patch('app.services.idle_conversation_monitor.workflow_event_listener') as mock_workflow_listener:
            mock_workflow_listener.publish_workflow_completion_failure = AsyncMock()
            
            # Execute
            await idle_monitor._publish_workflow_failure_event(
                conversation_id="test-conversation-id",
                state=conversation_state,
                error_code="041202",
                error_message="Conversation timed out due to inactivity"
            )
            
            # Verify workflow failure event was NOT published (None reply headers)
            mock_workflow_listener.publish_workflow_completion_failure.assert_not_called()

    @pytest.mark.asyncio
    async def test_publish_workflow_failure_event_empty_reply_headers(self, idle_monitor):
        """Test publishing workflow failure event when reply headers are empty"""
        conversation_state = {
            "conversation_id": "test-conversation-id",
            "workflow_reply_headers": {}  # Empty reply headers
        }
        
        with patch('app.services.idle_conversation_monitor.workflow_event_listener') as mock_workflow_listener:
            mock_workflow_listener.publish_workflow_completion_failure = AsyncMock()
            
            # Execute
            await idle_monitor._publish_workflow_failure_event(
                conversation_id="test-conversation-id",
                state=conversation_state,
                error_code="041202",
                error_message="Conversation timed out due to inactivity"
            )
            
            # Verify workflow failure event was NOT published (empty reply headers)
            mock_workflow_listener.publish_workflow_completion_failure.assert_not_called()

    @pytest.mark.asyncio
    async def test_publish_workflow_failure_event_missing_reply_to_exchange(self, idle_monitor):
        """Test publishing workflow failure event when replyToExchange is missing"""
        conversation_state = {
            "conversation_id": "test-conversation-id",
            "workflow_reply_headers": {
                "replyToEvent": "workflow.response.chatbot",
                "eventId": 80
                # Missing replyToExchange
            }
        }
        
        with patch('app.services.idle_conversation_monitor.workflow_event_listener') as mock_workflow_listener:
            mock_workflow_listener.publish_workflow_completion_failure = AsyncMock()
            
            # Execute
            await idle_monitor._publish_workflow_failure_event(
                conversation_id="test-conversation-id",
                state=conversation_state,
                error_code="041202",
                error_message="Conversation timed out due to inactivity"
            )
            
            # Verify workflow failure event was NOT published (missing replyToExchange)
            mock_workflow_listener.publish_workflow_completion_failure.assert_not_called()

    @pytest.mark.asyncio
    async def test_publish_workflow_failure_event_missing_reply_to_event(self, idle_monitor):
        """Test publishing workflow failure event when replyToEvent is missing"""
        conversation_state = {
            "conversation_id": "test-conversation-id",
            "workflow_reply_headers": {
                "replyToExchange": "ex.workflow.response",
                "eventId": 80
                # Missing replyToEvent
            }
        }
        
        with patch('app.services.idle_conversation_monitor.workflow_event_listener') as mock_workflow_listener:
            mock_workflow_listener.publish_workflow_completion_failure = AsyncMock()
            
            # Execute
            await idle_monitor._publish_workflow_failure_event(
                conversation_id="test-conversation-id",
                state=conversation_state,
                error_code="041202",
                error_message="Conversation timed out due to inactivity"
            )
            
            # Verify workflow failure event was NOT published (missing replyToEvent)
            mock_workflow_listener.publish_workflow_completion_failure.assert_not_called()

    @pytest.mark.asyncio
    async def test_handle_idle_conversation_no_state_found(self, idle_monitor):
        """Test handling idle conversation when no state is found"""
        with patch.object(idle_monitor.redis_service, 'get_conversation_state') as mock_get_state:
            mock_get_state.return_value = None  # No state found
            
            # Execute - should not raise exception
            await idle_monitor._handle_idle_conversation("test-conversation-id")
            
            # Verify get_conversation_state was called
            mock_get_state.assert_called_once_with("test-conversation-id")

    @pytest.mark.asyncio
    async def test_handle_idle_conversation_database_update_failure(self, idle_monitor, mock_conversation_state_with_workflow_headers):
        """Test handling idle conversation when database update fails"""
        with patch('app.services.idle_conversation_monitor.get_db') as mock_get_db, \
             patch('app.services.idle_conversation_monitor.update_conversation_in_db') as mock_update_conversation, \
             patch.object(idle_monitor, '_publish_workflow_failure_event') as mock_publish_workflow_failure:
            
            # Setup mocks
            mock_db = Mock()
            mock_get_db.return_value = iter([mock_db])
            mock_update_conversation.return_value = False  # Database update failed
            
            # Mock Redis service
            idle_monitor.redis_service.get_conversation_state = Mock(return_value=mock_conversation_state_with_workflow_headers)
            
            # Execute
            await idle_monitor._handle_idle_conversation("test-conversation-id")
            
            # Verify database update was attempted
            mock_update_conversation.assert_called_once()
            
            # Verify workflow failure event was NOT published (database update failed)
            mock_publish_workflow_failure.assert_not_called()

    @pytest.mark.asyncio
    async def test_handle_idle_conversation_exception_handling(self, idle_monitor, mock_conversation_state_with_workflow_headers):
        """Test that exceptions in handle_idle_conversation are handled gracefully"""
        with patch('app.services.idle_conversation_monitor.get_db') as mock_get_db, \
             patch.object(idle_monitor.redis_service, 'get_conversation_state') as mock_get_state:
            
            # Mock get_conversation_state to raise exception
            mock_get_state.side_effect = Exception("Redis error")
            
            # Execute - should not raise exception (exception should be caught and logged)
            try:
                await idle_monitor._handle_idle_conversation("test-conversation-id")
            except Exception:
                # This is expected to raise an exception, but it should be caught and logged
                pass
            
            # Verify get_conversation_state was called
            mock_get_state.assert_called_once_with("test-conversation-id")
