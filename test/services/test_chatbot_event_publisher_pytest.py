#!/usr/bin/env python3
"""
Pytest test cases for ChatbotEventPublisher
"""

import pytest
import json
from unittest.mock import MagicMock, AsyncMock, patch
from app.services.chatbot_event_publisher import ChatbotEventPublisher


@pytest.mark.asyncio
async def test_publish_chatbot_name_updated_comprehensive():
    """Test chatbot name update event publishing with success and failure scenarios"""
    # Initialize publisher
    publisher = ChatbotEventPublisher()
    
    # Test data
    chatbot_id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
    chatbot_name = "New Chatbot name"
    tenant_id = 33
    
    # Expected payload structure
    expected_payload = {
        "id": chatbot_id,
        "name": chatbot_name,
        "tenantId": tenant_id
    }
    
    # Test 1: Success scenario
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock()  # Success (no exception)
        
        # Test publishing chatbot name updated event
        result = await publisher.publish_chatbot_name_updated(
            chatbot_id=chatbot_id,
            chatbot_name=chatbot_name,
            tenant_id=tenant_id
        )
        
        # Success assertions
        assert result is True, "Publisher should return True on success"
        mock_rabbitmq.publish_message.assert_called_once()
        
        # Check the call arguments
        call_args = mock_rabbitmq.publish_message.call_args
        assert call_args[1]['exchange'] == 'ex.whatsappChatbot'
        assert call_args[1]['routing_key'] == 'whatsapp.chatbot.name.updated'
        assert call_args[1]['durable'] is True
        
        # Parse and check the message payload (body)
        message_body = json.loads(call_args[1]['message'])
        assert message_body == expected_payload, f"Expected {expected_payload}, got {message_body}"
        
        # Verify payload structure and content
        assert 'id' in message_body, "Payload should contain 'id' field"
        assert 'name' in message_body, "Payload should contain 'name' field"
        assert 'tenantId' in message_body, "Payload should contain 'tenantId' field"
        assert len(message_body) == 3, "Payload should contain exactly 3 fields"
        
        # Verify field types and values
        assert message_body['id'] == chatbot_id, f"ID should be {chatbot_id}"
        assert message_body['name'] == chatbot_name, f"Name should be {chatbot_name}"
        assert message_body['tenantId'] == tenant_id, f"TenantId should be {tenant_id}"
    
    # Test 2: Failure scenario
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(side_effect=Exception("Connection failed"))
        
        # Test publishing chatbot name updated event
        result = await publisher.publish_chatbot_name_updated(
            chatbot_id=chatbot_id,
            chatbot_name=chatbot_name,
            tenant_id=tenant_id
        )
        
        # Failure assertions
        assert result is False, "Publisher should return False on exception"
        mock_rabbitmq.publish_message.assert_called_once()


@pytest.mark.asyncio
async def test_publish_chatbot_name_updated_exception():
    """Test chatbot name update event publishing exception handling"""
    # Initialize publisher
    publisher = ChatbotEventPublisher()
    
    # Mock RabbitMQ service to raise exception
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(side_effect=Exception("Connection failed"))
        
        # Test publishing chatbot name updated event
        result = await publisher.publish_chatbot_name_updated(
            chatbot_id="test-id",
            chatbot_name="Test Name",
            tenant_id=123
        )
        
        # Assertions
        assert result is False, "Publisher should return False on exception"
        mock_rabbitmq.publish_message.assert_called_once()


@pytest.mark.asyncio
async def test_publish_chatbot_name_updated_payload_format():
    """Test that the payload format is exactly as specified"""
    # Initialize publisher
    publisher = ChatbotEventPublisher()
    
    # Test data
    chatbot_id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
    chatbot_name = "New Chatbot name"
    tenant_id = 33
    
    # Mock RabbitMQ service
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(return_value=True)
        
        # Test publishing chatbot name updated event
        await publisher.publish_chatbot_name_updated(
            chatbot_id=chatbot_id,
            chatbot_name=chatbot_name,
            tenant_id=tenant_id
        )
        
        # Get the call arguments
        call_args = mock_rabbitmq.publish_message.call_args
        
        # Verify exchange
        assert call_args[1]['exchange'] == 'ex.whatsappChatbot'
        
        # Verify routing key
        assert call_args[1]['routing_key'] == 'whatsapp.chatbot.name.updated'
        
        # Verify payload structure
        message_body = json.loads(call_args[1]['message'])
        assert 'id' in message_body
        assert 'name' in message_body
        assert 'tenantId' in message_body
        
        # Verify payload values
        assert message_body['id'] == chatbot_id
        assert message_body['name'] == chatbot_name
        assert message_body['tenantId'] == tenant_id
        
        # Verify no extra fields
        assert len(message_body) == 3, f"Expected exactly 3 fields, got {len(message_body)}: {list(message_body.keys())}"


@pytest.mark.asyncio
async def test_publish_chatbot_name_updated_with_special_characters():
    """Test chatbot name update with special characters in name"""
    # Initialize publisher
    publisher = ChatbotEventPublisher()
    
    # Test data with special characters
    chatbot_id = "test-id-123"
    chatbot_name = "Chatbot with Special Chars: @#$%^&*()_+-=[]{}|;':\",./<>?"
    tenant_id = 456
    
    # Mock RabbitMQ service
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(return_value=True)
        
        # Test publishing chatbot name updated event
        result = await publisher.publish_chatbot_name_updated(
            chatbot_id=chatbot_id,
            chatbot_name=chatbot_name,
            tenant_id=tenant_id
        )
        
        # Assertions
        assert result is True, "Publisher should handle special characters"
        
        # Verify the message can be parsed as JSON
        call_args = mock_rabbitmq.publish_message.call_args
        message_body = json.loads(call_args[1]['message'])
        assert message_body['name'] == chatbot_name


@pytest.mark.asyncio
async def test_publish_chatbot_name_updated_with_unicode():
    """Test chatbot name update with unicode characters"""
    # Initialize publisher
    publisher = ChatbotEventPublisher()
    
    # Test data with unicode characters
    chatbot_id = "test-unicode-123"
    chatbot_name = "Chatbot with Unicode: 你好世界 🌍 émojis 🚀"
    tenant_id = 789
    
    # Mock RabbitMQ service
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(return_value=True)
        
        # Test publishing chatbot name updated event
        result = await publisher.publish_chatbot_name_updated(
            chatbot_id=chatbot_id,
            chatbot_name=chatbot_name,
            tenant_id=tenant_id
        )
        
        # Assertions
        assert result is True, "Publisher should handle unicode characters"
        
        # Verify the message can be parsed as JSON
        call_args = mock_rabbitmq.publish_message.call_args
        message_body = json.loads(call_args[1]['message'])
        assert message_body['name'] == chatbot_name


@pytest.mark.asyncio
async def test_publish_chatbot_name_updated_with_empty_name():
    """Test chatbot name update with empty name"""
    # Initialize publisher
    publisher = ChatbotEventPublisher()
    
    # Test data with empty name
    chatbot_id = "test-empty-name"
    chatbot_name = ""
    tenant_id = 999
    
    # Mock RabbitMQ service
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(return_value=True)
        
        # Test publishing chatbot name updated event
        result = await publisher.publish_chatbot_name_updated(
            chatbot_id=chatbot_id,
            chatbot_name=chatbot_name,
            tenant_id=tenant_id
        )
        
        # Assertions
        assert result is True, "Publisher should handle empty name"
        
        # Verify the message payload
        call_args = mock_rabbitmq.publish_message.call_args
        message_body = json.loads(call_args[1]['message'])
        assert message_body['name'] == ""


@pytest.mark.asyncio
async def test_publish_chatbot_name_updated_with_none_values():
    """Test chatbot name update with None values (should not happen in practice)"""
    # Initialize publisher
    publisher = ChatbotEventPublisher()
    
    # Mock RabbitMQ service
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(return_value=True)
        
        # Test publishing chatbot name updated event with None values
        result = await publisher.publish_chatbot_name_updated(
            chatbot_id=None,
            chatbot_name=None,
            tenant_id=None
        )
        
        # Assertions - should still work but with None values in payload
        assert result is True, "Publisher should handle None values"
        
        # Verify the message payload contains None values
        call_args = mock_rabbitmq.publish_message.call_args
        message_body = json.loads(call_args[1]['message'])
        assert message_body['id'] is None
        assert message_body['name'] is None
        assert message_body['tenantId'] is None


@pytest.mark.asyncio
async def test_publish_chatbot_name_updated_logging():
    """Test that appropriate logging occurs during event publishing"""
    # Initialize publisher
    publisher = ChatbotEventPublisher()
    
    # Mock RabbitMQ service
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq, \
         patch('app.services.chatbot_event_publisher.logger') as mock_logger:
        
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(return_value=True)
        
        # Test publishing chatbot name updated event
        await publisher.publish_chatbot_name_updated(
            chatbot_id="test-logging",
            chatbot_name="Test Logging",
            tenant_id=123
        )
        
        # Verify success logging occurred
        mock_logger.info.assert_called()
        success_log_call = None
        for call in mock_logger.info.call_args_list:
            if "Published chatbot name updated event" in str(call):
                success_log_call = call
                break
        
        assert success_log_call is not None, "Success log message should be called"


@pytest.mark.asyncio
async def test_publish_chatbot_name_updated_failure_logging():
    """Test that appropriate error logging occurs during event publishing failure"""
    # Initialize publisher
    publisher = ChatbotEventPublisher()
    
    # Mock RabbitMQ service to raise exception
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq, \
         patch('app.services.chatbot_event_publisher.logger') as mock_logger:
        
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(side_effect=Exception("Connection failed"))
        
        # Test publishing chatbot name updated event
        await publisher.publish_chatbot_name_updated(
            chatbot_id="test-failure-logging",
            chatbot_name="Test Failure Logging",
            tenant_id=456
        )
        
        # Verify error logging occurred
        mock_logger.error.assert_called()
        error_log_call = None
        for call in mock_logger.error.call_args_list:
            if "Failed to publish chatbot name updated event" in str(call):
                error_log_call = call
                break
        
        assert error_log_call is not None, "Error log message should be called"


@pytest.mark.asyncio
async def test_publish_chatbot_deactivated_event_success():
    """Test successful publishing of chatbot deactivated event"""
    # Setup
    publisher = ChatbotEventPublisher()

    # Mock RabbitMQ service
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(return_value=True)

        # Test data
        chatbot_id = "test-chatbot-123"
        chatbot_name = "Test Chatbot"
        connected_account_id = 456
        connected_account_name = "Test Account"
        tenant_id = 789
        is_last_active = True

        # Test publishing chatbot deactivated event
        result = await publisher.publish_chatbot_deactivated_event(
            chatbot_id=chatbot_id,
            chatbot_name=chatbot_name,
            connected_account_id=connected_account_id,
            connected_account_name=connected_account_name,
            tenant_id=tenant_id,
            is_last_active=is_last_active
        )

        # Assertions
        assert result is True

        # Verify the message was published with correct parameters
        mock_rabbitmq.publish_message.assert_called_once()
        call_args = mock_rabbitmq.publish_message.call_args

        # Check exchange and routing key
        assert call_args[1]['exchange'] == "ex.whatsappChatbot"
        assert call_args[1]['routing_key'] == "chatbot.deactivated"

        # Verify the message payload
        message_body = json.loads(call_args[1]['message'])
        expected_payload = {
            "chatbotId": chatbot_id,
            "chatbotName": chatbot_name,
            "status": "INACTIVE",
            "connectedAccount": {
                "id": connected_account_id,
                "name": connected_account_name
            },
            "tenantId": tenant_id,
            "isLastActive": is_last_active
        }

        assert message_body == expected_payload


@pytest.mark.asyncio
async def test_publish_chatbot_deactivated_event_not_last_active():
    """Test publishing chatbot deactivated event when not last active"""
    # Setup
    publisher = ChatbotEventPublisher()

    # Mock RabbitMQ service
    with patch('app.services.chatbot_event_publisher.rabbitmq_service') as mock_rabbitmq:
        mock_rabbitmq.connection = MagicMock()
        mock_rabbitmq.connection.is_closed = False
        mock_rabbitmq.channel = MagicMock()
        mock_rabbitmq.channel.is_closed = False
        mock_rabbitmq.publish_message = AsyncMock(return_value=True)

        # Test data
        chatbot_id = "test-chatbot-123"
        chatbot_name = "Test Chatbot"
        connected_account_id = 456
        connected_account_name = "Test Account"
        tenant_id = 789
        is_last_active = False

        # Test publishing chatbot deactivated event
        result = await publisher.publish_chatbot_deactivated_event(
            chatbot_id=chatbot_id,
            chatbot_name=chatbot_name,
            connected_account_id=connected_account_id,
            connected_account_name=connected_account_name,
            tenant_id=tenant_id,
            is_last_active=is_last_active
        )

        # Assertions
        assert result is True

        # Verify the message payload includes isLastActive: false
        call_args = mock_rabbitmq.publish_message.call_args
        message_body = json.loads(call_args[1]['message'])
        assert message_body["isLastActive"] is False


def test_get_deactivated_routing_key():
    """Test getter method for deactivated routing key"""
    publisher = ChatbotEventPublisher()
    assert publisher.get_deactivated_routing_key() == "chatbot.deactivated"
