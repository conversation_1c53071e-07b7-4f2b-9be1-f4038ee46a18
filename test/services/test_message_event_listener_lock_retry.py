"""
Test cases for Message Event Listener Lock Retry Mechanism

This module verifies that the lock retry mechanism properly handles
multiple rapid user messages without dropping them.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock, call

from app.services.message_event_listener import MessageEventListener
from app.services.redis_service import RedisService


class TestMessageEventListenerLockRetry:
    """Test cases for lock retry mechanism in message processing"""

    @pytest.fixture
    def message_listener(self):
        """Create a MessageEventListener instance for testing"""
        return MessageEventListener()

    @pytest.fixture
    def mock_redis_service(self):
        """Create a mock RedisService"""
        return MagicMock(spec=RedisService)

    @pytest.mark.asyncio
    async def test_lock_acquired_on_first_attempt(self, message_listener, mock_redis_service):
        """Test that processing continues when lock is acquired on first attempt"""
        
        conversation_id = "test-conv-123"
        user_message = "Hello"
        
        # Mock Redis to succeed on first attempt
        mock_redis_service.acquire_lock.return_value = True
        mock_redis_service.get_conversation_state.return_value = None
        
        with patch('app.services.message_event_listener.RedisService', return_value=mock_redis_service):
            with patch('app.services.message_event_listener.get_db') as mock_db:
                mock_db.return_value = iter([MagicMock()])
                
                await message_listener._process_conversation_message(conversation_id, user_message)
                
                # Verify lock was acquired exactly once
                assert mock_redis_service.acquire_lock.call_count == 1
                mock_redis_service.acquire_lock.assert_called_with(
                    f"conversation_processing_lock:{conversation_id}",
                    timeout=30
                )
                
                # Verify state was retrieved
                mock_redis_service.get_conversation_state.assert_called_once_with(conversation_id)

    @pytest.mark.asyncio
    async def test_lock_acquired_on_retry(self, message_listener, mock_redis_service):
        """Test that processing retries and succeeds when lock becomes available"""
        
        conversation_id = "test-conv-456"
        user_message = "World"
        
        # Mock Redis to fail first time, succeed second time
        mock_redis_service.acquire_lock.side_effect = [False, True]
        mock_redis_service.get_conversation_state.return_value = None
        
        with patch('app.services.message_event_listener.RedisService', return_value=mock_redis_service):
            with patch('app.services.message_event_listener.get_db') as mock_db:
                mock_db.return_value = iter([MagicMock()])
                
                # Track if asyncio.sleep was called
                with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                    await message_listener._process_conversation_message(conversation_id, user_message)
                    
                    # Verify lock was attempted twice
                    assert mock_redis_service.acquire_lock.call_count == 2
                    
                    # Verify sleep was called with exponential backoff (0.5 seconds for first retry)
                    mock_sleep.assert_called_once()
                    assert mock_sleep.call_args[0][0] == 0.5  # First retry delay
                    
                    # Verify state was retrieved after acquiring lock
                    mock_redis_service.get_conversation_state.assert_called_once_with(conversation_id)

    @pytest.mark.asyncio
    async def test_lock_retry_with_exponential_backoff(self, message_listener, mock_redis_service):
        """Test that retries use exponential backoff timing"""
        
        conversation_id = "test-conv-789"
        user_message = "Test"
        
        # Mock Redis to fail 3 times, succeed on 4th attempt
        mock_redis_service.acquire_lock.side_effect = [False, False, False, True]
        mock_redis_service.get_conversation_state.return_value = None
        
        with patch('app.services.message_event_listener.RedisService', return_value=mock_redis_service):
            with patch('app.services.message_event_listener.get_db') as mock_db:
                mock_db.return_value = iter([MagicMock()])
                
                with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                    await message_listener._process_conversation_message(conversation_id, user_message)
                    
                    # Verify lock was attempted 4 times
                    assert mock_redis_service.acquire_lock.call_count == 4
                    
                    # Verify exponential backoff: 0.5s, 1s, 2s
                    assert mock_sleep.call_count == 3
                    sleep_delays = [call_args[0][0] for call_args in mock_sleep.call_args_list]
                    assert sleep_delays == [0.5, 1.0, 2.0]

    @pytest.mark.asyncio
    async def test_message_dropped_after_max_retries(self, message_listener, mock_redis_service):
        """Test that message is dropped after max retries (5 attempts)"""
        
        conversation_id = "test-conv-drop"
        user_message = "Dropped"
        
        # Mock Redis to always fail
        mock_redis_service.acquire_lock.return_value = False
        
        with patch('app.services.message_event_listener.RedisService', return_value=mock_redis_service):
            with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                await message_listener._process_conversation_message(conversation_id, user_message)
                
                # Verify lock was attempted max_retries (5) times
                assert mock_redis_service.acquire_lock.call_count == 5
                
                # Verify sleep was called 4 times (not on last attempt)
                assert mock_sleep.call_count == 4
                
                # Verify state was NOT retrieved (message was dropped)
                mock_redis_service.get_conversation_state.assert_not_called()

    @pytest.mark.asyncio
    async def test_concurrent_messages_processed_sequentially(self, message_listener):
        """Test that multiple concurrent messages are processed sequentially with retries"""
        
        conversation_id = "test-conv-concurrent"
        
        # Create a real Redis service mock that simulates lock behavior
        lock_held = False
        
        def acquire_lock_side_effect(key, timeout):
            nonlocal lock_held
            if not lock_held:
                lock_held = True
                return True
            return False
        
        def release_lock_side_effect(key):
            nonlocal lock_held
            lock_held = False
            return True
        
        mock_redis_service = MagicMock(spec=RedisService)
        mock_redis_service.acquire_lock.side_effect = acquire_lock_side_effect
        mock_redis_service.release_lock.side_effect = release_lock_side_effect
        mock_redis_service.get_conversation_state.return_value = None
        
        with patch('app.services.message_event_listener.RedisService', return_value=mock_redis_service):
            with patch('app.services.message_event_listener.get_db') as mock_db:
                mock_db.return_value = iter([MagicMock()])
                
                # Process two messages concurrently
                # The second should retry and wait for the first to complete
                task1 = asyncio.create_task(
                    message_listener._process_conversation_message(conversation_id, "Message 1")
                )
                
                # Small delay to ensure first message starts processing
                await asyncio.sleep(0.1)
                
                task2 = asyncio.create_task(
                    message_listener._process_conversation_message(conversation_id, "Message 2")
                )
                
                # Wait for both to complete
                await asyncio.gather(task1, task2)
                
                # Verify both attempted to acquire lock
                assert mock_redis_service.acquire_lock.call_count >= 2


def test_retry_configuration():
    """Test that retry configuration is appropriate"""
    
    # Verify retry parameters match expected values
    max_retries = 5
    base_delay = 0.5
    
    # Calculate total max wait time with exponential backoff
    total_wait = sum(base_delay * (2 ** i) for i in range(max_retries - 1))
    
    # Total wait should be: 0.5 + 1 + 2 + 4 = 7.5 seconds
    assert total_wait == 7.5, "Total retry wait time should be 7.5 seconds"
    
    # This is reasonable for handling rapid user inputs
    # Most messages will process in < 3 seconds
    print(f"✅ Retry configuration validated: {max_retries} attempts, up to {total_wait}s wait")

