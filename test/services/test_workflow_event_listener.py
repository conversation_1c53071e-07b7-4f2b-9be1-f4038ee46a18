"""
Test cases for Workflow Event Listener Service
"""

import pytest
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from sqlalchemy.orm import Session
import aio_pika

from app.services.workflow_event_listener import WorkflowEventListener
from app.models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>onversation, ChatbotNode, ChatbotQuestion
from app.services.jwt_service import JWTService


class TestWorkflowEventListener:
    """Test cases for WorkflowEventListener class"""

    @pytest.fixture
    def workflow_listener(self):
        """Create a WorkflowEventListener instance for testing"""
        return WorkflowEventListener()

    @pytest.fixture
    def mock_payload(self):
        """Create a mock workflow trigger payload"""
        return {
            "metadata": {
                "tenantId": 478,
                "userId": "794",
                "entityType": "WHATSAPP_MESSAGE",
                "workflowId": "WF_314",
                "executedWorkflows": ["WF_302", "WF_314"],
                "entityAction": "CREATED",
                "executeWorkflow": True,
                "entityId": 123,
                "workflowName": "WorkflowNo 14",
                "eventId": 1
            },
            "messageConversationId": 3390,
            "connectedAccount": {
                "id": 1,
                "name": "whatsapp"
            },
            "chatbot": {
                "id": "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                "name": "chatbot"
            },
            "entityDetails": [
                {
                    "id": 1,
                    "name": "Lead 1",
                    "entity": "lead",
                    "email": None
                },
                {
                    "id": 1,
                    "name": "deal 1",
                    "entity": "deal",
                    "email": None
                },
                {
                    "id": 1,
                    "name": "contact 1",
                    "entity": "contact",
                    "email": None
                }
            ]
        }

    @pytest.fixture
    def mock_chatbot(self):
        """Create a mock chatbot for testing"""
        chatbot = Mock(spec=Chatbot)
        chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
        chatbot.name = "Test Chatbot"
        chatbot.type = "RULE"
        chatbot.status = "ACTIVE"
        chatbot.welcome_message = "Welcome to Test Chatbot!"
        return chatbot

    @pytest.fixture
    def mock_conversation(self):
        """Create a mock conversation for testing"""
        conversation = Mock(spec=ChatbotConversation)
        conversation.id = "test-conversation-id"
        conversation.chatbot_id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
        conversation.tenant_id = 478
        conversation.user_id = "794"
        conversation.completed = False
        return conversation

    @pytest.fixture
    def mock_first_node(self):
        """Create a mock first node for rule-based chatbot"""
        node = Mock(spec=ChatbotNode)
        node.node_id = "node-1"
        node.data = {"text": "What is your name?"}
        return node

    @pytest.fixture
    def mock_first_question(self):
        """Create a mock first question for AI-based chatbot"""
        question = Mock(spec=ChatbotQuestion)
        question.id = "question-1"
        question.question = "What is your name?"
        return question

    @pytest.mark.asyncio
    async def test_handle_workflow_trigger_event_success(self, workflow_listener, mock_payload, mock_chatbot, mock_first_node):
        """Test successful workflow trigger event handling"""
        # Mock dependencies
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch('app.services.workflow_event_listener.charge_calculator') as mock_charge_calculator, \
             patch.object(workflow_listener, '_check_existing_conversation') as mock_check, \
             patch.object(workflow_listener, '_process_workflow_trigger') as mock_process:
            
            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            mock_charge_calculator.calculate_question_charge.return_value = 1
            
            # Mock existing conversation check to return None (no existing conversation)
            mock_check.return_value = None
            
            # Mock database query
            mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot
            
            # Mock message
            mock_message = Mock()
            mock_message.routing_key = "workflow.trigger.chatbot"
            mock_message.exchange = "ex.workflow"
            
            # Execute
            await workflow_listener.handle_workflow_trigger_event(mock_payload, mock_message)
            
            # Verify check was called
            mock_check.assert_called_once_with(3390, 478, mock_db)
            
            # Verify processing was called
            mock_process.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_workflow_trigger_event_missing_conversation_id(self, workflow_listener):
        """Test workflow trigger event handling with missing conversation ID"""
        payload = {
            "metadata": {"tenantId": 478},
            "chatbot": {"id": "test-chatbot-id"}
        }
        
        mock_message = Mock()
        mock_message.routing_key = "workflow.trigger.chatbot"
        mock_message.exchange = "ex.workflow"
        
        # Execute - should not raise exception but log error
        await workflow_listener.handle_workflow_trigger_event(payload, mock_message)

    @pytest.mark.asyncio
    async def test_handle_workflow_trigger_event_missing_chatbot_id(self, workflow_listener):
        """Test workflow trigger event handling with missing chatbot ID"""
        payload = {
            "metadata": {"tenantId": 478},
            "messageConversationId": 3390,
            "chatbot": {}
        }
        
        mock_message = Mock()
        mock_message.routing_key = "workflow.trigger.chatbot"
        mock_message.exchange = "ex.workflow"
        
        # Execute - should not raise exception but log error
        await workflow_listener.handle_workflow_trigger_event(payload, mock_message)

    @pytest.mark.asyncio
    async def test_check_existing_conversation_no_active_conversation(self, workflow_listener):
        """Test conversation check when no active conversation exists"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db:
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock no active conversation found for specific messageConversationId and tenant
            mock_db.query.return_value.filter.return_value.first.return_value = None

            # Execute - should return None
            result = await workflow_listener._check_existing_conversation(3390, 478, mock_db)
            assert result is None

    @pytest.mark.asyncio
    async def test_check_existing_conversation_active_conversation_exists(self, workflow_listener, mock_conversation):
        """Test conversation check when active conversation exists"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db:
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock active conversation found for specific messageConversationId and tenant
            mock_conversation.message_conversation_id = 3390
            mock_conversation.tenant_id = 478
            mock_conversation.completed = False
            mock_db.query.return_value.filter.return_value.first.return_value = mock_conversation

            # Execute - should return the conversation
            result = await workflow_listener._check_existing_conversation(3390, 478, mock_db)
            assert result == mock_conversation

    @pytest.mark.asyncio
    async def test_check_existing_conversation_different_message_conversation_id(self, workflow_listener, mock_conversation):
        """Test conversation check when active conversation exists for different messageConversationId"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db:
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock active conversation found for different messageConversationId
            mock_conversation.message_conversation_id = 1234  # Different messageConversationId
            mock_conversation.tenant_id = 478
            mock_conversation.completed = False
            mock_db.query.return_value.filter.return_value.first.return_value = None  # No conversation for 3390

            # Execute - should return None since it's a different messageConversationId
            result = await workflow_listener._check_existing_conversation(3390, 478, mock_db)
            assert result is None

    @pytest.mark.asyncio
    async def test_process_workflow_trigger_rule_based_chatbot(self, workflow_listener, mock_chatbot, mock_first_node):
        """Test workflow trigger processing for rule-based chatbot"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch('app.services.workflow_event_listener.charge_calculator') as mock_charge_calculator, \
             patch('app.services.workflow_event_listener.jwt_service') as mock_jwt_service, \
             patch.object(workflow_listener, '_process_rule_based_workflow_trigger') as mock_rule_trigger:
            
            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            mock_charge_calculator.calculate_question_charge.return_value = 1
            
            # Mock JWT service
            mock_jwt_service.build_jwt_token_for_analysis.return_value = ("test.jwt.token", 794)
            
            # Mock database queries - need to handle multiple queries properly
            mock_db.query.return_value.filter.return_value.first.side_effect = [mock_chatbot, mock_first_node]
            mock_db.add = Mock()
            mock_db.commit = Mock()
            
            # Mock Redis operations
            mock_redis.store_conversation_state = Mock()
            
            # Mock rule-based workflow trigger to succeed
            mock_rule_trigger.return_value = None
            
            # Execute
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=3390,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id="794",
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[],
                metadata={"tenantId": 478}
            )
            
            # Verify conversation state was stored
            assert mock_redis.store_conversation_state.called
            
            # Verify rule-based workflow trigger was called
            assert mock_rule_trigger.called

    @pytest.mark.asyncio
    async def test_process_workflow_trigger_chatbot_not_found(self, workflow_listener):
        """Test workflow trigger processing when chatbot is not found"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db:
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock chatbot not found
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            # Execute - should not raise exception but log error
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=3390,
                chatbot_id="non-existent-chatbot",
                tenant_id=478,
                user_id="794",
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[],
                metadata={"tenantId": 478}
            )

    @pytest.mark.asyncio
    async def test_get_first_question_rule_based(self, workflow_listener, mock_chatbot, mock_first_node):
        """Test getting first question for rule-based chatbot"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db:
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock first node query
            mock_db.query.return_value.filter.return_value.first.return_value = mock_first_node
            
            # Execute
            result = await workflow_listener._get_first_question(mock_chatbot, mock_db)
            
            # Verify result
            assert result is not None
            assert result["id"] == "node-1"
            assert result["question"] == "What is your name?"
            assert result["type"] == "rule_based"

    @pytest.mark.asyncio
    async def test_get_first_question_ai_based(self, workflow_listener, mock_first_question):
        """Test getting first question for AI-based chatbot"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db:
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Create AI-based chatbot
            ai_chatbot = Mock(spec=Chatbot)
            ai_chatbot.type = "AI"
            ai_chatbot.id = "ai-chatbot-id"
            
            # Mock first question query
            mock_db.query.return_value.filter.return_value.first.return_value = mock_first_question
            
            # Execute
            result = await workflow_listener._get_first_question(ai_chatbot, mock_db)
            
            # Verify result
            assert result is not None
            assert result["id"] == "question-1"
            assert result["question"] == "What is your name?"
            assert result["type"] == "ai_based"

    @pytest.mark.asyncio
    async def test_get_first_question_no_question_found(self, workflow_listener, mock_chatbot):
        """Test getting first question when no question is found"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db:
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock no question found
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            # Execute
            result = await workflow_listener._get_first_question(mock_chatbot, mock_db)
            
            # Verify result
            assert result is None

    @pytest.mark.asyncio
    async def test_start_workflow_listener(self, workflow_listener):
        """Test starting the workflow listener"""
        with patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq:
            mock_rabbitmq.connect = AsyncMock(return_value=True)
            mock_rabbitmq.setup_workflow_listener = AsyncMock()
            mock_rabbitmq.setup_whatsapp_chatbot_publisher = AsyncMock()
            mock_rabbitmq.register_event_handler = Mock()
            mock_rabbitmq.start_consuming = AsyncMock()
            
            # Execute
            await workflow_listener.start()
            
            # Verify setup was called
            assert workflow_listener.is_running
            mock_rabbitmq.connect.assert_called_once()
            mock_rabbitmq.setup_workflow_listener.assert_called_once()
            mock_rabbitmq.setup_whatsapp_chatbot_publisher.assert_called_once()
            mock_rabbitmq.register_event_handler.assert_called_once()
            mock_rabbitmq.start_consuming.assert_called_once()

    @pytest.mark.asyncio
    async def test_stop_workflow_listener(self, workflow_listener):
        """Test stopping the workflow listener"""
        with patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq:
            mock_rabbitmq.stop_consuming = AsyncMock()
            workflow_listener.is_running = True
            
            # Execute
            await workflow_listener.stop()
            
            # Verify stop was called
            assert not workflow_listener.is_running
            mock_rabbitmq.stop_consuming.assert_called_once_with(workflow_listener.queue_name)

    @pytest.mark.asyncio
    async def test_charge_calculation_rule_based_chatbot(self, workflow_listener, mock_chatbot, mock_first_node):
        """Test that RULE-based chatbots have charge of 0.25"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch('app.services.workflow_event_listener.charge_calculator') as mock_charge_calculator, \
             patch('app.services.workflow_event_listener.jwt_service') as mock_jwt_service, \
             patch.object(workflow_listener, '_process_rule_based_workflow_trigger') as mock_rule_trigger:
            
            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock JWT service
            mock_jwt_service.build_jwt_token_for_analysis.return_value = ("test.jwt.token", 794)
            
            # Mock database queries
            mock_db.query.return_value.filter.return_value.first.side_effect = [mock_chatbot, mock_first_node]
            mock_db.add = Mock()
            mock_db.commit = Mock()
            
            # Mock Redis operations
            mock_redis.store_conversation_state = Mock()
            
            # Mock rule-based workflow trigger to succeed
            mock_rule_trigger.return_value = None
            
            # Execute
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=3390,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id="794",
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[],
                metadata={"tenantId": 478}
            )
            
            # Verify the rule-based trigger was called
            assert mock_rule_trigger.called
            
            # Verify charge calculator was NOT called for RULE type
            mock_charge_calculator.calculate_question_charge.assert_not_called()

    @pytest.mark.asyncio
    async def test_charge_calculation_ai_based_chatbot(self, workflow_listener, mock_first_question):
        """Test that AI-based chatbots use charge calculator"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch.object(workflow_listener, 'charge_calculator') as mock_charge_calculator, \
             patch('app.services.workflow_event_listener.jwt_service') as mock_jwt_service, \
             patch.object(workflow_listener.conversation_event_publisher, 'publish_conversation_response') as mock_publish:
            
            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Create AI-based chatbot
            ai_chatbot = Mock(spec=Chatbot)
            ai_chatbot.id = "ai-chatbot-id"
            ai_chatbot.name = "AI Chatbot"
            ai_chatbot.type = "AI"
            ai_chatbot.status = "ACTIVE"
            ai_chatbot.welcome_message = "Welcome to AI Chatbot!"
            
            # Mock charge calculator to return 1 for AI chatbot
            mock_charge_calculator.calculate_question_charge.return_value = 1
            
            # Mock JWT service
            mock_jwt_service.build_jwt_token_for_analysis.return_value = ("test.jwt.token", 794)
            
            # Mock database queries
            mock_db.query.return_value.filter.return_value.first.side_effect = [ai_chatbot, mock_first_question]
            
            # Mock Redis operations
            mock_redis.store_conversation_state = Mock()
            
            # Execute
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=3390,
                chatbot_id="ai-chatbot-id",
                tenant_id=478,
                user_id="794",
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[],
                metadata={"tenantId": 478}
            )
            
            # Verify the charge was calculated via charge calculator for AI chatbot
            publish_call_args = mock_publish.call_args
            assert publish_call_args[1]['charge'] == 1
            
            # Verify charge calculator was called for AI type
            # Note: The actual implementation passes the question dict, not the mock object
            mock_charge_calculator.calculate_question_charge.assert_called_once_with(
                {'id': 'question-1', 'question': 'What is your name?', 'type': 'ai_based'},
                is_predefined=True, is_llm_generated=False
            )

    @pytest.mark.asyncio
    async def test_process_rule_based_workflow_trigger_with_sendmessage_chaining(self, workflow_listener, mock_chatbot, mock_first_node):
        """Test that RULE-based workflow trigger properly processes first sendMessage node and chains subsequent nodes"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch.object(workflow_listener, 'redis_service') as mock_redis_service, \
             patch('app.services.workflow_event_listener.ChatbotService') as mock_chatbot_service_class:

            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            mock_chatbot_service = Mock()
            mock_chatbot_service_class.return_value = mock_chatbot_service

            # Mock first node query
            mock_db.query.return_value.filter.return_value.first.return_value = mock_first_node

            # Mock Redis operations
            mock_redis_service.store_conversation_state = Mock()

            # Mock chatbot service publish_start_conversation_event to return success
            mock_chatbot_service.publish_start_conversation_event = AsyncMock(return_value=True)

            # Prepare test data
            conversation_state = {
                "conversation_id": "test-conversation-id",
                "chatbot_id": "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                "tenant_id": 478,
                "user_id": "794",
                "message_conversation_id": 3390,
                "connected_account": {"id": 1, "name": "whatsapp"},
                "entity_details": [],
                "workflow_metadata": {"tenantId": 478},
                "current_node": None,
                "conversation_turns": [],
                "current_charge": 0,
                "total_charge": 0,
                "completed": False,
                "started_at": None,
                "last_activity": None
            }

            # Execute
            await workflow_listener._process_rule_based_workflow_trigger(
                chatbot=mock_chatbot,
                conversation_id="test-conversation-id",
                conversation_state=conversation_state,
                tenant_id=478,
                message_conversation_id=3390,
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[],
                db=mock_db
            )

            # Verify conversation state was updated with rule_current_node_id
            assert conversation_state["rule_current_node_id"] == "node-1"
            assert conversation_state["current_charge"] == 0.25
            assert conversation_state["total_charge"] == 0.25

            # Verify conversation state was stored
            mock_redis_service.store_conversation_state.assert_called_with("test-conversation-id", conversation_state)

            # Verify the test executed without exceptions (ChatbotService was called)
            # Note: We verify that the conversation state was properly updated and stored
            # The chatbot service call may fail due to missing test infrastructure (RabbitMQ, etc.)
            # but the key is that the _process_rule_based_workflow_trigger method executed successfully


class TestWorkflowEventListenerJWTIntegration:
    """Test cases for JWT integration in Workflow Event Listener"""

    @pytest.fixture
    def workflow_listener(self):
        """Create a WorkflowEventListener instance for testing"""
        return WorkflowEventListener()

    @pytest.fixture
    def mock_jwt_payload(self):
        """Create a mock JWT workflow trigger payload"""
        return {
            "metadata": {
                "tenantId": 478,
                "userId": 794,
                "entityType": "WHATSAPP_MESSAGE",
                "workflowId": "WF_314",
                "executedWorkflows": ["WF_302", "WF_314"],
                "entityAction": "CREATED",
                "executeWorkflow": True,
                "entityId": 123,
                "workflowName": "WorkflowNo 14",
                "eventId": 1
            },
            "messageConversationId": 123,
            "connectedAccount": {
                "id": 1,
                "name": "whatsapp"
            },
            "chatbot": {
                "id": "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                "name": "chatbot"
            },
            "entityDetails": [
                {
                    "id": 1,
                    "name": "Lead 1",
                    "entity": "lead",
                    "email": None
                }
            ]
        }

    @pytest.fixture
    def mock_iam_response(self):
        """Mock IAM service response"""
        return {
            "id": "actual_user_794",
            "firstName": "Test",
            "lastName": "User",
            "tenantId": "478",
            "email": "<EMAIL>",
            "permissions": [
                {
                    "id": 1,
                    "name": "user",
                    "description": "has access to user resource",
                    "action": {
                        "read": True, "readAll": True, "write": True,
                        "update": True, "updateAll": True
                    }
                }
            ]
        }

    @pytest.mark.asyncio
    async def test_jwt_token_creation_in_workflow_processing(self, workflow_listener, mock_jwt_payload, mock_iam_response):
        """Test that JWT token is created during workflow processing"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('app.services.workflow_event_listener.jwt_service') as mock_jwt_service, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch.object(workflow_listener, '_process_rule_based_workflow_trigger') as mock_rule_trigger:

            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])

            # Mock JWT service
            mock_jwt_service.build_jwt_token_for_analysis.return_value = ("test.jwt.token", 794)

            # Create chatbot mock
            chatbot = Mock(spec=Chatbot)
            chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            chatbot.name = "Test Chatbot"
            chatbot.type = "RULE"
            chatbot.status = "ACTIVE"
            chatbot.welcome_message = "Welcome!"

            # Mock first question
            first_question = Mock()
            first_question.id = "question-1"
            first_question.data = {"text": "First question"}

            # Mock database queries
            mock_db.query.return_value.filter.return_value.first.side_effect = [chatbot, first_question]
            mock_db.add = Mock()
            mock_db.commit = Mock()

            # Mock Redis operations
            mock_redis.store_conversation_state = Mock()
            
            # Mock rule-based workflow trigger to succeed
            mock_rule_trigger.return_value = None

            # Execute workflow processing
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id=794,
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[{"id": 1, "name": "Lead 1", "entity": "lead"}],
                metadata=mock_jwt_payload["metadata"]
            )

            # Verify JWT service was called with correct parameters
            mock_jwt_service.build_jwt_token_for_analysis.assert_called_once_with("794", "478")

            # Verify conversation state was stored with JWT token
            stored_state_call = mock_redis.store_conversation_state.call_args
            stored_state = stored_state_call[0][1]  # Second argument is the state

            assert stored_state["jwt_token"] == "test.jwt.token"
            assert stored_state["user_id"] == 794  # Should use actual user ID (long integer)
            assert stored_state["original_user_id"] == 794  # Should preserve original user ID
            assert stored_state["workflow_metadata"] == mock_jwt_payload["metadata"]
            
            # Verify rule-based trigger was called
            assert mock_rule_trigger.called

    @pytest.mark.asyncio
    async def test_jwt_token_creation_failure_does_not_break_workflow(self, workflow_listener, mock_jwt_payload):
        """Test that JWT token creation failure doesn't break the workflow"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('app.services.workflow_event_listener.jwt_service') as mock_jwt_service, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch.object(workflow_listener, '_process_rule_based_workflow_trigger') as mock_rule_trigger:

            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])

            # Mock JWT service to raise exception
            mock_jwt_service.build_jwt_token_for_analysis.side_effect = Exception("IAM service unavailable")

            # Create chatbot mock
            chatbot = Mock(spec=Chatbot)
            chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            chatbot.name = "Test Chatbot"
            chatbot.type = "RULE"
            chatbot.status = "ACTIVE"
            chatbot.welcome_message = "Welcome!"

            # Mock first question
            first_question = Mock()
            first_question.id = "question-1"
            first_question.data = {"text": "First question"}

            # Mock database queries
            mock_db.query.return_value.filter.return_value.first.side_effect = [chatbot, first_question]
            mock_db.add = Mock()
            mock_db.commit = Mock()

            # Mock Redis operations
            mock_redis.store_conversation_state = Mock()
            
            # Mock rule-based workflow trigger to succeed
            mock_rule_trigger.return_value = None

            # Execute workflow processing - should not raise exception
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id=794,
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[{"id": 1, "name": "Lead 1", "entity": "lead"}],
                metadata=mock_jwt_payload["metadata"]
            )

            # Verify workflow continues despite JWT failure
            stored_state_call = mock_redis.store_conversation_state.call_args
            stored_state = stored_state_call[0][1]

            assert stored_state["jwt_token"] is None  # JWT token should be None
            assert stored_state["user_id"] == 794  # Should use original user ID
            assert stored_state["workflow_metadata"] == mock_jwt_payload["metadata"]

            # Verify rule-based trigger was called
            assert mock_rule_trigger.called

    @pytest.mark.asyncio
    async def test_missing_user_id_or_tenant_id_skips_jwt_creation(self, workflow_listener, mock_jwt_payload):
        """Test that missing user_id or tenant_id skips JWT token creation"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('app.services.workflow_event_listener.jwt_service') as mock_jwt_service, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch.object(workflow_listener.conversation_event_publisher, 'publish_conversation_response') as mock_publish:

            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])

            # Create chatbot mock
            chatbot = Mock(spec=Chatbot)
            chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            chatbot.name = "Test Chatbot"
            chatbot.type = "RULE"
            chatbot.status = "ACTIVE"
            chatbot.welcome_message = "Welcome!"

            # Mock first question
            first_question = Mock()
            first_question.id = "question-1"
            first_question.data = {"text": "First question"}

            # Mock database queries
            mock_db.query.return_value.filter.return_value.first.side_effect = [chatbot, first_question]

            # Mock Redis operations
            mock_redis.store_conversation_state = Mock()

            # Test with missing user_id
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id=None,  # Missing user_id
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[{"id": 1, "name": "Lead 1", "entity": "lead"}],
                metadata=mock_jwt_payload["metadata"]
            )

            # Verify JWT service was not called
            mock_jwt_service.build_jwt_token_for_analysis.assert_not_called()

            # Verify conversation state has no JWT token
            stored_state_call = mock_redis.store_conversation_state.call_args
            stored_state = stored_state_call[0][1]
            assert stored_state["jwt_token"] is None

    @pytest.mark.asyncio
    async def test_conversation_state_includes_all_jwt_related_fields(self, workflow_listener, mock_jwt_payload):
        """Test that conversation state includes all JWT-related fields"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('app.services.workflow_event_listener.jwt_service') as mock_jwt_service, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch.object(workflow_listener.conversation_event_publisher, 'publish_conversation_response'):

            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])

            # Mock JWT service
            mock_jwt_service.build_jwt_token_for_analysis.return_value = ("test.jwt.token", 794)

            # Create chatbot mock
            chatbot = Mock(spec=Chatbot)
            chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            chatbot.name = "Test Chatbot"
            chatbot.type = "RULE"
            chatbot.status = "ACTIVE"
            chatbot.welcome_message = "Welcome!"

            # Mock first question
            first_question = Mock()
            first_question.id = "question-1"
            first_question.data = {"text": "First question"}

            # Mock database queries
            mock_db.query.return_value.filter.return_value.first.side_effect = [chatbot, first_question]

            # Mock Redis operations
            mock_redis.store_conversation_state = Mock()

            # Execute workflow processing
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id=794,
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[{"id": 1, "name": "Lead 1", "entity": "lead"}],
                metadata=mock_jwt_payload["metadata"]
            )

            # Verify conversation state structure
            stored_state_call = mock_redis.store_conversation_state.call_args
            stored_state = stored_state_call[0][1]

            # Check all JWT-related fields are present
            required_fields = [
                "conversation_id", "chatbot_id", "tenant_id", "user_id", "original_user_id",
                "message_conversation_id", "connected_account", "entity_details",
                "workflow_metadata", "jwt_token"
            ]

            for field in required_fields:
                assert field in stored_state, f"Required field '{field}' missing from conversation state"

            # Verify specific JWT-related values
            assert stored_state["jwt_token"] == "test.jwt.token"
            assert stored_state["user_id"] == 794
            assert stored_state["original_user_id"] == 794
            assert stored_state["workflow_metadata"] == mock_jwt_payload["metadata"]
            assert stored_state["connected_account"] == {"id": 1, "name": "whatsapp"}
            assert stored_state["entity_details"] == [{"id": 1, "name": "Lead 1", "entity": "lead"}]

    @pytest.mark.asyncio
    async def test_database_conversation_record_uses_actual_user_id(self, workflow_listener, mock_jwt_payload):
        """Test that database conversation record uses actual user ID from IAM"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('app.services.workflow_event_listener.jwt_service') as mock_jwt_service, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch.object(workflow_listener.conversation_event_publisher, 'publish_conversation_response'):

            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])

            # Mock JWT service
            mock_jwt_service.build_jwt_token_for_analysis.return_value = ("test.jwt.token", 794)

            # Create chatbot mock
            chatbot = Mock(spec=Chatbot)
            chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            chatbot.name = "Test Chatbot"
            chatbot.type = "RULE"
            chatbot.status = "ACTIVE"
            chatbot.welcome_message = "Welcome!"

            # Mock first question
            first_question = Mock()
            first_question.id = "question-1"
            first_question.data = {"text": "First question"}

            # Mock database queries
            mock_db.query.return_value.filter.return_value.first.side_effect = [chatbot, first_question]

            # Mock Redis operations
            mock_redis.store_conversation_state = Mock()

            # Execute workflow processing
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id=794,
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[{"id": 1, "name": "Lead 1", "entity": "lead"}],
                metadata=mock_jwt_payload["metadata"]
            )

            # Verify database conversation record was created with actual user ID
            db_add_call = mock_db.add.call_args
            conversation_record = db_add_call[0][0]

            assert conversation_record.user_id == "794"  # Should use actual user ID from IAM (converted to string for DB)
            assert conversation_record.tenant_id == 478
            assert conversation_record.message_conversation_id == 123


class TestWorkflowEventPublishing:
    """Test cases for workflow event publishing functionality"""

    @pytest.fixture
    def workflow_listener(self):
        """Create a WorkflowEventListener instance for testing"""
        return WorkflowEventListener()

    @pytest.fixture
    def mock_payload_with_reply_headers(self):
        """Create a mock workflow trigger payload with reply headers"""
        return {
            "metadata": {
                "tenantId": 478,
                "userId": "794",
                "eventId": 80
            },
            "messageConversationId": 3390,
            "connectedAccount": {
                "id": 1,
                "name": "whatsapp"
            },
            "chatbot": {
                "id": "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                "name": "chatbot"
            },
            "entityDetails": [
                {
                    "id": 1,
                    "name": "Lead 1",
                    "entity": "lead",
                    "email": None
                }
            ]
        }

    @pytest.fixture
    def mock_message_with_reply_headers(self):
        """Create a mock message with reply headers"""
        mock_message = Mock()
        mock_message.routing_key = "workflow.trigger.chatbot"
        mock_message.exchange = "ex.workflow"
        mock_message.headers = {
            "replyToExchange": "ex.workflow.response",
            "replyToEvent": "workflow.response.chatbot"
        }
        mock_message.channel = Mock()
        return mock_message

    @pytest.mark.asyncio
    async def test_reply_headers_extraction_and_storage(self, workflow_listener, mock_payload_with_reply_headers, mock_message_with_reply_headers):
        """Test that reply headers are extracted and stored in conversation state"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch('app.services.workflow_event_listener.charge_calculator') as mock_charge_calculator, \
             patch.object(workflow_listener, '_check_existing_conversation') as mock_check, \
             patch.object(workflow_listener, '_process_workflow_trigger') as mock_process:
            
            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            mock_charge_calculator.calculate_question_charge.return_value = 1
            
            # Mock existing conversation check to return None (no existing conversation)
            mock_check.return_value = None
            
            # Mock database query
            mock_chatbot = Mock(spec=Chatbot)
            mock_chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            mock_chatbot.name = "Test Chatbot"
            mock_chatbot.type = "RULE"
            mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot
            
            # Execute
            await workflow_listener.handle_workflow_trigger_event(mock_payload_with_reply_headers, mock_message_with_reply_headers)
            
            # Verify check was called
            mock_check.assert_called_once_with(3390, 478, mock_db)
            
            # Verify processing was called with reply headers
            mock_process.assert_called_once()
            call_args = mock_process.call_args
            reply_headers = call_args[1]['reply_headers']
            
            assert reply_headers is not None
            assert reply_headers["replyToExchange"] == "ex.workflow.response"
            assert reply_headers["replyToEvent"] == "workflow.response.chatbot"
            assert reply_headers["eventId"] == 80

    @pytest.mark.asyncio
    async def test_workflow_trigger_without_reply_headers(self, workflow_listener, mock_payload_with_reply_headers):
        """Test workflow trigger when no reply headers are present"""
        mock_message = Mock()
        mock_message.routing_key = "workflow.trigger.chatbot"
        mock_message.exchange = "ex.workflow"
        mock_message.headers = {}  # No reply headers
        mock_message.channel = Mock()
        
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch('app.services.workflow_event_listener.charge_calculator') as mock_charge_calculator, \
             patch.object(workflow_listener, '_check_existing_conversation') as mock_check, \
             patch.object(workflow_listener, '_process_workflow_trigger') as mock_process:
            
            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            mock_charge_calculator.calculate_question_charge.return_value = 1
            
            # Mock existing conversation check to return None (no existing conversation)
            mock_check.return_value = None
            
            # Mock database query
            mock_chatbot = Mock(spec=Chatbot)
            mock_chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            mock_chatbot.name = "Test Chatbot"
            mock_chatbot.type = "RULE"
            mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot
            
            # Execute
            await workflow_listener.handle_workflow_trigger_event(mock_payload_with_reply_headers, mock_message)
            
        # Verify processing was called with reply headers containing None values
        mock_process.assert_called_once()
        call_args = mock_process.call_args
        reply_headers = call_args[1]['reply_headers']

        assert reply_headers is not None
        assert reply_headers['replyToExchange'] is None
        assert reply_headers['replyToEvent'] is None
        assert reply_headers['eventId'] == 80

    @pytest.mark.asyncio
    async def test_publish_workflow_response_success(self, workflow_listener):
        """Test publishing workflow success response"""
        mock_channel = Mock()
        mock_exchange = Mock()
        mock_channel.declare_exchange = AsyncMock(return_value=mock_exchange)
        mock_exchange.publish = AsyncMock()
        
        # Execute
        await workflow_listener._publish_workflow_response(
            status="SUCCESS",
            status_code=200,
            entity_id=3390,
            channel=mock_channel,
            reply_to_exchange="ex.workflow.response",
            reply_to_event="workflow.response.chatbot",
            event_id=80,
            execution_details={"conversationId": "test-conversation-id"}
        )
        
        # Verify exchange was declared
        mock_channel.declare_exchange.assert_called_once_with(
            "ex.workflow.response", 
            aio_pika.ExchangeType.TOPIC, 
            durable=True
        )
        
        # Verify message was published
        mock_exchange.publish.assert_called_once()
        publish_call = mock_exchange.publish.call_args
        message = publish_call[0][0]
        
        # Verify message content
        assert message.content_type == "application/json"
        assert message.delivery_mode == aio_pika.DeliveryMode.PERSISTENT
        
        # Verify message body
        message_data = json.loads(message.body.decode())
        assert message_data["status"] == "SUCCESS"
        assert message_data["statusCode"] == 200
        assert message_data["entityId"] == 3390
        assert message_data["eventId"] == 80
        assert message_data["executionDetails"]["conversationId"] == "test-conversation-id"

    @pytest.mark.asyncio
    async def test_publish_workflow_response_failure(self, workflow_listener):
        """Test publishing workflow failure response"""
        mock_channel = Mock()
        mock_exchange = Mock()
        mock_channel.declare_exchange = AsyncMock(return_value=mock_exchange)
        mock_exchange.publish = AsyncMock()
        
        # Execute
        await workflow_listener._publish_workflow_response(
            status="FAILED",
            status_code=500,
            entity_id=3390,
            channel=mock_channel,
            reply_to_exchange="ex.workflow.response",
            reply_to_event="workflow.response.chatbot",
            event_id=80,
            error_code="041200",
            error_message="Internal server error"
        )
        
        # Verify exchange was declared
        mock_channel.declare_exchange.assert_called_once()
        
        # Verify message was published
        mock_exchange.publish.assert_called_once()
        publish_call = mock_exchange.publish.call_args
        message = publish_call[0][0]
        
        # Verify message content
        message_data = json.loads(message.body.decode())
        assert message_data["status"] == "FAILED"
        assert message_data["statusCode"] == 500
        assert message_data["entityId"] == 3390
        assert message_data["errorCode"] == "041200"
        assert message_data["errorMessage"] == "Internal server error"

    @pytest.mark.asyncio
    async def test_publish_workflow_completion_success(self, workflow_listener):
        """Test publishing workflow completion success event"""
        reply_headers = {
            "replyToExchange": "ex.workflow.response",
            "replyToEvent": "workflow.response.chatbot",
            "eventId": 80
        }
        
        with patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq:
            mock_rabbitmq.is_healthy = AsyncMock(return_value=True)
            mock_channel = Mock()
            mock_exchange = Mock()
            mock_exchange.publish = AsyncMock()
            mock_channel.declare_exchange = AsyncMock(return_value=mock_exchange)
            mock_rabbitmq.get_channel = AsyncMock(return_value=mock_channel)
            
            with patch.object(workflow_listener, '_publish_workflow_response') as mock_publish:
                # Execute
                await workflow_listener.publish_workflow_completion_success(
                    conversation_id="test-conversation-id",
                    message_conversation_id=3390,
                    chatbot_id="test-chatbot-id",
                    tenant_id=478,
                    reply_headers=reply_headers,
                    execution_details={"entitiesUpdated": 5}
                )
                
                # Verify publish was called
                mock_publish.assert_called_once()
                call_args = mock_publish.call_args[1]
                
                assert call_args["status"] == "SUCCESS"
                assert call_args["status_code"] == 200
                assert call_args["entity_id"] == 3390
                assert call_args["reply_to_exchange"] == "ex.workflow.response"
                assert call_args["reply_to_event"] == "workflow.response.chatbot"
                assert call_args["event_id"] == 80
                assert call_args["execution_details"]["conversationId"] == "test-conversation-id"
                assert call_args["execution_details"]["entitiesUpdated"] == 5

    @pytest.mark.asyncio
    async def test_publish_workflow_completion_success_no_reply_headers(self, workflow_listener):
        """Test publishing workflow completion success when no reply headers"""
        with patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq:
            # Execute with None reply headers
            await workflow_listener.publish_workflow_completion_success(
                conversation_id="test-conversation-id",
                message_conversation_id=3390,
                chatbot_id="test-chatbot-id",
                tenant_id=478,
                reply_headers=None,
                execution_details={"entitiesUpdated": 5}
            )
            
            # Verify RabbitMQ was not called
            mock_rabbitmq.is_healthy.assert_not_called()

    @pytest.mark.asyncio
    async def test_publish_workflow_completion_success_rabbitmq_not_connected(self, workflow_listener):
        """Test publishing workflow completion success when RabbitMQ is not connected"""
        reply_headers = {
            "replyToExchange": "ex.workflow.response",
            "replyToEvent": "workflow.response.chatbot",
            "eventId": 80
        }
        
        with patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq:
            mock_rabbitmq.is_healthy = AsyncMock(return_value=False)
            
            # Execute
            await workflow_listener.publish_workflow_completion_success(
                conversation_id="test-conversation-id",
                message_conversation_id=3390,
                chatbot_id="test-chatbot-id",
                tenant_id=478,
                reply_headers=reply_headers,
                execution_details={"entitiesUpdated": 5}
            )
            
            # Verify get_channel was not called
            mock_rabbitmq.get_channel.assert_not_called()

    @pytest.mark.asyncio
    async def test_publish_workflow_completion_failure(self, workflow_listener):
        """Test publishing workflow completion failure event"""
        reply_headers = {
            "replyToExchange": "ex.workflow.response",
            "replyToEvent": "workflow.response.chatbot",
            "eventId": 80
        }
        
        with patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq:
            mock_rabbitmq.is_healthy = AsyncMock(return_value=True)
            mock_channel = Mock()
            mock_exchange = Mock()
            mock_exchange.publish = AsyncMock()
            mock_channel.declare_exchange = AsyncMock(return_value=mock_exchange)
            mock_rabbitmq.get_channel = AsyncMock(return_value=mock_channel)
            
            with patch.object(workflow_listener, '_publish_workflow_response') as mock_publish:
                # Execute
                await workflow_listener.publish_workflow_completion_failure(
                    conversation_id="test-conversation-id",
                    message_conversation_id=3390,
                    error_code="041202",
                    error_message="Conversation timed out",
                    reply_headers=reply_headers
                )
                
                # Verify publish was called
                mock_publish.assert_called_once()
                call_args = mock_publish.call_args[1]
                
                assert call_args["status"] == "FAILED"
                assert call_args["status_code"] == 500
                assert call_args["entity_id"] == 3390
                assert call_args["reply_to_exchange"] == "ex.workflow.response"
                assert call_args["reply_to_event"] == "workflow.response.chatbot"
                assert call_args["event_id"] == 80
                assert call_args["error_code"] == "041202"
                assert call_args["error_message"] == "Conversation timed out"

    @pytest.mark.asyncio
    async def test_publish_workflow_completion_failure_no_reply_headers(self, workflow_listener):
        """Test publishing workflow completion failure when no reply headers"""
        with patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq:
            # Execute with None reply headers
            await workflow_listener.publish_workflow_completion_failure(
                conversation_id="test-conversation-id",
                message_conversation_id=3390,
                error_code="041202",
                error_message="Conversation timed out",
                reply_headers=None
            )
            
            # Verify RabbitMQ was not called
            mock_rabbitmq.is_healthy.assert_not_called()

    @pytest.mark.asyncio
    async def test_publish_workflow_response_exception_handling(self, workflow_listener):
        """Test that exceptions in publish_workflow_response are handled gracefully"""
        mock_channel = Mock()
        mock_channel.declare_exchange = AsyncMock(side_effect=Exception("RabbitMQ error"))
        
        # Execute - should not raise exception
        await workflow_listener._publish_workflow_response(
            status="SUCCESS",
            status_code=200,
            entity_id=3390,
            channel=mock_channel,
            reply_to_exchange="ex.workflow.response",
            reply_to_event="workflow.response.chatbot"
        )
        
        # Verify exchange was attempted
        mock_channel.declare_exchange.assert_called_once()

    @pytest.mark.asyncio
    async def test_publish_workflow_completion_success_exception_handling(self, workflow_listener):
        """Test that exceptions in publish_workflow_completion_success are handled gracefully"""
        reply_headers = {
            "replyToExchange": "ex.workflow.response",
            "replyToEvent": "workflow.response.chatbot",
            "eventId": 80
        }
        
        with patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq:
            mock_rabbitmq.is_healthy = AsyncMock(return_value=True)
            mock_rabbitmq.get_channel = AsyncMock(side_effect=Exception("Channel error"))
            
            # Execute - should not raise exception
            await workflow_listener.publish_workflow_completion_success(
                conversation_id="test-conversation-id",
                message_conversation_id=3390,
                chatbot_id="test-chatbot-id",
                tenant_id=478,
                reply_headers=reply_headers
            )
            
            # Verify get_channel was attempted
            mock_rabbitmq.get_channel.assert_called_once()


class TestMessageEventListenerWorkflowCompletion:
    """Test cases for workflow completion event publishing in MessageEventListener"""

    @pytest.fixture
    def message_listener(self):
        """Create a MessageEventListener instance for testing"""
        from app.services.message_event_listener import MessageEventListener
        return MessageEventListener()

    @pytest.mark.asyncio
    async def test_workflow_completion_published_on_automatic_completion(self, message_listener):
        """Test that workflow completion events are published when conversations complete automatically"""
        with patch('app.services.workflow_event_listener.workflow_event_listener') as mock_workflow_listener:
            
            # Setup conversation state with workflow reply headers
            conversation_state = {
                "conversation_id": "test-conv-id",
                "chatbot_id": "test-chatbot-id",
                "tenant_id": 478,
                "message_conversation_id": 3390,
                "workflow_reply_headers": {
                    "replyToExchange": "ex.workflow.response",
                    "replyToEvent": "workflow.response.chatbot",
                    "eventId": 80
                },
                "entity_details": [
                    {"entityId": 1, "entityType": "lead"},
                    {"entityId": 2, "entityType": "contact"}
                ]
            }
            
            mock_db = Mock()
            
            # Mock the workflow completion event publishing
            mock_workflow_listener.publish_workflow_completion_success = AsyncMock()
            
            # Execute the workflow completion method
            await message_listener._publish_workflow_completion_if_needed(
                conversation_id="test-conv-id",
                state=conversation_state,
                db=mock_db
            )
            
            # Verify workflow completion event was published
            mock_workflow_listener.publish_workflow_completion_success.assert_called_once()
            
            # Verify the call arguments
            call_args = mock_workflow_listener.publish_workflow_completion_success.call_args
            assert call_args[1]['conversation_id'] == "test-conv-id"
            assert call_args[1]['message_conversation_id'] == 3390
            assert call_args[1]['chatbot_id'] == "test-chatbot-id"
            assert call_args[1]['tenant_id'] == 478
            assert call_args[1]['reply_headers'] == conversation_state["workflow_reply_headers"]
            
            # Verify execution details
            execution_details = call_args[1]['execution_details']
            assert execution_details['totalEntities'] == 2
            assert execution_details['completionType'] == "automatic"
            assert execution_details['completionMethod'] == "conversation_flow_end"

    @pytest.mark.asyncio
    async def test_workflow_completion_skipped_when_no_reply_headers(self, message_listener):
        """Test that workflow completion events are not published when no reply headers exist"""
        with patch('app.services.workflow_event_listener.workflow_event_listener') as mock_workflow_listener:
            
            # Setup conversation state WITHOUT workflow reply headers
            conversation_state = {
                "conversation_id": "test-conv-id",
                "chatbot_id": "test-chatbot-id",
                "tenant_id": 478,
                "message_conversation_id": 3390,
                # No workflow_reply_headers
                "entity_details": []
            }
            
            mock_db = Mock()
            
            # Mock the workflow completion event publishing
            mock_workflow_listener.publish_workflow_completion_success = AsyncMock()
            
            # Execute the workflow completion method
            await message_listener._publish_workflow_completion_if_needed(
                conversation_id="test-conv-id",
                state=conversation_state,
                db=mock_db
            )
            
            # Verify workflow completion event was NOT published
            mock_workflow_listener.publish_workflow_completion_success.assert_not_called()

    @pytest.mark.asyncio
    async def test_workflow_completion_with_entity_update_results(self, message_listener):
        """Test that workflow completion events include entity update results when available"""
        with patch('app.services.workflow_event_listener.workflow_event_listener') as mock_workflow_listener:
            
            # Setup conversation state with entity update results
            conversation_state = {
                "conversation_id": "test-conv-id",
                "chatbot_id": "test-chatbot-id",
                "tenant_id": 478,
                "message_conversation_id": 3390,
                "workflow_reply_headers": {
                    "replyToExchange": "ex.workflow.response",
                    "replyToEvent": "workflow.response.chatbot",
                    "eventId": 80
                },
                "entity_details": [
                    {"entityId": 1, "entityType": "lead"},
                    {"entityId": 2, "entityType": "contact"}
                ],
                "entity_update_results": {
                    "successful_updates": 1,
                    "failed_updates": 1,
                    "total_entities": 2
                }
            }
            
            mock_db = Mock()
            
            # Mock the workflow completion event publishing
            mock_workflow_listener.publish_workflow_completion_success = AsyncMock()
            
            # Execute the workflow completion method
            await message_listener._publish_workflow_completion_if_needed(
                conversation_id="test-conv-id",
                state=conversation_state,
                db=mock_db
            )
            
            # Verify workflow completion event was published
            mock_workflow_listener.publish_workflow_completion_success.assert_called_once()
            
            # Verify execution details include entity update results
            call_args = mock_workflow_listener.publish_workflow_completion_success.call_args
            execution_details = call_args[1]['execution_details']
            assert execution_details['entitiesUpdated'] == 1
            assert execution_details['entitiesFailed'] == 1
            assert execution_details['totalEntities'] == 2

    @pytest.mark.asyncio
    async def test_workflow_completion_error_handling(self, message_listener):
        """Test that workflow completion event publishing errors are handled gracefully"""
        with patch('app.services.workflow_event_listener.workflow_event_listener') as mock_workflow_listener:
            
            # Setup conversation state with workflow reply headers
            conversation_state = {
                "conversation_id": "test-conv-id",
                "chatbot_id": "test-chatbot-id",
                "tenant_id": 478,
                "message_conversation_id": 3390,
                "workflow_reply_headers": {
                    "replyToExchange": "ex.workflow.response",
                    "replyToEvent": "workflow.response.chatbot",
                    "eventId": 80
                },
                "entity_details": []
            }
            
            mock_db = Mock()
            
            # Mock the workflow completion event publishing to raise an exception
            mock_workflow_listener.publish_workflow_completion_success = AsyncMock(
                side_effect=Exception("RabbitMQ connection failed")
            )
            
            # Execute the workflow completion method - should not raise exception
            await message_listener._publish_workflow_completion_if_needed(
                conversation_id="test-conv-id",
                state=conversation_state,
                db=mock_db
            )
            
            # Verify workflow completion event was attempted
            mock_workflow_listener.publish_workflow_completion_success.assert_called_once()


class TestWorkflowConversationContinuation:
    """Test cases for continuing existing conversations instead of rejecting them"""

    @pytest.fixture
    def workflow_listener(self):
        """Create a WorkflowEventListener instance for testing"""
        from app.services.workflow_event_listener import WorkflowEventListener
        return WorkflowEventListener()

    @pytest.mark.asyncio
    async def test_reject_existing_conversation_with_error_response(self, workflow_listener):
        """Test that existing conversations are rejected with error response"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch.object(workflow_listener, '_publish_workflow_response') as mock_publish, \
             patch('app.services.rabbitmq_service.RabbitMQService') as mock_rabbitmq_service:

            # Mock database connection
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])

            # Mock RabbitMQ service and channel
            mock_channel = Mock()
            mock_rabbitmq_instance = Mock()
            mock_rabbitmq_instance.get_channel = AsyncMock(return_value=mock_channel)
            mock_rabbitmq_service.return_value = mock_rabbitmq_instance

            # Mock existing conversation
            mock_conversation = Mock()
            mock_conversation.id = "existing-conv-id"
            mock_conversation.tenant_id = 478
            mock_conversation.message_conversation_id = 3390
            mock_conversation.completed = False

            # Mock database query to return existing conversation
            mock_db.query.return_value.filter.return_value.first.return_value = mock_conversation

            # Setup workflow trigger payload
            payload = {
                "metadata": {"tenantId": 478, "userId": 11501},
                "messageConversationId": 3390,
                "chatbot": {"id": "test-chatbot-id"}
            }

            mock_message = Mock()
            mock_message.routing_key = "workflow.trigger.chatbot"
            mock_message.exchange = "ex.workflow"
            mock_message.headers = {
                "replyToExchange": "ex.workflow.response",
                "replyToEvent": "workflow.response.chatbot"
            }
            mock_message.channel = Mock()

            # Execute workflow trigger
            await workflow_listener.handle_workflow_trigger_event(payload, mock_message)

            # Verify that an error response was published for conversation already in progress
            mock_publish.assert_called_once()
            call_args = mock_publish.call_args
            assert call_args[1]["status"] == "FAILED"
            assert call_args[1]["status_code"] == 409
            assert call_args[1]["error_code"] == "041082"
            assert "already in progress" in call_args[1]["error_message"]

    @pytest.mark.asyncio
    async def test_start_new_conversation_when_none_exists(self, workflow_listener):
        """Test that new conversations are started when none exist"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch.object(workflow_listener, '_process_workflow_trigger') as mock_process, \
             patch.object(workflow_listener, '_publish_workflow_response') as mock_publish:
            
            # Mock database connection
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock no existing conversation
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            # Mock the process workflow trigger method
            mock_process.return_value = "new-conv-id"
            
            # Setup workflow trigger payload
            payload = {
                "metadata": {"tenantId": 478, "userId": 11501},
                "messageConversationId": 3390,
                "chatbot": {"id": "test-chatbot-id"}
            }
            
            mock_message = Mock()
            mock_message.routing_key = "workflow.trigger.chatbot"
            mock_message.exchange = "ex.workflow"
            mock_message.headers = {
                "replyToExchange": "ex.workflow.response",
                "replyToEvent": "workflow.response.chatbot"
            }
            mock_message.channel = Mock()
            
            # Execute workflow trigger
            await workflow_listener.handle_workflow_trigger_event(payload, mock_message)
            
            # Verify that process_workflow_trigger was called
            mock_process.assert_called_once()
            
            # Verify that no error response was published
            mock_publish.assert_not_called()

    @pytest.mark.asyncio
    async def test_reject_existing_conversation_with_event_id(self, workflow_listener):
        """Test that existing conversations are rejected with proper event ID in error response"""
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch.object(workflow_listener, '_publish_workflow_response') as mock_publish, \
             patch('app.services.rabbitmq_service.RabbitMQService') as mock_rabbitmq_service:
            
            # Mock database connection
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock RabbitMQ service and channel
            mock_channel = Mock()
            mock_rabbitmq_instance = Mock()
            mock_rabbitmq_instance.get_channel = AsyncMock(return_value=mock_channel)
            mock_rabbitmq_service.return_value = mock_rabbitmq_instance
            
            # Mock existing conversation
            mock_conversation = Mock()
            mock_conversation.id = "existing-conv-id"
            mock_conversation.tenant_id = 478
            mock_conversation.message_conversation_id = 3390
            mock_conversation.completed = False
            
            # Mock database query to return existing conversation
            mock_db.query.return_value.filter.return_value.first.return_value = mock_conversation
            
            # Setup workflow trigger payload
            payload = {
                "metadata": {"tenantId": 478, "userId": 11501, "eventId": 80},
                "messageConversationId": 3390,
                "chatbot": {"id": "test-chatbot-id"}
            }
            
            mock_message = Mock()
            mock_message.routing_key = "workflow.trigger.chatbot"
            mock_message.exchange = "ex.workflow"
            mock_message.headers = {
                "replyToExchange": "ex.workflow.response",
                "replyToEvent": "workflow.response.chatbot"
            }
            mock_message.channel = Mock()
            
            # Execute workflow trigger
            await workflow_listener.handle_workflow_trigger_event(payload, mock_message)
            
            # Verify that an error response was published with proper event details
            mock_publish.assert_called_once()
            call_args = mock_publish.call_args
            assert call_args[1]["status"] == "FAILED"
            assert call_args[1]["status_code"] == 409
            assert call_args[1]["error_code"] == "041082"
            assert call_args[1]["reply_to_exchange"] == "ex.workflow.response"
            assert call_args[1]["reply_to_event"] == "workflow.response.chatbot"
            assert call_args[1]["event_id"] == 80
    
    def test_message_conversation_status_new_is_processed(self):
        """Test that events with messageConversationStatus NEW are processed"""
        print("\n🧪 Testing messageConversationStatus = NEW...")
        
        status = "NEW"
        should_ignore = (status == "IN_PROGRESS")
        assert should_ignore == False
        print("✅ NEW status is processed")
    
    def test_message_conversation_status_completed_is_processed(self):
        """Test that events with messageConversationStatus COMPLETED are processed"""
        print("\n🧪 Testing messageConversationStatus = COMPLETED...")
        
        status = "COMPLETED"
        should_ignore = (status == "IN_PROGRESS")
        assert should_ignore == False
        print("✅ COMPLETED status is processed")
    
    def test_message_conversation_status_in_progress_is_ignored(self):
        """Test that events with messageConversationStatus IN_PROGRESS are ignored"""
        print("\n🧪 Testing messageConversationStatus = IN_PROGRESS...")
        
        status = "IN_PROGRESS"
        should_ignore = (status == "IN_PROGRESS")
        assert should_ignore == True
        print("✅ IN_PROGRESS status is ignored")
    
    def test_message_conversation_status_none_is_processed(self):
        """Test that events without messageConversationStatus are processed (backward compatibility)"""
        print("\n🧪 Testing no messageConversationStatus...")
        
        status = None
        should_ignore = (status == "IN_PROGRESS")
        assert should_ignore == False
        print("✅ No status is processed (backward compatible)")
    
    def test_message_conversation_status_case_sensitive(self):
        """Test that status filtering is case-sensitive"""
        print("\n🧪 Testing case sensitivity...")
        
        test_cases = [
            ("IN_PROGRESS", True),    # Ignored
            ("in_progress", False),   # Processed
            ("NEW", False),           # Processed
            ("COMPLETED", False),     # Processed
        ]
        
        for status, should_be_ignored in test_cases:
            should_ignore = (status == "IN_PROGRESS")
            assert should_ignore == should_be_ignored
            print(f"  ✓ '{status}' → ignore={should_be_ignored}")
        
        print("✅ Status filtering is case-sensitive")
    
    def test_message_conversation_status_all_combinations(self):
        """Test all messageConversationStatus combinations"""
        print("\n🧪 Testing all status combinations...")
        
        test_scenarios = [
            ("NEW", True, "Process NEW"),
            ("COMPLETED", True, "Process COMPLETED"),
            ("IN_PROGRESS", False, "Ignore IN_PROGRESS"),
            (None, True, "Process None"),
            ("", True, "Process empty"),
            ("UNKNOWN", True, "Process unknown"),
        ]
        
        for status, should_process, desc in test_scenarios:
            should_ignore = (status == "IN_PROGRESS")
            assert (not should_ignore) == should_process
            print(f"  ✓ {desc}")
        
        print("✅ All status combinations handled correctly")