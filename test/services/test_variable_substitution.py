#!/usr/bin/env python3
"""
Test script for variable substitution functionality in rule-based chatbots
"""

import json
from app.services.entity_field_service import EntityFieldService

def test_variable_substitution():
    """Test variable substitution functionality"""
    
    # Initialize service
    entity_field_service = EntityFieldService()
    
    # Test data
    field_values = {
        "1": "<PERSON>",  # firstName
        "userName": "John Doe",  # fullName
        "userEmail": "<EMAIL>",
        "docType": "PDF Document"
    }
    
    # Test text substitution
    test_text = "Hello {{1}}, here's your {{docType}} guide for {{userName}}!"
    expected_text = "Hello John, here's your PDF Document guide for <PERSON>!"
    
    result_text = entity_field_service.substitute_variables(test_text, field_values)
    print(f"Text substitution test:")
    print(f"  Input: {test_text}")
    print(f"  Expected: {expected_text}")
    print(f"  Result: {result_text}")
    print(f"  ✅ PASS" if result_text == expected_text else f"  ❌ FAIL")
    print()
    
    # Test node data substitution
    test_node_data = {
        "text": "Welcome {{1}}!",
        "options": [
            {
                "type": "text",
                "text": "Process your {{docType}}"
            },
            {
                "type": "media",
                "mediaFile": {
                    "fileId": 14,
                    "fileName": "guide.pdf",
                    "fileSize": 24576,
                    "fileType": "document",
                    "fileCaption": "Guide for {{userName}} - {{docType}}"
                }
            }
        ]
    }
    
    expected_node_data = {
        "text": "Welcome John!",
        "options": [
            {
                "type": "text",
                "text": "Process your PDF Document"
            },
            {
                "type": "media",
                "mediaFile": {
                    "fileId": 14,
                    "fileName": "guide.pdf",
                    "fileSize": 24576,
                    "fileType": "document",
                    "fileCaption": "Guide for John Doe - PDF Document"
                }
            }
        ]
    }
    
    result_node_data = entity_field_service.substitute_node_variables(test_node_data, field_values)
    print(f"Node data substitution test:")
    print(f"  Input: {json.dumps(test_node_data, indent=2)}")
    print(f"  Expected: {json.dumps(expected_node_data, indent=2)}")
    print(f"  Result: {json.dumps(result_node_data, indent=2)}")
    print(f"  ✅ PASS" if result_node_data == expected_node_data else f"  ❌ FAIL")
    print()
    
    # Test with missing variables
    test_missing = "Hello {{missingVar}}, your {{docType}} is ready!"
    expected_missing = "Hello {{missingVar}}, your PDF Document is ready!"
    
    result_missing = entity_field_service.substitute_variables(test_missing, field_values)
    print(f"Missing variable test:")
    print(f"  Input: {test_missing}")
    print(f"  Expected: {expected_missing}")
    print(f"  Result: {result_missing}")
    print(f"  ✅ PASS" if result_missing == expected_missing else f"  ❌ FAIL")
    print()
    
    # Test empty field values
    test_empty = "Hello {{1}}, your {{docType}} is ready!"
    expected_empty = "Hello {{1}}, your {{docType}} is ready!"
    
    result_empty = entity_field_service.substitute_variables(test_empty, {})
    print(f"Empty field values test:")
    print(f"  Input: {test_empty}")
    print(f"  Expected: {expected_empty}")
    print(f"  Result: {result_empty}")
    print(f"  ✅ PASS" if result_empty == expected_empty else f"  ❌ FAIL")
    print()

if __name__ == "__main__":
    print("🧪 Testing Variable Substitution Functionality")
    print("=" * 50)
    test_variable_substitution()
    print("✅ All tests completed!")
