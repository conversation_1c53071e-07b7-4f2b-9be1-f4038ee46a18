#!/usr/bin/env python3
"""
Test script for EntityFieldService with real API response format
"""

import json
from unittest.mock import Mock, patch
from app.services.entity_field_service import EntityFieldService

def test_lead_field_extraction():
    """Test lead field extraction with real API response format"""
    
    # Sample lead data from your API response
    lead_data = {
        "createdAt": "2025-03-27T10:50:15.425Z",
        "updatedAt": "2025-09-01T15:37:20.619Z",
        "createdBy": 594,
        "updatedBy": 594,
        "recordActions": {
            "read": True,
            "update": True,
            "delete": False,
            "email": True,
            "call": True,
            "sms": True,
            "task": True,
            "note": True,
            "meeting": True,
            "document": True,
            "deleteAll": True,
            "quotation": False,
            "reassign": True
        },
        "metaData": {
            "idNameStore": {
                "pipeline": {
                    "996": "Default Lead Pipeline"
                },
                "convertedBy": {
                    "594": "<PERSON>ks<PERSON>"
                },
                "updatedBy": {
                    "594": "<PERSON>ks<PERSON>"
                },
                "createdBy": {
                    "594": "<PERSON>ks<PERSON>"
                },
                "salutation": {
                    "16123": "Mr"
                },
                "ownerId": {
                    "594": "Akshay Gunshetti"
                },
                "pipelineStage": {
                    "4185": "Open"
                }
            }
        },
        "id": 31301725,
        "ownerId": 594,
        "firstName": "Akshay",
        "lastName": "Lasy Name",
        "phoneNumbers": [
            {
                "id": 39641051,
                "type": "MOBILE",
                "code": "IN",
                "value": "7878787878",
                "dialCode": "+91",
                "primary": True
            }
        ],
        "salutation": 16123,
        "pipeline": {
            "id": 996,
            "name": "Default Lead Pipeline",
            "stage": {
                "id": 4185,
                "name": "Open"
            }
        },
        "forecastingType": "OPEN",
        "country": "AL",
        "convertedAt": "2025-03-28T12:08:07.321Z",
        "convertedBy": 594,
        "latestActivityCreatedAt": "2025-09-04T07:35:59.926Z",
        "createdViaId": "594",
        "createdViaName": "User",
        "createdViaType": "Web",
        "updatedViaId": "594",
        "updatedViaName": "User",
        "updatedViaType": "Web",
        "conversionDetails": [
            {
                "entityType": "DEAL",
                "entityId": 2380945,
                "convertedAt": "2025-03-28T12:08:07.321+0000"
            },
            {
                "entityType": "CONTACT",
                "entityId": 3512242,
                "convertedAt": "2025-03-28T12:08:07.321+0000"
            }
        ],
        "customFieldValues": {
            "cfDob": "2025-03-05T18:30:00.000Z"
        },
        "score": 0.0
    }
    
    # Initialize service
    entity_field_service = EntityFieldService()
    
    # Test field extraction
    test_cases = [
        ("firstName", "Akshay"),
        ("lastName", "Lasy Name"),
        ("fullName", "Akshay Lasy Name"),
        ("phone", "7878787878"),
        ("pipeline", "Default Lead Pipeline"),
        ("stage", "Open"),
        ("owner", "Akshay Gunshetti"),
        ("country", "AL"),
        ("score", "0.0"),
        ("createdAt", "2025-03-27T10:50:15.425Z"),
        ("updatedAt", "2025-09-01T15:37:20.619Z")
    ]
    
    # Test custom field extraction separately
    print("\n🧪 Testing Custom Field Extraction")
    print("=" * 50)
    custom_field_result = entity_field_service._extract_field_value(lead_data, "cfDob", "lead")
    custom_field_expected = "2025-03-05T18:30:00.000Z"
    custom_field_status = "✅ PASS" if custom_field_result == custom_field_expected else "❌ FAIL"
    print(f"  cfDob: {custom_field_result} (expected: {custom_field_expected}) {custom_field_status}")
    
    print("🧪 Testing Lead Field Extraction")
    print("=" * 50)
    
    all_passed = True
    for field_name, expected_value in test_cases:
        result = entity_field_service._extract_field_value(lead_data, field_name, "lead")
        status = "✅ PASS" if result == expected_value else "❌ FAIL"
        print(f"  {field_name}: {result} (expected: {expected_value}) {status}")
        if result != expected_value:
            all_passed = False
    
    print(f"\nOverall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    return all_passed

def test_contact_field_extraction():
    """Test contact field extraction (similar structure to lead)"""
    
    # Sample contact data (similar to lead structure)
    contact_data = {
        "id": 3512242,
        "ownerId": 594,
        "firstName": "John",
        "lastName": "Doe",
        "phoneNumbers": [
            {
                "id": 39641052,
                "type": "MOBILE",
                "code": "US",
                "value": "5551234567",
                "dialCode": "+1",
                "primary": True
            }
        ],
        "email": "<EMAIL>",
        "company": "Acme Corp",
        "country": "US",
        "createdAt": "2025-03-27T10:50:15.425Z",
        "updatedAt": "2025-09-01T15:37:20.619Z",
        "metaData": {
            "idNameStore": {
                "ownerId": {
                    "594": "Akshay Gunshetti"
                }
            }
        }
    }
    
    entity_field_service = EntityFieldService()
    
    test_cases = [
        ("firstName", "John"),
        ("lastName", "Doe"),
        ("fullName", "John Doe"),
        ("email", "<EMAIL>"),
        ("phone", "5551234567"),
        ("company", "Acme Corp"),
        ("owner", "Akshay Gunshetti"),
        ("country", "US")
    ]
    
    print("\n🧪 Testing Contact Field Extraction")
    print("=" * 50)
    
    all_passed = True
    for field_name, expected_value in test_cases:
        result = entity_field_service._extract_field_value(contact_data, field_name, "contact")
        status = "✅ PASS" if result == expected_value else "❌ FAIL"
        print(f"  {field_name}: {result} (expected: {expected_value}) {status}")
        if result != expected_value:
            all_passed = False
    
    print(f"\nOverall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    return all_passed

def test_variable_substitution_with_real_data():
    """Test variable substitution with real field values"""
    
    entity_field_service = EntityFieldService()
    
    # Simulate field values from real API responses
    field_values = {
        "1": "Akshay",  # firstName
        "userName": "Akshay Lasy Name",  # fullName
        "userEmail": "<EMAIL>",  # email
        "userPhone": "7878787878",  # phone
        "userCompany": "Tech Corp",  # company
        "userOwner": "Akshay Gunshetti",  # owner
        "userCountry": "AL",  # country
        "leadPipeline": "Default Lead Pipeline",  # pipeline
        "leadStage": "Open",  # stage
        "leadScore": "0.0"  # score
    }
    
    # Test text substitution
    test_text = "Hello {{1}}, your {{leadPipeline}} lead is in {{leadStage}} stage. Owner: {{userOwner}}"
    expected_text = "Hello Akshay, your Default Lead Pipeline lead is in Open stage. Owner: Akshay Gunshetti"
    
    result_text = entity_field_service.substitute_variables(test_text, field_values)
    
    print("\n🧪 Testing Variable Substitution with Real Data")
    print("=" * 50)
    print(f"Input: {test_text}")
    print(f"Expected: {expected_text}")
    print(f"Result: {result_text}")
    print(f"Status: {'✅ PASS' if result_text == expected_text else '❌ FAIL'}")
    
    return result_text == expected_text

def test_api_integration_mock():
    """Test API integration with mocked responses"""
    
    entity_field_service = EntityFieldService()
    
    # Mock the API response
    mock_response = Mock()
    mock_response.json.return_value = {
        "id": 31301725,
        "firstName": "Akshay",
        "lastName": "Lasy Name",
        "phoneNumbers": [
            {
                "id": 39641051,
                "type": "MOBILE",
                "value": "7878787878",
                "primary": True
            }
        ],
        "pipeline": {
            "name": "Default Lead Pipeline",
            "stage": {
                "name": "Open"
            }
        },
        "ownerId": 594,
        "metaData": {
            "idNameStore": {
                "ownerId": {
                    "594": "Akshay Gunshetti"
                }
            }
        }
    }
    mock_response.raise_for_status.return_value = None
    
    with patch('requests.get', return_value=mock_response):
        # Test API call
        result = entity_field_service.get_entity_field_value("31301725", "lead", "firstName", "test_token")
        
        print("\n🧪 Testing API Integration (Mocked)")
        print("=" * 50)
        print(f"API Call: GET http://sd-sales/v1/leads/31301725")
        print(f"Field: firstName")
        print(f"Result: {result}")
        print(f"Expected: Akshay")
        print(f"Status: {'✅ PASS' if result == 'Akshay' else '❌ FAIL'}")
        
        return result == 'Akshay'

if __name__ == "__main__":
    print("🚀 Testing EntityFieldService with Real API Format")
    print("=" * 60)
    
    # Run all tests
    test1 = test_lead_field_extraction()
    test2 = test_contact_field_extraction()
    test3 = test_variable_substitution_with_real_data()
    test4 = test_api_integration_mock()
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Lead Field Extraction: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"Contact Field Extraction: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"Variable Substitution: {'✅ PASS' if test3 else '❌ FAIL'}")
    print(f"API Integration: {'✅ PASS' if test4 else '❌ FAIL'}")
    
    all_tests_passed = all([test1, test2, test3, test4])
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if all_tests_passed else '❌ SOME TESTS FAILED'}")
    
    if all_tests_passed:
        print("\n🎉 EntityFieldService is ready for production!")
    else:
        print("\n⚠️  Please fix failing tests before deployment.")

def test_get_entity_owner_id_success():
    """Test successful owner ID retrieval for lead"""
    
    # Sample lead data with owner ID
    lead_data = {
        "id": 12345,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "ownerId": 159,
        "phone": "+1234567890",
        "company": "Test Company"
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = lead_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_owner_id("12345", "LEAD", "test_token")
        
        assert result == 159
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert "http://sd-sales/v1/leads/12345" in call_args[0][0]
        assert call_args[1]["headers"]["Authorization"] == "Bearer test_token"
    
    print("✓ EntityFieldService.get_entity_owner_id() - LEAD success test completed")

def test_get_entity_owner_id_contact_success():
    """Test successful owner ID retrieval for contact"""
    
    # Sample contact data with owner ID
    contact_data = {
        "id": 67890,
        "firstName": "Jane",
        "lastName": "Smith",
        "email": "<EMAIL>",
        "ownerId": 200,
        "phone": "+0987654321",
        "company": "Another Company"
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = contact_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_owner_id("67890", "CONTACT", "test_token")
        
        assert result == 200
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert "http://sd-sales/v1/contacts/67890" in call_args[0][0]
        assert call_args[1]["headers"]["Authorization"] == "Bearer test_token"
    
    print("✓ EntityFieldService.get_entity_owner_id() - CONTACT success test completed")

def test_get_entity_owner_id_no_owner():
    """Test owner ID retrieval when ownerId is not present"""
    
    # Sample lead data without owner ID
    lead_data = {
        "id": 12345,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "company": "Test Company"
        # No ownerId field
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = lead_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_owner_id("12345", "LEAD", "test_token")
        
        assert result is None
    
    print("✓ EntityFieldService.get_entity_owner_id() - no owner test completed")

def test_get_entity_owner_id_unknown_entity_type():
    """Test owner ID retrieval for unknown entity type"""
    
    service = EntityFieldService()
    result = service.get_entity_owner_id("12345", "UNKNOWN", "test_token")
    
    assert result is None
    
    print("✓ EntityFieldService.get_entity_owner_id() - unknown entity type test completed")

def test_get_entity_owner_id_api_error():
    """Test owner ID retrieval when API returns error"""
    
    with patch('requests.get') as mock_get:
        # Mock API error
        mock_get.side_effect = Exception("API Error")
        
        service = EntityFieldService()
        result = service.get_entity_owner_id("12345", "LEAD", "test_token")
        
        assert result is None
    
    print("✓ EntityFieldService.get_entity_owner_id() - API error test completed")

def test_get_entity_owner_id_owner_id_none():
    """Test owner ID retrieval when ownerId is explicitly None"""
    
    # Sample lead data with ownerId explicitly set to None
    lead_data = {
        "id": 12345,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "ownerId": None,
        "phone": "+1234567890",
        "company": "Test Company"
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = lead_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_owner_id("12345", "LEAD", "test_token")
        
        assert result is None
    
    print("✓ EntityFieldService.get_entity_owner_id() - ownerId None test completed")

def test_get_entity_details_success():
    """Test successful entity details retrieval (ownerId and entityName)"""
    
    # Sample lead data with both ownerId and names
    lead_data = {
        "id": 12345,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "ownerId": 159,
        "phone": "+1234567890",
        "company": "Test Company"
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = lead_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_details("12345", "LEAD", "test_token")
        
        expected_result = {
            "ownerId": 159,
            "entityName": "John Doe"
        }
        
        assert result == expected_result
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert "http://sd-sales/v1/leads/12345" in call_args[0][0]
        assert call_args[1]["headers"]["Authorization"] == "Bearer test_token"
    
    print("✓ EntityFieldService.get_entity_details() - LEAD success test completed")

def test_get_entity_details_contact_success():
    """Test successful entity details retrieval for contact"""
    
    # Sample contact data with both ownerId and names
    contact_data = {
        "id": 67890,
        "firstName": "Jane",
        "lastName": "Smith",
        "email": "<EMAIL>",
        "ownerId": 200,
        "phone": "+0987654321",
        "company": "Another Company"
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = contact_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_details("67890", "CONTACT", "test_token")
        
        expected_result = {
            "ownerId": 200,
            "entityName": "Jane Smith"
        }
        
        assert result == expected_result
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert "http://sd-sales/v1/contacts/67890" in call_args[0][0]
        assert call_args[1]["headers"]["Authorization"] == "Bearer test_token"
    
    print("✓ EntityFieldService.get_entity_details() - CONTACT success test completed")

def test_get_entity_details_no_owner():
    """Test entity details retrieval when ownerId is not present"""
    
    # Sample lead data without owner ID
    lead_data = {
        "id": 12345,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "company": "Test Company"
        # No ownerId field
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = lead_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_details("12345", "LEAD", "test_token")
        
        expected_result = {
            "ownerId": None,
            "entityName": "John Doe"
        }
        
        assert result == expected_result
    
    print("✓ EntityFieldService.get_entity_details() - no owner test completed")

def test_get_entity_details_no_names():
    """Test entity details retrieval when names are not present"""
    
    # Sample lead data without names
    lead_data = {
        "id": 12345,
        "email": "<EMAIL>",
        "ownerId": 159,
        "phone": "+1234567890",
        "company": "Test Company"
        # No firstName/lastName fields
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = lead_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_details("12345", "LEAD", "test_token")
        
        expected_result = {
            "ownerId": 159,
            "entityName": None
        }
        
        assert result == expected_result
    
    print("✓ EntityFieldService.get_entity_details() - no names test completed")

def test_get_entity_details_empty_names():
    """Test entity details retrieval when names are empty strings"""
    
    # Sample lead data with empty names
    lead_data = {
        "id": 12345,
        "firstName": "",
        "lastName": "",
        "email": "<EMAIL>",
        "ownerId": 159,
        "phone": "+1234567890",
        "company": "Test Company"
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = lead_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_details("12345", "LEAD", "test_token")
        
        expected_result = {
            "ownerId": 159,
            "entityName": None
        }
        
        assert result == expected_result
    
    print("✓ EntityFieldService.get_entity_details() - empty names test completed")

def test_get_entity_details_unknown_entity_type():
    """Test entity details retrieval for unknown entity type"""
    
    service = EntityFieldService()
    result = service.get_entity_details("12345", "UNKNOWN", "test_token")
    
    expected_result = {
        "ownerId": None,
        "entityName": None
    }
    
    assert result == expected_result
    
    print("✓ EntityFieldService.get_entity_details() - unknown entity type test completed")

def test_get_entity_details_api_error():
    """Test entity details retrieval when API returns error"""
    
    with patch('requests.get') as mock_get:
        # Mock API error
        mock_get.side_effect = Exception("API Error")
        
        service = EntityFieldService()
        result = service.get_entity_details("12345", "LEAD", "test_token")
        
        expected_result = {
            "ownerId": None,
            "entityName": None
        }
        
        assert result == expected_result
    
    print("✓ EntityFieldService.get_entity_details() - API error test completed")

def test_get_entity_details_owner_id_none():
    """Test entity details retrieval when ownerId is explicitly None"""
    
    # Sample lead data with ownerId explicitly set to None
    lead_data = {
        "id": 12345,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "ownerId": None,
        "phone": "+1234567890",
        "company": "Test Company"
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = lead_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_details("12345", "LEAD", "test_token")
        
        expected_result = {
            "ownerId": None,
            "entityName": "John Doe"
        }
        
        assert result == expected_result
    
    print("✓ EntityFieldService.get_entity_details() - ownerId None test completed")

def test_get_entity_details_single_name():
    """Test entity details retrieval when only one name is present"""
    
    # Sample lead data with only firstName
    lead_data = {
        "id": 12345,
        "firstName": "John",
        "email": "<EMAIL>",
        "ownerId": 159,
        "phone": "+1234567890",
        "company": "Test Company"
        # No lastName field
    }
    
    with patch('requests.get') as mock_get:
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = lead_data
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        service = EntityFieldService()
        result = service.get_entity_details("12345", "LEAD", "test_token")
        
        expected_result = {
            "ownerId": 159,
            "entityName": "John"
        }
        
        assert result == expected_result
    
    print("✓ EntityFieldService.get_entity_details() - single name test completed")

if __name__ == "__main__":
    # Run existing tests
    test_lead_field_extraction()
    test_contact_field_extraction()
    test_variable_substitution_with_real_data()
    test_api_integration_mock()
    
    # Run owner ID tests
    test_get_entity_owner_id_success()
    test_get_entity_owner_id_contact_success()
    test_get_entity_owner_id_no_owner()
    test_get_entity_owner_id_unknown_entity_type()
    test_get_entity_owner_id_api_error()
    test_get_entity_owner_id_owner_id_none()
    
    # Run new entity details tests
    test_get_entity_details_success()
    test_get_entity_details_contact_success()
    test_get_entity_details_no_owner()
    test_get_entity_details_no_names()
    test_get_entity_details_empty_names()
    test_get_entity_details_unknown_entity_type()
    test_get_entity_details_api_error()
    test_get_entity_details_owner_id_none()
    test_get_entity_details_single_name()
    
    print("\n🎉 All EntityFieldService tests completed!")
