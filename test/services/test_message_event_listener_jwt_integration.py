"""
Test cases for Message Event Listener JWT and Metadata Integration

This module contains test cases for verifying that metadata and JWT tokens
flow correctly through message processing and conversation events.
"""

import pytest
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from app.services.message_event_listener import MessageEventListener


class TestMessageEventListenerJWTIntegration:
    """Test cases for JWT and metadata integration in Message Event Listener"""

    @pytest.fixture
    def message_listener(self):
        """Create a MessageEventListener instance for testing"""
        return MessageEventListener()

    @pytest.fixture
    def mock_conversation_state_with_jwt(self):
        """Create a mock conversation state with JWT and metadata"""
        return {
            "conversation_id": "test-conv-123",
            "chatbot_id": "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
            "tenant_id": 478,
            "user_id": 794,  # Actual user ID from IAM (long integer)
            "original_user_id": 794,       # Original user ID from request
            "message_conversation_id": 123,
            "connected_account": {"id": 1, "name": "whatsapp"},
            "entity_details": [
                {"id": 1, "name": "Lead 1", "entity": "lead", "email": None}
            ],
            "workflow_metadata": {
                "tenantId": 478,
                "userId": 794,
                "entityType": "WHATSAPP_MESSAGE",
                "workflowId": "WF_314",
                "executedWorkflows": ["WF_302", "WF_314"],
                "entityAction": "CREATED",
                "executeWorkflow": True,
                "entityId": 123,
                "workflowName": "WorkflowNo 14",
                "eventId": 1
            },
            "jwt_token": "test.jwt.token.with.metadata",
            "chatbotType": "RULE",
            "history": [],
            "current_node": None,
            "conversation_turns": [],
            "current_charge": 0,
            "total_charge": 0,
            "completed": False,
            "started_at": None,
            "last_activity": None
        }

    @pytest.fixture
    def mock_conversation_state_without_jwt(self):
        """Create a mock conversation state without JWT and metadata"""
        return {
            "conversation_id": "test-conv-456",
            "chatbot_id": "b1ffbc99-9c0b-4ef8-bb6d-6bb9bd380a12",
            "tenant_id": 999,
            "user_id": "simple_user_123",
            "message_conversation_id": 456,
            "chatbotType": "AI",
            "history": [],
            "current_charge": 0,
            "total_charge": 0,
            "completed": False
        }

    def test_get_event_extra_data_with_full_metadata(self, message_listener, mock_conversation_state_with_jwt):
        """Test _get_event_extra_data includes all metadata when available"""
        extra_data = message_listener._get_event_extra_data(mock_conversation_state_with_jwt)
        
        # Verify all expected fields are present
        assert extra_data["chatbotType"] == "RULE"
        assert extra_data["metadata"] == mock_conversation_state_with_jwt["workflow_metadata"]
        assert extra_data["jwtToken"] == "test.jwt.token.with.metadata"
        assert extra_data["actualUserId"] == 794
        assert extra_data["connectedAccount"] == {"id": 1, "name": "whatsapp"}
        assert extra_data["entityDetails"] == [{"id": 1, "name": "Lead 1", "entity": "lead", "email": None}]

    def test_get_event_extra_data_with_minimal_state(self, message_listener, mock_conversation_state_without_jwt):
        """Test _get_event_extra_data handles minimal conversation state"""
        extra_data = message_listener._get_event_extra_data(mock_conversation_state_without_jwt)
        
        # Verify only available fields are present
        assert extra_data["chatbotType"] == "AI"
        assert "metadata" not in extra_data  # No workflow metadata
        assert "jwtToken" not in extra_data  # No JWT token
        assert extra_data["actualUserId"] == "simple_user_123"
        assert "connectedAccount" not in extra_data  # Not in state
        assert "entityDetails" not in extra_data   # Not in state

    def test_get_event_extra_data_with_none_values(self, message_listener):
        """Test _get_event_extra_data handles None values gracefully"""
        state_with_nones = {
            "chatbotType": "RULE",
            "workflow_metadata": None,
            "jwt_token": None,
            "user_id": None,
            "connected_account": None,
            "entity_details": None
        }
        
        extra_data = message_listener._get_event_extra_data(state_with_nones)
        
        # Verify only non-None fields are included
        assert extra_data["chatbotType"] == "RULE"
        assert "metadata" not in extra_data
        assert "jwtToken" not in extra_data
        assert "actualUserId" not in extra_data
        assert "connectedAccount" not in extra_data
        assert "entityDetails" not in extra_data

    @pytest.mark.asyncio
    async def test_conversation_ended_response_includes_metadata(self, message_listener, mock_conversation_state_with_jwt):
        """Test that conversation ended responses include metadata"""
        with patch('app.services.message_event_listener.conversation_event_publisher') as mock_publisher:
            # Mock the state to indicate conversation is ended
            mock_conversation_state_with_jwt["ended"] = True
            
            # Mock the async method
            mock_publisher.publish_conversation_response = AsyncMock()
            
            # Create mock DB session
            from app.services.message_event_listener import get_db
            with patch('app.services.message_event_listener.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value = iter([mock_db])
                
                # Call the knowledge phase handler (which checks for ended state)
                await message_listener._handle_knowledge_phase(
                    mock_db,
                    mock_conversation_state_with_jwt,
                    "test message",
                    "test-conv-123",
                    "478",
                    Mock()  # Mock ElasticsearchService
                )
                
                # Verify publish_conversation_response was called with metadata
                mock_publisher.publish_conversation_response.assert_called_once()
                call_args = mock_publisher.publish_conversation_response.call_args
                
                # Check that extra data includes all JWT/metadata information
                extra_data = call_args[1]['extra']
                assert extra_data["chatbotType"] == "RULE"
                assert extra_data["metadata"] == mock_conversation_state_with_jwt["workflow_metadata"]
                assert extra_data["jwtToken"] == "test.jwt.token.with.metadata"
                assert extra_data["actualUserId"] == 794

    @pytest.mark.asyncio
    async def test_question_limit_response_includes_metadata(self, message_listener, mock_conversation_state_with_jwt):
        """Test that question limit responses include metadata"""
        with patch('app.services.message_event_listener.conversation_event_publisher') as mock_publisher, \
             patch('app.services.message_event_listener.ChatbotService') as mock_chatbot_service_class, \
             patch('app.services.message_event_listener.RedisService') as mock_redis_service_class, \
             patch('app.services.message_event_listener.update_conversation_in_db') as mock_update_db:
            
            # Mock the async method
            mock_publisher.publish_conversation_response = AsyncMock()
            
            # Mock ChatbotService to return 5+ questions (limit reached)
            mock_chatbot_service = Mock()
            mock_chatbot_service.get_conversation_questions_count.return_value = 5
            mock_chatbot_service_class.return_value = mock_chatbot_service
            
            # Mock RedisService
            mock_redis = Mock()
            mock_redis_service_class.return_value = mock_redis
            
            # Create mock DB session
            with patch('app.services.message_event_listener.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value = iter([mock_db])
                
                # Call the knowledge phase handler
                await message_listener._handle_knowledge_phase(
                    mock_db,
                    mock_conversation_state_with_jwt,
                    "test message",
                    "test-conv-123",
                    "478",
                    Mock()  # Mock ElasticsearchService
                )
                
                # Verify publish_conversation_response was called with metadata
                mock_publisher.publish_conversation_response.assert_called_once()
                call_args = mock_publisher.publish_conversation_response.call_args
                
                # Check that extra data includes all JWT/metadata information
                extra_data = call_args[1]['extra']
                assert extra_data["chatbotType"] == "RULE"
                assert extra_data["metadata"] == mock_conversation_state_with_jwt["workflow_metadata"]
                assert extra_data["jwtToken"] == "test.jwt.token.with.metadata"
                assert extra_data["actualUserId"] == 794
                assert extra_data["connectedAccount"] == {"id": 1, "name": "whatsapp"}
                assert extra_data["entityDetails"] == [{"id": 1, "name": "Lead 1", "entity": "lead", "email": None}]

    @pytest.mark.asyncio
    async def test_completion_phase_includes_metadata(self, message_listener, mock_conversation_state_with_jwt):
        """Test that completion phase responses include metadata"""
        with patch('app.services.message_event_listener.conversation_event_publisher') as mock_publisher, \
             patch('app.services.message_event_listener.RedisService') as mock_redis_service_class, \
             patch('app.services.message_event_listener.update_conversation_in_db') as mock_update_db:
            
            # Mock the async method
            mock_publisher.publish_conversation_response = AsyncMock()
            
            # Mock RedisService
            mock_redis = Mock()
            mock_redis_service_class.return_value = mock_redis
            
            # Set up state for completion
            completion_state = mock_conversation_state_with_jwt.copy()
            completion_state["completed"] = True
            completion_state["knowledgebase_id"] = None  # No knowledgebase for immediate completion
            
            # Create mock DB session
            with patch('app.services.message_event_listener.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value = iter([mock_db])
                
                # Create mock ElasticsearchService
                mock_es_service = Mock()
                mock_es_service.generate_chat_response.return_value = ("Generated response", 100, 50, "gpt-3.5-turbo")
                
                # Call the completion handler directly
                await message_listener._handle_completion_phase(
                    mock_db,
                    completion_state,
                    "test-conv-123",
                    "Conversation completed!",
                    mock_es_service
                )
                
                # Verify publish_conversation_response was called with metadata
                mock_publisher.publish_conversation_response.assert_called()
                call_args = mock_publisher.publish_conversation_response.call_args
                
                # Check that extra data includes all JWT/metadata information
                extra_data = call_args[1]['extra']
                assert extra_data["chatbotType"] == "RULE"
                assert extra_data["metadata"] == mock_conversation_state_with_jwt["workflow_metadata"]
                assert extra_data["jwtToken"] == "test.jwt.token.with.metadata"
                assert extra_data["actualUserId"] == 794

    @pytest.mark.asyncio
    async def test_generic_response_includes_metadata(self, message_listener, mock_conversation_state_with_jwt):
        """Test that generic responses (no knowledge base answers) include metadata"""
        with patch('app.services.message_event_listener.conversation_event_publisher') as mock_publisher, \
             patch('app.services.message_event_listener.ChatbotService') as mock_chatbot_service_class, \
             patch('app.services.message_event_listener.EntityFieldService') as mock_entity_service_class:
            
            # Mock the async method
            mock_publisher.publish_conversation_response = AsyncMock()
            
            # Mock ChatbotService to return < 5 questions
            mock_chatbot_service = Mock()
            mock_chatbot_service.get_conversation_questions_count.return_value = 2
            mock_chatbot_service_class.return_value = mock_chatbot_service
            
            # Mock EntityFieldService
            mock_entity_service = Mock()
            mock_entity_service_class.return_value = mock_entity_service
            
            # Set up state for knowledge phase without knowledgebase
            knowledge_state = mock_conversation_state_with_jwt.copy()
            knowledge_state["knowledgebase_id"] = None
            knowledge_state["ended"] = False
            
            # Create mock DB session
            with patch('app.services.message_event_listener.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value = iter([mock_db])
                
                # Create mock ElasticsearchService
                mock_es_service = Mock()
                
                # Call the knowledge phase handler
                await message_listener._handle_knowledge_phase(
                    mock_db,
                    knowledge_state,
                    "test question",
                    "test-conv-123",
                    "478",
                    mock_es_service
                )
                
                # Verify publish_conversation_response was called with metadata
                mock_publisher.publish_conversation_response.assert_called()
                call_args = mock_publisher.publish_conversation_response.call_args
                
                # Check that extra data includes all JWT/metadata information
                extra_data = call_args[1]['extra']
                assert extra_data["chatbotType"] == "RULE"
                assert extra_data["metadata"] == mock_conversation_state_with_jwt["workflow_metadata"]
                assert extra_data["jwtToken"] == "test.jwt.token.with.metadata"
                assert extra_data["actualUserId"] == 794

    def test_event_extra_data_backward_compatibility(self, message_listener):
        """Test that _get_event_extra_data works with old conversation states"""
        # Old style conversation state (before JWT integration)
        old_style_state = {
            "chatbotType": "AI",
            "tenant_id": 123,
            "user_id": "old_user_456"
            # Missing: workflow_metadata, jwt_token, connected_account, entity_details
        }
        
        extra_data = message_listener._get_event_extra_data(old_style_state)
        
        # Should work with minimal data
        assert extra_data["chatbotType"] == "AI"
        assert extra_data["actualUserId"] == "old_user_456"
        
        # Should not include fields that don't exist
        assert "metadata" not in extra_data
        assert "jwtToken" not in extra_data
        assert "connectedAccount" not in extra_data
        assert "entityDetails" not in extra_data

    def test_event_extra_data_with_empty_collections(self, message_listener):
        """Test _get_event_extra_data with empty lists/dicts"""
        state_with_empties = {
            "chatbotType": "RULE",
            "workflow_metadata": {},
            "connected_account": {},
            "entity_details": [],
            "user_id": "test_user",
            "jwt_token": "test.token"
        }
        
        extra_data = message_listener._get_event_extra_data(state_with_empties)
        
        # Should include fields even if they're empty
        assert extra_data["chatbotType"] == "RULE"
        assert extra_data["metadata"] == {}
        assert extra_data["connectedAccount"] == {}
        assert extra_data["entityDetails"] == []
        assert extra_data["actualUserId"] == "test_user"
        assert extra_data["jwtToken"] == "test.token"


class TestMessageEventListenerMetadataFlow:
    """Integration tests for metadata flow through message processing"""

    @pytest.fixture
    def complete_workflow_state(self):
        """Create a complete workflow state for integration testing"""
        return {
            "conversation_id": "integration-test-conv",
            "chatbot_id": "workflow-chatbot-123",
            "tenant_id": 478,
            "user_id": 794,
            "original_user_id": 794,
            "message_conversation_id": 12345,
            "connected_account": {"id": 1, "name": "whatsapp"},
            "entity_details": [
                {"id": 1, "name": "Test Lead", "entity": "lead", "email": "<EMAIL>"},
                {"id": 2, "name": "Test Deal", "entity": "deal", "email": None}
            ],
            "workflow_metadata": {
                "tenantId": 478,
                "userId": 794,
                "entityType": "WHATSAPP_MESSAGE",
                "workflowId": "WF_INTEGRATION_TEST",
                "executedWorkflows": ["WF_PREV", "WF_INTEGRATION_TEST"],
                "entityAction": "CREATED",
                "executeWorkflow": True,
                "entityId": 12345,
                "workflowName": "Integration Test Workflow",
                "eventId": 9999
            },
            "jwt_token": "integration.test.jwt.token.with.full.metadata",
            "chatbotType": "RULE",
            "history": [],
            "completed": False,
            "ended": False
        }

    @pytest.mark.asyncio
    async def test_end_to_end_metadata_flow(self, complete_workflow_state):
        """Test complete end-to-end metadata flow from conversation to event publishing"""
        message_listener = MessageEventListener()
        
        with patch('app.services.message_event_listener.conversation_event_publisher') as mock_publisher, \
             patch('app.services.message_event_listener.ChatbotService') as mock_chatbot_service_class, \
             patch('app.services.message_event_listener.EntityFieldService') as mock_entity_service_class:
            
            # Mock the async method
            mock_publisher.publish_conversation_response = AsyncMock()
            
            # Mock services
            mock_chatbot_service = Mock()
            mock_chatbot_service.get_conversation_questions_count.return_value = 2
            mock_chatbot_service_class.return_value = mock_chatbot_service
            
            mock_entity_service = Mock()
            mock_entity_service_class.return_value = mock_entity_service
            
            # Set up for knowledge phase with no knowledgebase (generic response)
            test_state = complete_workflow_state.copy()
            test_state["knowledgebase_id"] = None
            
            with patch('app.services.message_event_listener.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value = iter([mock_db])
                
                mock_es_service = Mock()
                
                # Execute the knowledge phase
                await message_listener._handle_knowledge_phase(
                    mock_db,
                    test_state,
                    "Integration test question",
                    "integration-test-conv",
                    "478",
                    mock_es_service
                )
                
                # Verify the complete metadata flow
                mock_publisher.publish_conversation_response.assert_called_once()
                call_args = mock_publisher.publish_conversation_response.call_args
                
                # Verify all conversation parameters
                assert call_args[1]['chatbot_conversation_id'] == "integration-test-conv"
                assert call_args[1]['completed'] == False
                assert call_args[1]['charge'] == 0
                
                # Verify complete extra data with all metadata
                extra_data = call_args[1]['extra']
                
                # Core fields
                assert extra_data["chatbotType"] == "RULE"
                assert extra_data["actualUserId"] == "iam_user_794"
                assert extra_data["jwtToken"] == "integration.test.jwt.token.with.full.metadata"
                
                # Workflow metadata
                expected_metadata = complete_workflow_state["workflow_metadata"]
                assert extra_data["metadata"] == expected_metadata
                assert extra_data["metadata"]["workflowId"] == "WF_INTEGRATION_TEST"
                assert extra_data["metadata"]["tenantId"] == 478
                assert extra_data["metadata"]["userId"] == 794
                
                # Account and entity details
                assert extra_data["connectedAccount"] == {"id": 1, "name": "whatsapp"}
                assert len(extra_data["entityDetails"]) == 2
                assert extra_data["entityDetails"][0]["name"] == "Test Lead"
                assert extra_data["entityDetails"][1]["name"] == "Test Deal"
