#!/usr/bin/env python3
"""
Test script to verify the user validation implementation
"""

import sys
import os
from unittest.mock import Mock, patch

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def test_user_model_import():
    """Test that the User model can be imported"""
    print("Testing User model import...")
    try:
        from app.models import User, ChatbotListResponse, UserResponse
        print("✅ User model imported successfully")
        print("✅ ChatbotListResponse model imported successfully")
        print("✅ UserResponse model imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import User model: {e}")
        return False

def test_user_service_import():
    """Test that the UserService can be imported"""
    print("\nTesting UserService import...")
    try:
        from app.services.user_service import UserService, user_service
        print("✅ UserService imported successfully")
        print("✅ Global user_service instance imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import UserService: {e}")
        return False

def test_chatbot_service_updates():
    """Test that ChatbotService has been updated with user validation"""
    print("\nTesting ChatbotService updates...")
    try:
        from app.services.chatbot_service import ChatbotService
        
        # Check if methods have the correct signatures
        service = ChatbotService()
        
        # Check create_chatbot method signature
        import inspect
        create_sig = inspect.signature(service.create_chatbot)
        expected_params = ['chatbot_data', 'tenant_id', 'user_id', 'token']
        actual_params = list(create_sig.parameters.keys())

        if actual_params == expected_params:
            print("✅ create_chatbot method has correct signature")
        else:
            print(f"❌ create_chatbot method signature mismatch. Expected: {expected_params}, Got: {actual_params}")
            return False

        # Check update_chatbot method signature
        update_sig = inspect.signature(service.update_chatbot)
        expected_params = ['chatbot_id', 'chatbot_data', 'tenant_id', 'user_id', 'token']
        actual_params = list(update_sig.parameters.keys())

        if actual_params == expected_params:
            print("✅ update_chatbot method has correct signature")
        else:
            print(f"❌ update_chatbot method signature mismatch. Expected: {expected_params}, Got: {actual_params}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Failed to test ChatbotService: {e}")
        return False

def test_user_service_methods():
    """Test UserService methods"""
    print("\nTesting UserService methods...")
    try:
        from app.services.user_service import UserService
        
        service = UserService()
        
        # Check if all required methods exist
        required_methods = [
            'get_user_from_iam',
            'get_user_by_id',
            'create_or_update_user',
            'validate_and_get_user',
            'get_users_by_ids'
        ]
        
        for method_name in required_methods:
            if hasattr(service, method_name):
                print(f"✅ {method_name} method exists")
            else:
                print(f"❌ {method_name} method missing")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Failed to test UserService methods: {e}")
        return False

def test_migration_file():
    """Test that the migration file exists and has correct content"""
    print("\nTesting migration file...")
    try:
        migration_file = "alembic/versions/20250721172206721_b2c3d4e5f6g7_add_users_table_and_chatbot_user_tracking.py"
        
        if os.path.exists(migration_file):
            print("✅ Migration file exists")
            
            with open(migration_file, 'r') as f:
                content = f.read()
            
            # Check for required content
            required_content = [
                "create_table('users'",
                "add_column('chatbots', sa.Column('created_by'",
                "add_column('chatbots', sa.Column('updated_by'"
            ]
            
            for required in required_content:
                if required in content:
                    print(f"✅ Migration contains: {required}")
                else:
                    print(f"❌ Migration missing: {required}")
                    return False
            
            return True
        else:
            print(f"❌ Migration file not found: {migration_file}")
            return False
    except Exception as e:
        print(f"❌ Failed to test migration file: {e}")
        return False

def test_chatbot_model_updates():
    """Test that Chatbot model has been updated with user tracking fields"""
    print("\nTesting Chatbot model updates...")
    try:
        from app.models import Chatbot
        
        # Check if the model has the new fields
        if hasattr(Chatbot, 'created_by'):
            print("✅ Chatbot model has created_by field")
        else:
            print("❌ Chatbot model missing created_by field")
            return False
        
        if hasattr(Chatbot, 'updated_by'):
            print("✅ Chatbot model has updated_by field")
        else:
            print("❌ Chatbot model missing updated_by field")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Failed to test Chatbot model: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing User Validation Implementation")
    print("=" * 50)
    
    tests = [
        test_user_model_import,
        test_user_service_import,
        test_user_service_methods,
        test_chatbot_model_updates,
        test_chatbot_service_updates,
        test_migration_file
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! User validation implementation is complete.")
        print("\n📝 Next steps:")
        print("1. Run the database migration: alembic upgrade head")
        print("2. Set IAM_BASE_PATH environment variable")
        print("3. Test the API endpoints with user validation")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
