"""
Test cases for entity filtering functionality
Tests that variable mappings and entity field updates are filtered based on chatbot entities configuration
"""

import pytest
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from typing import List, Dict, Any, Optional


class TestEntityFiltering:
    """Test suite for entity filtering based on chatbot entities configuration"""
    
    @pytest.mark.asyncio
    async def test_entity_field_update_filtering_with_allowed_entities(self):
        """Test that only allowed entities are updated when entities are configured"""
        print("\n🧪 Testing entity field update filtering with allowed entities...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        
        chatbot_service = ChatbotService()
        
        # Mock chatbot with LEAD entity configured
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "chatbot-123"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.entities = [{"entity": "LEAD"}]
        
        # Entity details for LEAD and CONTACT
        entity_details = [
            {"id": 100, "entityType": "LEAD"},
            {"id": 200, "entityType": "CONTACT"}
        ]
        
        # Collected answers for both entities
        collected_answers = [
            {
                "question": "First Name",
                "answer": "John",
                "field_name": "firstName",
                "entity_type": "LEAD"
            },
            {
                "question": "Last Name",
                "answer": "Doe",
                "field_name": "lastName",
                "entity_type": "CONTACT"
            }
        ]
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_chatbot
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch.object(chatbot_service, '_prepare_entity_update_event', new_callable=AsyncMock) as mock_prepare:
                with patch.object(chatbot_service, '_publish_entity_update_event', new_callable=AsyncMock) as mock_publish:
                    mock_prepare.return_value = {"entity": "LEAD"}
                    mock_publish.return_value = True
                    
                    # Call update_entities_after_conversation
                    result = await chatbot_service.update_entities_after_conversation(
                        entity_details=entity_details,
                        collected_answers=collected_answers,
                        tenant_id=2048,
                        user_id="user-123",
                        chatbot_id="chatbot-123"
                    )
                    
                    # Should only update LEAD (1 entity)
                    assert result["total_entities"] == 1
                    assert len(result["successful_updates"]) == 1
                    assert result["successful_updates"][0]["entity_type"] == "LEAD"
                    
                    # CONTACT should be filtered out
                    assert not any(u["entity_type"] == "CONTACT" for u in result["successful_updates"])
                    
                    print("✅ Test passed: Only LEAD entity was updated, CONTACT was filtered")
    
    @pytest.mark.asyncio
    async def test_entity_field_update_no_filtering_when_no_entities_configured(self):
        """Test that all entities are updated when no entities configured (backward compatibility)"""
        print("\n🧪 Testing entity field update without filtering (no entities configured)...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        
        chatbot_service = ChatbotService()
        
        # Mock chatbot with NO entities configured
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "chatbot-123"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.entities = None  # No entities configured
        
        # Entity details for LEAD and CONTACT
        entity_details = [
            {"id": 100, "entityType": "LEAD"},
            {"id": 200, "entityType": "CONTACT"}
        ]
        
        collected_answers = [
            {
                "question": "First Name",
                "answer": "John",
                "field_name": "firstName",
                "entity_type": "LEAD"
            },
            {
                "question": "Last Name",
                "answer": "Doe",
                "field_name": "lastName",
                "entity_type": "CONTACT"
            }
        ]
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_chatbot
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch.object(chatbot_service, '_prepare_entity_update_event', new_callable=AsyncMock) as mock_prepare:
                with patch.object(chatbot_service, '_publish_entity_update_event', new_callable=AsyncMock) as mock_publish:
                    mock_prepare.return_value = {"entity": "any"}
                    mock_publish.return_value = True
                    
                    result = await chatbot_service.update_entities_after_conversation(
                        entity_details=entity_details,
                        collected_answers=collected_answers,
                        tenant_id=2048,
                        user_id="user-123",
                        chatbot_id="chatbot-123"
                    )
                    
                    # Should update BOTH entities (no filtering)
                    assert result["total_entities"] == 2
                    assert len(result["successful_updates"]) == 2
                    
                    entity_types = [u["entity_type"] for u in result["successful_updates"]]
                    assert "LEAD" in entity_types
                    assert "CONTACT" in entity_types
                    
                    print("✅ Test passed: Both entities updated when no entities configured")
    
    @pytest.mark.asyncio
    async def test_entity_field_update_multiple_allowed_entities(self):
        """Test filtering with multiple entities configured"""
        print("\n🧪 Testing entity field update with multiple allowed entities...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        
        chatbot_service = ChatbotService()
        
        # Mock chatbot with LEAD and DEAL entities configured
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "chatbot-123"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.entities = [{"entity": "LEAD"}, {"entity": "DEAL"}]
        
        # Entity details for LEAD, CONTACT, and DEAL
        entity_details = [
            {"id": 100, "entityType": "LEAD"},
            {"id": 200, "entityType": "CONTACT"},
            {"id": 300, "entityType": "DEAL"}
        ]
        
        collected_answers = [
            {"question": "Q1", "answer": "A1", "field_name": "field1", "entity_type": "LEAD"},
            {"question": "Q2", "answer": "A2", "field_name": "field2", "entity_type": "CONTACT"},
            {"question": "Q3", "answer": "A3", "field_name": "field3", "entity_type": "DEAL"}
        ]
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_chatbot
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch.object(chatbot_service, '_prepare_entity_update_event', new_callable=AsyncMock) as mock_prepare:
                with patch.object(chatbot_service, '_publish_entity_update_event', new_callable=AsyncMock) as mock_publish:
                    mock_prepare.return_value = {"entity": "any"}
                    mock_publish.return_value = True
                    
                    result = await chatbot_service.update_entities_after_conversation(
                        entity_details=entity_details,
                        collected_answers=collected_answers,
                        tenant_id=2048,
                        user_id="user-123",
                        chatbot_id="chatbot-123"
                    )
                    
                    # Should update LEAD and DEAL (2 entities), CONTACT filtered out
                    assert result["total_entities"] == 2
                    assert len(result["successful_updates"]) == 2
                    
                    entity_types = [u["entity_type"] for u in result["successful_updates"]]
                    assert "LEAD" in entity_types
                    assert "DEAL" in entity_types
                    assert "CONTACT" not in entity_types
                    
                    print("✅ Test passed: LEAD and DEAL updated, CONTACT filtered")
    
    @pytest.mark.asyncio
    async def test_variable_mapping_filtering_with_allowed_entities(self):
        """Test that variable mappings are filtered based on allowed entities"""
        print("\n🧪 Testing variable mapping filtering with allowed entities...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        # Entity details for LEAD and CONTACT
        entity_details = [
            {"id": 100, "entity": "lead"},
            {"id": 200, "entity": "contact"}
        ]
        
        # Variable mappings for both LEAD and CONTACT
        variable_mappings = [
            {
                "entity": "lead",
                "internalName": "firstName",
                "variable": "1",
                "fallbackValue": "DefaultLead"
            },
            {
                "entity": "contact",
                "internalName": "lastName",
                "variable": "2",
                "fallbackValue": "DefaultContact"
            }
        ]
        
        # Only LEAD is allowed
        allowed_entities = ["LEAD"]
        
        with patch.object(entity_field_service, 'get_entity_field_value', return_value="John") as mock_get_field:
            result = entity_field_service.get_entity_field_values_from_mappings(
                entity_details=entity_details,
                variable_mappings=variable_mappings,
                token="test-token",
                allowed_entities=allowed_entities
            )
            
            # Should only have variable 1 (LEAD), not variable 2 (CONTACT)
            assert "1" in result
            assert "2" not in result
            assert result["1"] == "John"
            
            # Should only call get_entity_field_value for LEAD
            assert mock_get_field.call_count == 1
            
            print("✅ Test passed: Only LEAD variable mapping processed, CONTACT filtered")
    
    @pytest.mark.asyncio
    async def test_variable_mapping_no_filtering_when_none(self):
        """Test that all variable mappings are processed when allowed_entities is None"""
        print("\n🧪 Testing variable mapping without filtering (None allowed_entities)...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        entity_details = [
            {"id": 100, "entity": "lead"},
            {"id": 200, "entity": "contact"}
        ]
        
        variable_mappings = [
            {
                "entity": "lead",
                "internalName": "firstName",
                "variable": "1",
                "fallbackValue": "DefaultLead"
            },
            {
                "entity": "contact",
                "internalName": "lastName",
                "variable": "2",
                "fallbackValue": "DefaultContact"
            }
        ]
        
        # No filtering (None means all allowed)
        allowed_entities = None
        
        with patch.object(entity_field_service, 'get_entity_field_value') as mock_get_field:
            mock_get_field.side_effect = lambda eid, etype, fname, token: f"Value{eid}"
            
            result = entity_field_service.get_entity_field_values_from_mappings(
                entity_details=entity_details,
                variable_mappings=variable_mappings,
                token="test-token",
                allowed_entities=allowed_entities
            )
            
            # Should have both variables
            assert "1" in result
            assert "2" in result
            assert result["1"] == "Value100"
            assert result["2"] == "Value200"
            
            # Should call get_entity_field_value for both entities
            assert mock_get_field.call_count == 2
            
            print("✅ Test passed: All variable mappings processed when allowed_entities is None")
    
    @pytest.mark.asyncio
    async def test_variable_mapping_case_insensitive_filtering(self):
        """Test that entity filtering is case-insensitive"""
        print("\n🧪 Testing case-insensitive entity filtering...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        entity_details = [
            {"id": 100, "entity": "lead"},  # lowercase
            {"id": 200, "entity": "contact"}  # lowercase
        ]
        
        variable_mappings = [
            {
                "entity": "lead",  # lowercase
                "internalName": "firstName",
                "variable": "1",
                "fallbackValue": "Default"
            },
            {
                "entity": "contact",  # lowercase
                "internalName": "lastName",
                "variable": "2",
                "fallbackValue": "Default"
            }
        ]
        
        # Allowed entities in UPPERCASE
        allowed_entities = ["LEAD"]
        
        with patch.object(entity_field_service, 'get_entity_field_value', return_value="Value") as mock_get_field:
            result = entity_field_service.get_entity_field_values_from_mappings(
                entity_details=entity_details,
                variable_mappings=variable_mappings,
                token="test-token",
                allowed_entities=allowed_entities
            )
            
            # Should match despite case difference
            assert "1" in result
            assert "2" not in result
            
            print("✅ Test passed: Entity filtering is case-insensitive")
    
    @pytest.mark.asyncio
    async def test_entity_field_update_empty_entities_list(self):
        """Test that empty entities list allows all entities (backward compatibility)"""
        print("\n🧪 Testing entity field update with empty entities list...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        
        chatbot_service = ChatbotService()
        
        # Mock chatbot with EMPTY entities list
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "chatbot-123"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.entities = []  # Empty list
        
        entity_details = [
            {"id": 100, "entityType": "LEAD"},
            {"id": 200, "entityType": "CONTACT"}
        ]
        
        collected_answers = [
            {"question": "Q1", "answer": "A1", "field_name": "field1", "entity_type": "LEAD"},
            {"question": "Q2", "answer": "A2", "field_name": "field2", "entity_type": "CONTACT"}
        ]
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_chatbot
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch.object(chatbot_service, '_prepare_entity_update_event', new_callable=AsyncMock) as mock_prepare:
                with patch.object(chatbot_service, '_publish_entity_update_event', new_callable=AsyncMock) as mock_publish:
                    mock_prepare.return_value = {"entity": "any"}
                    mock_publish.return_value = True
                    
                    result = await chatbot_service.update_entities_after_conversation(
                        entity_details=entity_details,
                        collected_answers=collected_answers,
                        tenant_id=2048,
                        user_id="user-123",
                        chatbot_id="chatbot-123"
                    )
                    
                    # Should update BOTH entities (empty list = no filtering)
                    assert result["total_entities"] == 2
                    assert len(result["successful_updates"]) == 2
                    
                    print("✅ Test passed: Empty entities list allows all entities")
    
    @pytest.mark.asyncio
    async def test_entity_field_update_no_chatbot_id(self):
        """Test that no filtering happens when chatbot_id is not provided"""
        print("\n🧪 Testing entity field update without chatbot_id...")
        
        from app.services.chatbot_service import ChatbotService
        
        chatbot_service = ChatbotService()
        
        entity_details = [
            {"id": 100, "entityType": "LEAD"},
            {"id": 200, "entityType": "CONTACT"}
        ]
        
        collected_answers = [
            {"question": "Q1", "answer": "A1", "field_name": "field1", "entity_type": "LEAD"},
            {"question": "Q2", "answer": "A2", "field_name": "field2", "entity_type": "CONTACT"}
        ]
        
        with patch.object(chatbot_service, '_prepare_entity_update_event', new_callable=AsyncMock) as mock_prepare:
            with patch.object(chatbot_service, '_publish_entity_update_event', new_callable=AsyncMock) as mock_publish:
                mock_prepare.return_value = {"entity": "any"}
                mock_publish.return_value = True
                
                # No chatbot_id provided
                result = await chatbot_service.update_entities_after_conversation(
                    entity_details=entity_details,
                    collected_answers=collected_answers,
                    tenant_id=2048,
                    user_id="user-123",
                    chatbot_id=None  # No chatbot_id
                )
                
                # Should update both entities (no filtering)
                assert result["total_entities"] == 2
                assert len(result["successful_updates"]) == 2
                
                print("✅ Test passed: No filtering when chatbot_id is None")
    
    @pytest.mark.asyncio
    async def test_get_allowed_entities_helper_method(self):
        """Test the _get_allowed_entities helper method directly"""
        print("\n🧪 Testing _get_allowed_entities helper method...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        
        chatbot_service = ChatbotService()
        
        # Test case 1: Chatbot with entities
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.entities = [{"entity": "LEAD"}, {"entity": "CONTACT"}]
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_chatbot
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            result = chatbot_service._get_allowed_entities("chatbot-123", 2048)
            
            assert result == ["LEAD", "CONTACT"]
            mock_db.close.assert_called_once()
            
        # Test case 2: Chatbot not found
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = None  # Chatbot not found
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            result = chatbot_service._get_allowed_entities("invalid-id", 2048)
            
            assert result is None
            
        # Test case 3: No chatbot_id provided
        result = chatbot_service._get_allowed_entities(None, 2048)
        assert result is None
        
        # Test case 4: Chatbot with no entities
        mock_chatbot_no_entities = Mock(spec=Chatbot)
        mock_chatbot_no_entities.entities = None
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_chatbot_no_entities
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            result = chatbot_service._get_allowed_entities("chatbot-123", 2048)
            
            assert result is None
            
        print("✅ Test passed: _get_allowed_entities works correctly for all cases")
    
    @pytest.mark.asyncio
    async def test_variable_mapping_with_fallback_values(self):
        """Test that fallback values work correctly with entity filtering"""
        print("\n🧪 Testing variable mapping with fallback values and filtering...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        # Entity details - only LEAD exists
        entity_details = [
            {"id": 100, "entity": "lead"}
        ]
        
        # Variable mappings for LEAD and CONTACT (but CONTACT doesn't exist in entity_details)
        variable_mappings = [
            {
                "entity": "lead",
                "internalName": "firstName",
                "variable": "1",
                "fallbackValue": "DefaultLead"
            },
            {
                "entity": "contact",
                "internalName": "lastName",
                "variable": "2",
                "fallbackValue": "DefaultContact"
            }
        ]
        
        # Only LEAD is allowed (CONTACT is filtered before processing)
        allowed_entities = ["LEAD"]
        
        with patch.object(entity_field_service, 'get_entity_field_value', return_value="John"):
            result = entity_field_service.get_entity_field_values_from_mappings(
                entity_details=entity_details,
                variable_mappings=variable_mappings,
                token="test-token",
                allowed_entities=allowed_entities
            )
            
            # Should have variable 1 with actual value
            assert "1" in result
            assert result["1"] == "John"
            
            # Variable 2 should NOT exist (filtered out)
            assert "2" not in result
            
            print("✅ Test passed: Filtered entities don't get fallback values")
    
    @pytest.mark.asyncio
    async def test_entity_update_with_mixed_case_entity_types(self):
        """Test that entity filtering handles mixed case entity types correctly"""
        print("\n🧪 Testing entity update with mixed case entity types...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        
        chatbot_service = ChatbotService()
        
        # Mock chatbot with entities in various cases
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "chatbot-123"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.entities = [{"entity": "Lead"}, {"entity": "CONTACT"}]  # Mixed case
        
        # Entity details with different cases
        entity_details = [
            {"id": 100, "entityType": "LEAD"},  # uppercase
            {"id": 200, "entityType": "contact"},  # lowercase
            {"id": 300, "entityType": "Deal"}  # mixed case - should be filtered
        ]
        
        collected_answers = [
            {"question": "Q1", "answer": "A1", "field_name": "field1", "entity_type": "LEAD"},
            {"question": "Q2", "answer": "A2", "field_name": "field2", "entity_type": "contact"},
            {"question": "Q3", "answer": "A3", "field_name": "field3", "entity_type": "Deal"}
        ]
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_chatbot
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch.object(chatbot_service, '_prepare_entity_update_event', new_callable=AsyncMock) as mock_prepare:
                with patch.object(chatbot_service, '_publish_entity_update_event', new_callable=AsyncMock) as mock_publish:
                    mock_prepare.return_value = {"entity": "any"}
                    mock_publish.return_value = True
                    
                    result = await chatbot_service.update_entities_after_conversation(
                        entity_details=entity_details,
                        collected_answers=collected_answers,
                        tenant_id=2048,
                        user_id="user-123",
                        chatbot_id="chatbot-123"
                    )
                    
                    # Should update LEAD and CONTACT (case-insensitive match), DEAL filtered
                    assert result["total_entities"] == 2
                    assert len(result["successful_updates"]) == 2
                    
                    entity_types = [u["entity_type"].upper() for u in result["successful_updates"]]
                    assert "LEAD" in entity_types
                    assert "CONTACT" in entity_types
                    assert "DEAL" not in entity_types
                    
                    print("✅ Test passed: Case-insensitive entity matching works correctly")
    
    @pytest.mark.asyncio
    async def test_variable_mapping_multiple_allowed_entities(self):
        """Test variable mapping with multiple allowed entities"""
        print("\n🧪 Testing variable mapping with multiple allowed entities...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        # Entity details for LEAD, CONTACT, and DEAL
        entity_details = [
            {"id": 100, "entity": "lead"},
            {"id": 200, "entity": "contact"},
            {"id": 300, "entity": "deal"}
        ]
        
        # Variable mappings for all three entities
        variable_mappings = [
            {"entity": "lead", "internalName": "firstName", "variable": "1", "fallbackValue": ""},
            {"entity": "contact", "internalName": "lastName", "variable": "2", "fallbackValue": ""},
            {"entity": "deal", "internalName": "amount", "variable": "3", "fallbackValue": ""}
        ]
        
        # Only LEAD and DEAL are allowed
        allowed_entities = ["LEAD", "DEAL"]
        
        with patch.object(entity_field_service, 'get_entity_field_value') as mock_get_field:
            def mock_value(eid, etype, fname, token):
                return f"Value_{etype}_{fname}"
            mock_get_field.side_effect = mock_value
            
            result = entity_field_service.get_entity_field_values_from_mappings(
                entity_details=entity_details,
                variable_mappings=variable_mappings,
                token="test-token",
                allowed_entities=allowed_entities
            )
            
            # Should have variables 1 and 3 (LEAD and DEAL), not 2 (CONTACT)
            assert "1" in result
            assert "2" not in result
            assert "3" in result
            assert result["1"] == "Value_lead_firstName"
            assert result["3"] == "Value_deal_amount"
            
            # Should only call get_entity_field_value for LEAD and DEAL
            assert mock_get_field.call_count == 2
            
            print("✅ Test passed: Multiple entity variable mapping filtering works correctly")
    
    @pytest.mark.asyncio
    async def test_variable_mapping_empty_entities_list(self):
        """Test variable mapping with empty entities list (should allow all)"""
        print("\n🧪 Testing variable mapping with empty entities list...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        entity_details = [
            {"id": 100, "entity": "lead"},
            {"id": 200, "entity": "contact"}
        ]
        
        variable_mappings = [
            {"entity": "lead", "internalName": "firstName", "variable": "1", "fallbackValue": ""},
            {"entity": "contact", "internalName": "lastName", "variable": "2", "fallbackValue": ""}
        ]
        
        # Empty list should allow all entities (backward compatibility)
        allowed_entities = []
        
        with patch.object(entity_field_service, 'get_entity_field_value', return_value="Value"):
            result = entity_field_service.get_entity_field_values_from_mappings(
                entity_details=entity_details,
                variable_mappings=variable_mappings,
                token="test-token",
                allowed_entities=allowed_entities
            )
            
            # Empty list is falsy in Python, so it should be treated as None (allow all)
            # Actually, let me check the implementation...
            # In the code, we check: if allowed_entities is not None:
            # So empty list [] will trigger filtering (no entities allowed)
            # This is actually correct behavior - empty list = no entities configured explicitly
            
            # With empty list, no entities should pass the filter
            assert len(result) == 0
            
            print("✅ Test passed: Empty entities list filters all (correct behavior)")
    
    @pytest.mark.asyncio
    async def test_variable_mapping_with_nonexistent_entity(self):
        """Test variable mapping when entity doesn't exist in entity_details"""
        print("\n🧪 Testing variable mapping with nonexistent entity...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        # Only LEAD exists in entity_details
        entity_details = [
            {"id": 100, "entity": "lead"}
        ]
        
        # Mappings for LEAD and CONTACT (but CONTACT doesn't exist)
        variable_mappings = [
            {"entity": "lead", "internalName": "firstName", "variable": "1", "fallbackValue": "DefaultLead"},
            {"entity": "contact", "internalName": "lastName", "variable": "2", "fallbackValue": "DefaultContact"}
        ]
        
        # Both LEAD and CONTACT are allowed (CONTACT just doesn't exist in entity_details)
        allowed_entities = ["LEAD", "CONTACT"]
        
        with patch.object(entity_field_service, 'get_entity_field_value', return_value="John"):
            result = entity_field_service.get_entity_field_values_from_mappings(
                entity_details=entity_details,
                variable_mappings=variable_mappings,
                token="test-token",
                allowed_entities=allowed_entities
            )
            
            # Should have variable 1 (LEAD exists and is allowed)
            assert "1" in result
            assert result["1"] == "John"
            
            # Variable 2 (CONTACT) is allowed but entity doesn't exist, so uses fallback
            assert "2" in result
            assert result["2"] == "DefaultContact"
            
            print("✅ Test passed: Nonexistent entity uses fallback value when allowed")
    
    @pytest.mark.asyncio
    async def test_entity_filtering_for_ai_based_chatbot(self):
        """Test that entity filtering works for AI-based chatbots"""
        print("\n🧪 Testing entity filtering for AI-based chatbot...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        
        chatbot_service = ChatbotService()
        
        # Mock AI chatbot with only LEAD entity configured
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "ai-chatbot-123"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.type = "AI"  # AI-based chatbot
        mock_chatbot.entities = [{"entity": "LEAD"}]
        
        # Entity details for LEAD and CONTACT
        entity_details = [
            {"id": 100, "entityType": "LEAD"},
            {"id": 200, "entityType": "CONTACT"}
        ]
        
        # Collected answers from AI conversation
        collected_answers = [
            {"question": "What is your name?", "answer": "John", "field_name": "firstName", "entity_type": "LEAD"},
            {"question": "What is your email?", "answer": "<EMAIL>", "field_name": "email", "entity_type": "CONTACT"}
        ]
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_chatbot
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch.object(chatbot_service, '_prepare_entity_update_event', new_callable=AsyncMock) as mock_prepare:
                with patch.object(chatbot_service, '_publish_entity_update_event', new_callable=AsyncMock) as mock_publish:
                    mock_prepare.return_value = {"entity": "LEAD"}
                    mock_publish.return_value = True
                    
                    result = await chatbot_service.update_entities_after_conversation(
                        entity_details=entity_details,
                        collected_answers=collected_answers,
                        tenant_id=2048,
                        user_id="user-123",
                        chatbot_id="ai-chatbot-123"
                    )
                    
                    # Should only update LEAD (AI chatbot respects entity filtering)
                    assert result["total_entities"] == 1
                    assert len(result["successful_updates"]) == 1
                    assert result["successful_updates"][0]["entity_type"] == "LEAD"
                    
                    # CONTACT should be filtered out even in AI chatbot
                    assert not any(u["entity_type"] == "CONTACT" for u in result["successful_updates"])
                    
                    print("✅ Test passed: AI chatbot respects entity filtering")
    
    @pytest.mark.asyncio
    async def test_entity_filtering_for_rule_based_chatbot(self):
        """Test that entity filtering works for RULE-based chatbots"""
        print("\n🧪 Testing entity filtering for RULE-based chatbot...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        
        chatbot_service = ChatbotService()
        
        # Mock RULE chatbot with only CONTACT entity configured
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "rule-chatbot-123"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.type = "RULE"  # RULE-based chatbot
        mock_chatbot.entities = [{"entity": "CONTACT"}]
        
        # Entity details for LEAD and CONTACT
        entity_details = [
            {"id": 100, "entityType": "LEAD"},
            {"id": 200, "entityType": "CONTACT"}
        ]
        
        # Collected answers from RULE conversation
        collected_answers = [
            {"question": "Name?", "answer": "Jane", "field_name": "firstName", "entity_type": "LEAD"},
            {"question": "Phone?", "answer": "1234567890", "field_name": "phone", "entity_type": "CONTACT"}
        ]
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_chatbot
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch.object(chatbot_service, '_prepare_entity_update_event', new_callable=AsyncMock) as mock_prepare:
                with patch.object(chatbot_service, '_publish_entity_update_event', new_callable=AsyncMock) as mock_publish:
                    mock_prepare.return_value = {"entity": "CONTACT"}
                    mock_publish.return_value = True
                    
                    result = await chatbot_service.update_entities_after_conversation(
                        entity_details=entity_details,
                        collected_answers=collected_answers,
                        tenant_id=2048,
                        user_id="user-123",
                        chatbot_id="rule-chatbot-123"
                    )
                    
                    # Should only update CONTACT (RULE chatbot respects entity filtering)
                    assert result["total_entities"] == 1
                    assert len(result["successful_updates"]) == 1
                    assert result["successful_updates"][0]["entity_type"] == "CONTACT"
                    
                    # LEAD should be filtered out
                    assert not any(u["entity_type"] == "LEAD" for u in result["successful_updates"])
                    
                    print("✅ Test passed: RULE chatbot respects entity filtering")
    
    @pytest.mark.asyncio
    async def test_variable_mapping_only_applies_to_rule_based_chatbots(self):
        """Verify that variable mappings are RULE-based feature and filtered correctly"""
        print("\n🧪 Testing variable mapping is RULE-based feature...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        # Simulate RULE-based chatbot scenario
        entity_details = [
            {"id": 100, "entity": "lead"},
            {"id": 200, "entity": "contact"}
        ]
        
        # Variable mappings (only used in RULE chatbots)
        variable_mappings = [
            {"entity": "lead", "internalName": "firstName", "variable": "1", "fallbackValue": ""},
            {"entity": "contact", "internalName": "lastName", "variable": "2", "fallbackValue": ""}
        ]
        
        # RULE chatbot with only LEAD configured
        allowed_entities = ["LEAD"]
        
        with patch.object(entity_field_service, 'get_entity_field_value', return_value="Value"):
            result = entity_field_service.get_entity_field_values_from_mappings(
                entity_details=entity_details,
                variable_mappings=variable_mappings,
                token="test-token",
                allowed_entities=allowed_entities
            )
            
            # Should only process LEAD variable (RULE chatbot feature)
            assert "1" in result  # LEAD variable
            assert "2" not in result  # CONTACT filtered
            
            print("✅ Test passed: Variable mappings filtered correctly in RULE chatbots")
    
    @pytest.mark.asyncio
    async def test_both_chatbot_types_use_entity_field_filtering(self):
        """Test that both AI and RULE chatbots use entity field update filtering"""
        print("\n🧪 Testing both chatbot types use entity field filtering...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        
        chatbot_service = ChatbotService()
        
        # Test data
        entity_details = [
            {"id": 100, "entityType": "LEAD"},
            {"id": 200, "entityType": "DEAL"}
        ]
        collected_answers = [
            {"question": "Q1", "answer": "A1", "field_name": "f1", "entity_type": "LEAD"},
            {"question": "Q2", "answer": "A2", "field_name": "f2", "entity_type": "DEAL"}
        ]
        
        # Test AI chatbot
        mock_ai_chatbot = Mock(spec=Chatbot)
        mock_ai_chatbot.id = "ai-123"
        mock_ai_chatbot.type = "AI"
        mock_ai_chatbot.entities = [{"entity": "LEAD"}]
        
        # Test RULE chatbot
        mock_rule_chatbot = Mock(spec=Chatbot)
        mock_rule_chatbot.id = "rule-123"
        mock_rule_chatbot.type = "RULE"
        mock_rule_chatbot.entities = [{"entity": "LEAD"}]
        
        for chatbot_type, mock_chatbot in [("AI", mock_ai_chatbot), ("RULE", mock_rule_chatbot)]:
            with patch('app.services.chatbot_service.get_db') as mock_get_db:
                mock_db = Mock()
                mock_query = Mock()
                mock_filter = Mock()
                mock_filter.first.return_value = mock_chatbot
                mock_query.filter.return_value = mock_filter
                mock_db.query.return_value = mock_query
                mock_get_db.return_value = iter([mock_db])
                
                with patch.object(chatbot_service, '_prepare_entity_update_event', new_callable=AsyncMock):
                    with patch.object(chatbot_service, '_publish_entity_update_event', new_callable=AsyncMock, return_value=True):
                        result = await chatbot_service.update_entities_after_conversation(
                            entity_details=entity_details,
                            collected_answers=collected_answers,
                            tenant_id=2048,
                            user_id="user-123",
                            chatbot_id=mock_chatbot.id
                        )
                        
                        # Both should only update LEAD (both use entity filtering)
                        assert result["total_entities"] == 1
                        assert result["successful_updates"][0]["entity_type"] == "LEAD"
                        print(f"✅ {chatbot_type} chatbot uses entity field filtering")
        
        print("✅ Test passed: Both AI and RULE chatbots use entity field filtering")

