"""
Test cases for JWT Service

This module contains comprehensive test cases for the JWT service functionality
including token creation, IAM integration, and error handling scenarios.
"""

import pytest
import json
import os
from unittest.mock import patch, MagicMock
from jose import jwt
import datetime
import requests

from app.services.jwt_service import JWTService, jwt_service
from app.exceptions import (
    IAMServiceException,
    IAMUserNotFoundException,
    IAMPermissionException,
    JWTTokenCreationException,
    EnvironmentVariableError
)


class TestJWTService:
    """Test cases for JWT Service functionality"""
    
    def setup_method(self):
        """Set up test environment before each test"""
        self.jwt_service = JWTService()
        self.test_user_id = "794"
        self.test_tenant_id = "478"
        self.mock_iam_response = {
            "id": "actual_user_794",
            "firstName": "Test",
            "lastName": "User",
            "tenantId": "478",
            "email": "<EMAIL>",
            "permissions": [
                {
                    "id": 1,
                    "name": "user",
                    "description": "has access to user resource",
                    "action": {
                        "read": True, "readAll": True, "write": True,
                        "update": True, "updateAll": True
                    }
                },
                {
                    "id": 2,
                    "name": "task",
                    "description": "has access to task resource",
                    "action": {
                        "read": True, "readAll": True, "write": True,
                        "update": True, "updateAll": True
                    }
                }
            ]
        }
    
    @patch.dict(os.environ, {"SECRET_KEY": "test-secret", "IAM_BASE_PATH": "http://iam.test.com"})
    def test_jwt_service_initialization(self):
        """Test JWT service initialization with environment variables"""
        service = JWTService()
        assert service.secret == "test-secret"
        assert service.iam_base_path == "http://iam.test.com"
    
    @patch.dict(os.environ, {}, clear=True)
    def test_jwt_service_initialization_defaults(self):
        """Test JWT service initialization with default values"""
        service = JWTService()
        assert service.secret == "test"
        assert service.iam_base_path == "http://sd-iam"
    
    @patch('requests.get')
    def test_build_jwt_token_for_analysis_success(self, mock_get):
        """Test successful JWT token creation with IAM integration"""
        # Mock the IAM response
        mock_response = MagicMock()
        mock_response.json.return_value = self.mock_iam_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Test token creation
        token, actual_user_id = self.jwt_service.build_jwt_token_for_analysis(
            self.test_user_id, self.test_tenant_id
        )
        
        # Verify return values
        assert isinstance(token, str)
        assert len(token) > 0
        assert actual_user_id == 794  # Should be long integer
        assert isinstance(actual_user_id, int)
        
        # Verify token structure by decoding (without verification for testing)
        decoded_token = jwt.decode(token, key="dummy-key", options={"verify_signature": False})
        assert decoded_token["iss"] == "sell"
        assert "data" in decoded_token
        assert decoded_token["data"]["tenantId"] == self.test_tenant_id
        assert decoded_token["data"]["userId"] == "794"  # Should be string in token
        assert decoded_token["data"]["permissions"] == self.mock_iam_response["permissions"]
        
        # Verify IAM was called with correct parameters
        expected_url = f"{self.jwt_service.iam_base_path}/v1/tenants/{self.test_tenant_id}/creator"
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert call_args[0][0] == expected_url
        assert "Authorization" in call_args[1]["headers"]
        assert call_args[1]["headers"]["Authorization"].startswith("Bearer ")
    
    @patch('requests.get')
    def test_build_jwt_token_for_analysis_iam_failure(self, mock_get):
        """Test JWT token creation when IAM request fails"""
        # Mock IAM request failure
        mock_get.side_effect = requests.exceptions.RequestException("IAM service unavailable")
        
        # Test token creation should raise IAMServiceException
        with pytest.raises(IAMServiceException) as exc_info:
            self.jwt_service.build_jwt_token_for_analysis(self.test_user_id, self.test_tenant_id)
        
        # Verify exception details
        assert exc_info.value.details["operation"] == "get_tenant_creator"
        assert "IAM service unavailable" in str(exc_info.value)
    
    @patch('requests.get')
    def test_build_jwt_token_for_analysis_no_permissions(self, mock_get):
        """Test JWT token creation when user has no permissions"""
        # Mock IAM response with no permissions
        mock_response = MagicMock()
        mock_iam_response_no_perms = self.mock_iam_response.copy()
        mock_iam_response_no_perms["permissions"] = None
        mock_response.json.return_value = mock_iam_response_no_perms
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Test token creation should raise IAMPermissionException
        with pytest.raises(IAMPermissionException) as exc_info:
            self.jwt_service.build_jwt_token_for_analysis(self.test_user_id, self.test_tenant_id)
        
        assert exc_info.value.details["userId"] == "actual_user_794"
        assert "No permissions found for tenant user" in str(exc_info.value)
    
    @patch('requests.get')
    def test_build_jwt_token_for_analysis_empty_permissions(self, mock_get):
        """Test JWT token creation when user has empty permissions list"""
        # Mock IAM response with empty permissions
        mock_response = MagicMock()
        mock_iam_response_empty_perms = self.mock_iam_response.copy()
        mock_iam_response_empty_perms["permissions"] = []
        mock_response.json.return_value = mock_iam_response_empty_perms
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Test token creation should raise IAMPermissionException
        with pytest.raises(IAMPermissionException) as exc_info:
            self.jwt_service.build_jwt_token_for_analysis(self.test_user_id, self.test_tenant_id)
        
        assert exc_info.value.details["userId"] == "actual_user_794"
        assert "No permissions found for tenant user" in str(exc_info.value)
    
    def test_build_jwt_token_for_analysis_no_iam_base_path(self):
        """Test JWT token creation when IAM_BASE_PATH is not configured"""
        # Create service with no IAM base path
        service = JWTService()
        service.iam_base_path = None
        
        with pytest.raises(IAMServiceException) as exc_info:
            service.build_jwt_token_for_analysis(self.test_user_id, self.test_tenant_id)
        
        assert exc_info.value.details["operation"] == "get_tenant_creator"
        assert "IAM_BASE_PATH environment variable is missing" in str(exc_info.value)
    
    @patch('requests.get')
    def test_get_tenant_creator_from_iam_success(self, mock_get):
        """Test successful IAM user data retrieval"""
        # Mock the IAM response
        mock_response = MagicMock()
        mock_response.json.return_value = self.mock_iam_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        test_token = "test.jwt.token"
        
        # Test IAM data retrieval
        user_data = self.jwt_service.get_tenant_creator_from_iam(self.test_tenant_id, test_token)
        
        # Verify returned data structure
        expected_data = {
            "id": "actual_user_794",
            "name": "Test User",
            "tenant_id": "478",
            "email": "<EMAIL>",
            "permissions": self.mock_iam_response["permissions"]
        }
        assert user_data == expected_data
        
        # Verify IAM was called correctly
        expected_url = f"{self.jwt_service.iam_base_path}/v1/tenants/{self.test_tenant_id}/creator"
        mock_get.assert_called_once_with(
            expected_url,
            headers={"Authorization": f"Bearer {test_token}"}
        )
    
    @patch('requests.get')
    def test_get_tenant_creator_from_iam_missing_name_fields(self, mock_get):
        """Test IAM user data retrieval with missing name fields"""
        # Mock IAM response with missing name fields
        mock_response = MagicMock()
        mock_iam_response_no_names = self.mock_iam_response.copy()
        del mock_iam_response_no_names["firstName"]
        del mock_iam_response_no_names["lastName"]
        mock_response.json.return_value = mock_iam_response_no_names
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        test_token = "test.jwt.token"
        
        # Test IAM data retrieval
        user_data = self.jwt_service.get_tenant_creator_from_iam(self.test_tenant_id, test_token)
        
        # Verify name is empty when fields are missing
        assert user_data["name"] == ""
    
    @patch('requests.get')
    def test_get_tenant_creator_from_iam_partial_name_fields(self, mock_get):
        """Test IAM user data retrieval with partial name fields"""
        # Mock IAM response with only firstName
        mock_response = MagicMock()
        mock_iam_response_first_only = self.mock_iam_response.copy()
        del mock_iam_response_first_only["lastName"]
        mock_response.json.return_value = mock_iam_response_first_only
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        test_token = "test.jwt.token"
        
        # Test IAM data retrieval
        user_data = self.jwt_service.get_tenant_creator_from_iam(self.test_tenant_id, test_token)
        
        # Verify name contains only firstName
        assert user_data["name"] == "Test"
    
    @patch('requests.get')
    def test_get_tenant_creator_from_iam_request_exception(self, mock_get):
        """Test IAM user data retrieval when request fails"""
        # Mock request exception
        mock_get.side_effect = requests.exceptions.RequestException("Connection error")
        
        test_token = "test.jwt.token"
        
        # Test IAM data retrieval should raise IAMServiceException
        with pytest.raises(IAMServiceException) as exc_info:
            self.jwt_service.get_tenant_creator_from_iam(self.test_tenant_id, test_token)
        
        # Verify exception details
        assert exc_info.value.details["operation"] == "get_tenant_creator"
        assert "Connection error" in str(exc_info.value)
    
    @patch('requests.get')
    def test_get_tenant_creator_from_iam_http_error(self, mock_get):
        """Test IAM user data retrieval when HTTP error occurs"""
        # Mock HTTP error response
        mock_response = MagicMock()
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("404 Not Found")
        mock_get.return_value = mock_response
        
        test_token = "test.jwt.token"
        
        # Test IAM data retrieval should raise IAMServiceException
        with pytest.raises(IAMServiceException) as exc_info:
            self.jwt_service.get_tenant_creator_from_iam(self.test_tenant_id, test_token)
        
        # Verify exception details
        assert exc_info.value.details["operation"] == "get_tenant_creator"
        assert "404 Not Found" in str(exc_info.value)
    
    def test_get_tenant_creator_from_iam_no_base_path(self):
        """Test IAM user data retrieval when base path is not configured"""
        # Create service with no IAM base path
        service = JWTService()
        service.iam_base_path = None
        
        test_token = "test.jwt.token"
        
        with pytest.raises(EnvironmentVariableError) as exc_info:
            service.get_tenant_creator_from_iam(self.test_tenant_id, test_token)
        
        assert exc_info.value.details["configKey"] == "IAM_BASE_PATH"
        assert "IAM_BASE_PATH environment variable is missing" in str(exc_info.value)
    
    @patch('requests.get')
    def test_jwt_token_expiry_and_structure(self, mock_get):
        """Test JWT token expiry and internal structure"""
        # Mock the IAM response
        mock_response = MagicMock()
        mock_response.json.return_value = self.mock_iam_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Test token creation
        token, _ = self.jwt_service.build_jwt_token_for_analysis(
            self.test_user_id, self.test_tenant_id
        )
        
        # Decode token to check structure
        decoded_token = jwt.decode(token, key="dummy-key", options={"verify_signature": False})
        
        # Verify token expiry is set correctly (should be ~5 minutes from now)
        exp_time = datetime.datetime.fromtimestamp(decoded_token["exp"], tz=datetime.timezone.utc)
        now = datetime.datetime.now(datetime.timezone.utc)
        time_diff = exp_time - now
        
        # Should be approximately 5 minutes (allow for test execution time)
        assert 290 <= time_diff.total_seconds() <= 310
        
        # Verify core access token structure
        data = decoded_token["data"]
        assert data["accessToken"] == "internal-token"
        assert data["expiresIn"] == 5 * 60 * 1000  # 5 minutes in milliseconds
        assert data["tokenType"] == "Bearer"
        assert "meta" in data
        assert data["meta"]["rate-limit"] == 5
        assert data["meta"]["pid"] == 2
    
    def test_global_jwt_service_instance(self):
        """Test that the global jwt_service instance is properly configured"""
        assert jwt_service is not None
        assert isinstance(jwt_service, JWTService)
        assert hasattr(jwt_service, 'build_jwt_token_for_analysis')
        assert hasattr(jwt_service, 'get_tenant_creator_from_iam')


class TestJWTServiceIntegration:
    """Integration test cases for JWT Service"""
    
    @patch.dict(os.environ, {"SECRET_KEY": "integration-test-secret", "IAM_BASE_PATH": "http://iam.integration.test"})
    @patch('requests.get')
    def test_end_to_end_token_creation_workflow(self, mock_get):
        """Test complete end-to-end token creation workflow"""
        # Mock IAM response
        iam_response = {
            "id": "integration_user_123",
            "firstName": "Integration",
            "lastName": "Test",
            "tenantId": "999",
            "email": "<EMAIL>",
            "permissions": [
                {
                    "id": 1,
                    "name": "user",
                    "description": "user access",
                    "action": {"read": True, "write": True}
                }
            ]
        }
        
        mock_response = MagicMock()
        mock_response.json.return_value = iam_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Create service instance
        service = JWTService()
        
        # Test complete workflow
        user_id = "123"
        tenant_id = "999"
        
        token, actual_user_id = service.build_jwt_token_for_analysis(user_id, tenant_id)
        
        # Verify complete workflow results
        assert isinstance(token, str)
        assert actual_user_id == 123  # Should be long integer
        assert isinstance(actual_user_id, int)
        
        # Verify token can be decoded and contains expected data
        decoded = jwt.decode(token, key="dummy-key", options={"verify_signature": False})
        assert decoded["iss"] == "sell"
        assert decoded["data"]["userId"] == "123"  # Should be string in token
        assert decoded["data"]["tenantId"] == tenant_id
        assert len(decoded["data"]["permissions"]) == 1
        assert decoded["data"]["permissions"][0]["name"] == "user"
        
        # Verify IAM was called twice (once for temp token, once for final token)
        assert mock_get.call_count >= 1
        
        # Verify final IAM call used correct URL
        expected_url = f"http://iam.integration.test/v1/tenants/{tenant_id}/creator"
        call_args = mock_get.call_args
        assert call_args[0][0] == expected_url
