#!/usr/bin/env python3
"""
Test script to verify the ChatbotService functionality
"""

import os
import sys
import logging
import pytest
from unittest.mock import MagicMock, patch, AsyncMock, Mock
from app.services.chatbot_service import ChatbotService
from app.models import Chatbot

# Set up basic logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_chatbot_service_import():
    """Test that ChatbotService can be imported successfully"""
    print("Testing ChatbotService import...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        service = ChatbotService()
        print("✓ ChatbotService imported and instantiated successfully")
        print(f"  - Service type: {type(service).__name__}")
        
        return True
        
    except Exception as e:
        print(f"✗ ChatbotService import failed: {str(e)}")
        return False


def test_chatbot_service_methods():
    """Test that ChatbotService has all expected methods"""
    print("\nTesting ChatbotService methods...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        service = ChatbotService()
        
        expected_methods = [
            'collect_and_publish_chatbot_usage',
            'get_chatbot_count_by_tenant',
            'get_all_tenant_chatbot_counts',
            'get_chatbot_by_id',
            'get_chatbots_by_tenant',
            'get_chatbot_statistics'
        ]
        
        found_methods = []
        for method_name in expected_methods:
            if hasattr(service, method_name):
                method = getattr(service, method_name)
                if callable(method):
                    found_methods.append(method_name)
                    print(f"  ✓ {method_name} method found")
                else:
                    print(f"  ✗ {method_name} is not callable")
            else:
                print(f"  ✗ {method_name} method not found")
        
        if len(found_methods) == len(expected_methods):
            print("✓ All expected methods are present")
            return True
        else:
            print(f"✗ Missing {len(expected_methods) - len(found_methods)} expected methods")
            return False
            
    except Exception as e:
        print(f"✗ Method testing failed: {str(e)}")
        return False


def test_event_listener_integration():
    """Test that event listeners properly use ChatbotService"""
    print("\nTesting event listener integration...")
    
    try:
        from app.services.event_listeners import SchedulerEventListener
        
        listener = SchedulerEventListener()
        
        # Check if the _perform_usage_collection method exists
        if hasattr(listener, '_perform_usage_collection'):
            print("✓ _perform_usage_collection method found in SchedulerEventListener")
            
            # Read the source to verify it uses ChatbotService
            import inspect
            source = inspect.getsource(listener._perform_usage_collection)
            
            if 'ChatbotService' in source:
                print("✓ SchedulerEventListener uses ChatbotService")
                return True
            else:
                print("✗ SchedulerEventListener does not use ChatbotService")
                return False
        else:
            print("✗ _perform_usage_collection method not found")
            return False
            
    except Exception as e:
        print(f"✗ Event listener integration test failed: {str(e)}")
        return False


@pytest.mark.asyncio
async def test_chatbot_usage_collection_mock():
    """Test chatbot usage collection with mocked dependencies"""
    print("\nTesting chatbot usage collection (mocked)...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        
        # Mock the database and RabbitMQ dependencies
        with patch('app.services.chatbot_service.get_db') as mock_get_db, \
             patch('app.services.chatbot_service.rabbitmq_service') as mock_rabbitmq:
            
            # Mock database session and query results
            mock_db = MagicMock()
            mock_get_db.return_value = iter([mock_db])
            
            # Mock query results - simulate 2 tenants with chatbots
            mock_query_result = [
                ('tenant1', 3),  # tenant1 has 3 chatbots
                ('tenant2', 1)   # tenant2 has 1 chatbot
            ]
            
            mock_db.query.return_value.filter.return_value.group_by.return_value.all.return_value = mock_query_result
            
            # Mock RabbitMQ publish_event as async
            mock_rabbitmq.publish_event = AsyncMock()
            
            # Test the service
            service = ChatbotService()
            await service.collect_and_publish_chatbot_usage()
            
            # Verify the publish_event was called with correct data
            mock_rabbitmq.publish_event.assert_called_once()
            call_args = mock_rabbitmq.publish_event.call_args
            
            expected_payload = [
                {"tenantId": "tenant1", "usageEntity": "CHATBOT", "count": 3},
                {"tenantId": "tenant2", "usageEntity": "CHATBOT", "count": 1}
            ]
            
            assert call_args[0][0] == "usage.collected"  # routing key
            assert call_args[0][1] == expected_payload    # payload
            
            print("✓ Chatbot usage collection works correctly")
            print(f"  - Published data for {len(expected_payload)} tenants")
            print(f"  - Routing key: usage.collected")
            
            return True
            
    except Exception as e:
        print(f"✗ Chatbot usage collection test failed: {str(e)}")
        return False


def test_chatbot_service_structure():
    """Test the overall structure of the ChatbotService"""
    print("\nTesting ChatbotService structure...")
    
    try:
        with open('app/services/chatbot_service.py', 'r') as f:
            content = f.read()
        
        required_components = [
            'class ChatbotService',
            'async def collect_and_publish_chatbot_usage',
            'def get_chatbot_count_by_tenant',
            'def get_all_tenant_chatbot_counts',
            'def get_chatbot_by_id',
            'def get_chatbots_by_tenant',
            'def get_chatbot_statistics',
            'from app.services.rabbitmq_service import rabbitmq_service',
            'logger = logging.getLogger(__name__)'
        ]
        
        found_components = []
        for component in required_components:
            if component in content:
                found_components.append(component)
                print(f"  ✓ {component}")
            else:
                print(f"  ✗ {component} not found")
        
        if len(found_components) == len(required_components):
            print("✓ ChatbotService structure is complete")
            return True
        else:
            print(f"✗ Missing {len(required_components) - len(found_components)} required components")
            return False
            
    except FileNotFoundError:
        print("✗ chatbot_service.py not found")
        return False
    except Exception as e:
        print(f"✗ Structure test failed: {str(e)}")
        return False


async def run_async_tests():
    """Run async tests"""
    async_tests = [
        test_chatbot_usage_collection_mock
    ]
    
    results = []
    for test in async_tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ Async test {test.__name__} failed: {str(e)}")
            results.append(False)
    
    return results


def main():
    """Run all ChatbotService tests"""
    print("=" * 70)
    print("ChatbotService Test Suite")
    print("=" * 70)
    
    # Sync tests
    sync_tests = [
        test_chatbot_service_import,
        test_chatbot_service_methods,
        test_event_listener_integration,
        test_chatbot_service_structure,
        test_get_chatbot_with_details_rule_based,
        test_get_chatbot_with_details_ai_based,
        test_exact_json_response_format_rule_based
    ]
    
    sync_results = []
    for test in sync_tests:
        try:
            result = test()
            sync_results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed: {str(e)}")
            sync_results.append(False)
    
    # Run async tests
    import asyncio
    asyncio.run(run_async_tests())
    
    # Use sync results only for the main test summary
    passed = sum(sync_results)
    total = len(sync_results)
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! ChatbotService is working correctly.")
        print("\nChatbotService Features:")
        print("  • Collects chatbot usage data by tenant")
        print("  • Publishes usage data to RabbitMQ")
        print("  • Provides chatbot count and statistics methods")
        print("  • Integrates with existing event listener system")
        print("  • Proper error handling and logging")
    else:
        print("✗ Some tests failed. Please review the implementation.")
    
    print("=" * 70)


def test_get_chatbot_with_details_rule_based():
    """Test that get_chatbot_with_details includes nodes and edges for rule-based chatbots"""
    print("\nTesting get_chatbot_with_details for rule-based chatbots...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        from unittest.mock import MagicMock, patch
        
        service = ChatbotService()
        
        # Mock database objects
        mock_chatbot = MagicMock()
        mock_chatbot.id = "test-chatbot-id"
        mock_chatbot.tenant_id = 123
        mock_chatbot.name = "Test Rule-Based Chatbot"
        mock_chatbot.type = "RULE"
        mock_chatbot.description = "Test description"
        mock_chatbot.welcome_message = "Welcome!"
        mock_chatbot.thank_you_message = "Thank you!"
        mock_chatbot.connected_account_display_name = "Test Account"
        mock_chatbot.connected_account_id = 456
        mock_chatbot.trigger = "NEW_ENTITY"
        mock_chatbot.status = "ACTIVE"
        mock_chatbot.created_at = "2025-01-01T00:00:00"
        mock_chatbot.updated_at = "2025-01-01T00:00:00"
        
        # Mock nodes
        mock_node = MagicMock()
        mock_node.id = "node-1"
        mock_node.node_id = "node-123"
        mock_node.name = "start_node"
        mock_node.type = "question"
        mock_node.position_x = 100
        mock_node.position_y = 200
        mock_node.data = {"text": "Hello, how can I help you?"}
        mock_node.variable_mapping = []
        mock_node.created_at = "2025-01-01T00:00:00"
        mock_node.updated_at = "2025-01-01T00:00:00"
        
        # Mock edges
        mock_edge = MagicMock()
        mock_edge.id = "edge-1"
        mock_edge.edge_id = "edge-123"
        mock_edge.source_node = "node-123"
        mock_edge.source_handle = "source"
        mock_edge.target_node = "node-456"
        mock_edge.created_at = "2025-01-01T00:00:00"
        mock_edge.updated_at = "2025-01-01T00:00:00"
        
        # Mock entity fields
        mock_entity_field = MagicMock()
        mock_entity_field.entity_type = "lead"
        mock_entity_field.field_id = 789
        mock_entity_field.name = "first_name"
        mock_entity_field.display_name = "First Name"
        mock_entity_field.standard = True
        
        # Mock database session and queries
        mock_db = MagicMock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot
        mock_db.query.return_value.filter.return_value.all.side_effect = [
            [],  # questions
            [],  # knowledgebase
            [mock_node],  # nodes
            [mock_edge],  # edges
            [mock_entity_field]  # node entity fields
        ]
        
        with patch('app.services.chatbot_service.next') as mock_next:
            mock_next.return_value = mock_db
            
            result = service.get_chatbot_with_details("test-chatbot-id", 123)
            
            # Verify basic chatbot data
            assert result["id"] == "test-chatbot-id"
            assert result["type"] == "RULE"
            assert result["name"] == "Test Rule-Based Chatbot"
            
            # Verify nodes and edges are included for rule-based chatbots
            assert "nodes" in result
            assert "edges" in result
            assert len(result["nodes"]) == 1
            assert len(result["edges"]) == 1
            
            # Verify node structure
            node = result["nodes"][0]
            assert node["id"] == "node-123"  # Should be string as node_id
            assert node["name"] == "start_node"
            assert node["type"] == "question"
            assert node["position"]["x"] == 100
            assert node["position"]["y"] == 200
            assert "data" in node
            assert "entityFields" in node["data"]
            
            # Verify edge structure
            edge = result["edges"][0]
            assert edge["id"] == "edge-123"
            assert edge["source"] == "node-123"
            assert edge["target"] == "node-456"
            
            print("✓ get_chatbot_with_details correctly includes nodes and edges for rule-based chatbots")
            return True
            
    except Exception as e:
        print(f"✗ get_chatbot_with_details test failed: {str(e)}")
        return False


def test_get_chatbot_with_details_ai_based():
    """Test that get_chatbot_with_details does not include nodes and edges for AI-based chatbots"""
    print("\nTesting get_chatbot_with_details for AI-based chatbots...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        from unittest.mock import MagicMock, patch
        
        service = ChatbotService()
        
        # Mock database objects
        mock_chatbot = MagicMock()
        mock_chatbot.id = "test-chatbot-id"
        mock_chatbot.tenant_id = 123
        mock_chatbot.name = "Test AI-Based Chatbot"
        mock_chatbot.type = "AI"
        mock_chatbot.description = "Test description"
        mock_chatbot.welcome_message = "Welcome!"
        mock_chatbot.thank_you_message = "Thank you!"
        mock_chatbot.connected_account_display_name = "Test Account"
        mock_chatbot.connected_account_id = 456
        mock_chatbot.trigger = "NEW_ENTITY"
        mock_chatbot.status = "ACTIVE"
        mock_chatbot.created_at = "2025-01-01T00:00:00"
        mock_chatbot.updated_at = "2025-01-01T00:00:00"
        
        # Mock database session and queries
        mock_db = MagicMock()
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot
        mock_db.query.return_value.filter.return_value.all.return_value = []  # Empty for questions and knowledgebase
        
        with patch('app.services.chatbot_service.next') as mock_next:
            mock_next.return_value = mock_db
            
            result = service.get_chatbot_with_details("test-chatbot-id", 123)
            
            # Verify basic chatbot data
            assert result["id"] == "test-chatbot-id"
            assert result["type"] == "AI"
            assert result["name"] == "Test AI-Based Chatbot"
            
            # Verify nodes and edges are NOT included for AI-based chatbots
            assert "nodes" not in result
            assert "edges" not in result
            
            print("✓ get_chatbot_with_details correctly excludes nodes and edges for AI-based chatbots")
            return True
            
    except Exception as e:
        print(f"✗ get_chatbot_with_details AI test failed: {str(e)}")
        return False


def test_exact_json_response_format_rule_based():
    """Test that the response matches the exact expected JSON format for rule-based chatbots"""
    print("\nTesting exact JSON response format for rule-based chatbots...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        from unittest.mock import MagicMock, patch
        import json
        
        service = ChatbotService()
        
        # Mock database objects matching the expected payload
        mock_chatbot = MagicMock()
        mock_chatbot.id = "97753c94-7333-45b3-b438-ce6992732f79"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.name = "Meddybuddie 🩺🤖 – Your Caring Health Companion"
        mock_chatbot.type = "RULE"
        mock_chatbot.description = "Meddybuddie is your 24/7 medical companion that helps you book appointments, answer common health queries, remind you of medicines, and connect you with the right doctors. Whether it's a quick check-up or urgent consultation, we make healthcare easy,"
        mock_chatbot.welcome_message = "👋 Hello and welcome to Meddybuddie – your friendly health assistant! 🩺\nI can help you book doctor appointments, check symptoms, and get basic medical guidance.\nHow can I assist you today? 🧑‍⚕️💬"
        mock_chatbot.thank_you_message = "✅ Thank you for using Meddybuddie! Your request has been noted, and we'll get back to you shortly.\nStay healthy, and remember — your health matters! 🌿💙"
        mock_chatbot.connected_account_display_name = "Kalpesh QA Rename"
        mock_chatbot.connected_account_id = 6
        mock_chatbot.trigger = "NEW_ENTITY"
        mock_chatbot.status = "ACTIVE"
        mock_chatbot.created_at = "2025-01-01T00:00:00"
        mock_chatbot.updated_at = "2025-01-01T00:00:00"
        
        # Mock multiple nodes with different types
        mock_nodes = []
        
        # Question node
        question_node = MagicMock()
        question_node.id = "node-1"
        question_node.node_id = "123333"
        question_node.name = "n1"
        question_node.type = "question"
        question_node.position_x = 123
        question_node.position_y = 444
        question_node.data = {
            "text": "Question to be ask {{1}}",
            "options": [
                {
                    "text": "what is your name",
                    "name": "opt1"
                }
            ],
            "isMedia": False,
            "dataVariable": "myvariable"
        }
        question_node.variable_mapping = [{}]
        mock_nodes.append(question_node)
        
        # Button node
        button_node = MagicMock()
        button_node.id = "node-2"
        button_node.node_id = "123334"
        button_node.name = "n2"
        button_node.type = "button"
        button_node.position_x = 123
        button_node.position_y = 444
        button_node.data = {
            "header": {
                "format": "text/image/video/document",
                "text": "ASDSAdasd",
                "mediaId": ""
            },
            "text": "Question to be ask {{}}",
            "footer": "footer value",
            "buttons": [
                {
                    "name": "opt1",
                    "text": "what is your name",
                    "position": 1
                }
            ]
        }
        button_node.variable_mapping = []
        mock_nodes.append(button_node)
        
        # List node
        list_node = MagicMock()
        list_node.id = "node-3"
        list_node.node_id = "123335"
        list_node.name = "n3"
        list_node.type = "list"
        list_node.position_x = 123
        list_node.position_y = 444
        list_node.data = {
            "header": "Question to be ask {{1}}",
            "body": "This is list variable {{2}}",
            "footer": "this is footer",
            "menuButton": "Clikck to view all",
            "sections": [
                {
                    "title": "adasda",
                    "rows": [
                        {
                            "id": "abc",
                            "text": "row title"
                        }
                    ]
                }
            ],
            "dataVariable": "myvariable"
        }
        list_node.variable_mapping = []
        mock_nodes.append(list_node)
        
        # SendMessage node
        send_message_node = MagicMock()
        send_message_node.id = "node-4"
        send_message_node.node_id = "123336"
        send_message_node.name = "n4"
        send_message_node.type = "sendMessage"
        send_message_node.position_x = 123
        send_message_node.position_y = 444
        send_message_node.data = {
            "options": [
                {
                    "type": "text",
                    "position": 12,
                    "text": "Question to be ask {{1}}"
                },
                {
                    "type": "media",
                    "position": 13,
                    "mediaFile": {
                        "fileId": 11,
                        "fileName": "myPhonto.png",
                        "fileSize": 111111,
                        "fileType": "png",
                        "fileCaption": "caption to show"
                    }
                }
            ]
        }
        send_message_node.variable_mapping = []
        mock_nodes.append(send_message_node)
        
        # Condition node
        condition_node = MagicMock()
        condition_node.id = "node-5"
        condition_node.node_id = "123337"
        condition_node.name = "n5"
        condition_node.type = "condition"
        condition_node.position_x = 123
        condition_node.position_y = 444
        condition_node.data = {
            "conditions": [
                {
                    "operator": "",
                    "operand1": "",
                    "operand2": ""
                },
                {
                    "operator": "",
                    "operand1": "",
                    "operand2": ""
                }
            ],
            "operator": "AND/OR"
        }
        condition_node.variable_mapping = []
        mock_nodes.append(condition_node)
        
        # Mock edges
        mock_edge = MagicMock()
        mock_edge.id = "edge-1"
        mock_edge.edge_id = "e1"
        mock_edge.source_node = "n1"
        mock_edge.source_handle = "h1"
        mock_edge.target_node = "n3"
        
        # Mock entity fields
        mock_entity_field = MagicMock()
        mock_entity_field.entity_type = "LEAD"
        mock_entity_field.field_id = 123
        mock_entity_field.name = "first_name"
        mock_entity_field.display_name = "First Name"
        mock_entity_field.standard = True
        
        # Mock database session and queries
        mock_db = MagicMock()
        
        # Mock the chatbot query
        mock_chatbot_query = MagicMock()
        mock_chatbot_query.filter.return_value.first.return_value = mock_chatbot
        
        # Mock the questions query
        mock_questions_query = MagicMock()
        mock_questions_query.filter.return_value.all.return_value = []
        
        # Mock the knowledgebase query
        mock_kb_query = MagicMock()
        mock_kb_query.filter.return_value.all.return_value = []
        
        # Mock the nodes query
        mock_nodes_query = MagicMock()
        mock_nodes_query.filter.return_value.all.return_value = mock_nodes
        
        # Mock the edges query
        mock_edges_query = MagicMock()
        mock_edges_query.filter.return_value.all.return_value = [mock_edge]
        
        # Mock the entity fields query
        mock_entity_fields_query = MagicMock()
        mock_entity_fields_query.filter.return_value.all.return_value = [mock_entity_field]
        
        # Set up the query side effects
        mock_db.query.side_effect = [
            mock_chatbot_query,  # First call for chatbot
            mock_questions_query,  # Second call for questions
            mock_kb_query,  # Third call for knowledgebase
            mock_nodes_query,  # Fourth call for nodes
            mock_edges_query,  # Fifth call for edges
            mock_entity_fields_query,  # Sixth call for entity fields
            mock_entity_fields_query,  # Additional calls for entity fields for each node
            mock_entity_fields_query,
            mock_entity_fields_query,
            mock_entity_fields_query,
            mock_entity_fields_query
        ]
        
        with patch('app.services.chatbot_service.next') as mock_next:
            mock_next.return_value = mock_db
            
            result = service.get_chatbot_with_details("97753c94-7333-45b3-b438-ce6992732f79", 2048)
            
            # Print the actual result for comparison
            print("\nActual Response:")
            print(json.dumps(result, indent=2))
            
            # Verify exact structure matches expected format
            expected_structure = {
                "id": str,
                "tenant_id": int,
                "name": str,
                "type": str,
                "description": str,
                "welcomeMessage": str,
                "thankYouMessage": str,
                "connectedAccount": dict,
                "trigger": str,
                "status": str,
                "questions": list,
                "knowledgebase": list,
                "knowledgebase_ids": list,
                "created_at": str,
                "updated_at": str,
                "nodes": list,
                "edges": list
            }
            
            # Verify top-level structure
            for key, expected_type in expected_structure.items():
                assert key in result, f"Missing key: {key}"
                assert isinstance(result[key], expected_type), f"Wrong type for {key}: expected {expected_type}, got {type(result[key])}"
            
            # Verify connected account structure
            assert "displayName" in result["connectedAccount"]
            assert "accountId" in result["connectedAccount"]
            
            # Verify nodes structure
            assert len(result["nodes"]) == 5
            for node in result["nodes"]:
                required_node_keys = ["id", "name", "type", "position", "data", "variableMapping"]
                for key in required_node_keys:
                    assert key in node, f"Missing node key: {key}"
                
                # Verify position structure
                assert "x" in node["position"]
                assert "y" in node["position"]
                assert isinstance(node["position"]["x"], (int, float))
                assert isinstance(node["position"]["y"], (int, float))
                
                # Verify data structure
                assert isinstance(node["data"], dict)
                
                # Verify variable mapping
                assert isinstance(node["variableMapping"], list)
            
            # Verify edges structure
            assert len(result["edges"]) == 1
            edge = result["edges"][0]
            required_edge_keys = ["id", "source", "sourceHandle", "target", "targetHandle"]
            for key in required_edge_keys:
                assert key in edge, f"Missing edge key: {key}"
            
            # Verify specific values match expected
            assert result["id"] == "97753c94-7333-45b3-b438-ce6992732f79"
            assert result["tenant_id"] == 2048
            assert result["type"] == "RULE"
            assert result["status"] == "ACTIVE"
            assert result["trigger"] == "NEW_ENTITY"
            
            # Verify node types
            node_types = [node["type"] for node in result["nodes"]]
            expected_types = ["question", "button", "list", "sendMessage", "condition"]
            assert set(node_types) == set(expected_types), f"Node types mismatch: {node_types} vs {expected_types}"
            
            print("✓ Exact JSON response format matches expected structure!")
            return True
            
    except Exception as e:
        print(f"✗ Exact JSON response format test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_prepare_conversation_completion_payload_success():
    """Test successful conversation completion payload preparation"""
    
    with patch('app.services.entity_field_service.EntityFieldService.get_entity_details') as mock_get_details:
        # Mock successful entity details retrieval
        mock_get_details.side_effect = [
            {"ownerId": 159, "entityName": "John Doe"},  # LEAD entity
            {"ownerId": 200, "entityName": "Jane Smith"}   # CONTACT entity
        ]
        
        chatbot_service = ChatbotService()
        
        entity_details = [
            {"id": 213452, "entityType": "LEAD"},
            {"id": 312662, "entityType": "CONTACT"}
        ]
        
        conversation_state = {
            "current_node": {
                "id": "n_pdf",
                "name": "send_pdf",
                "type": "sendMessage",
                "data": [
                    {
                        "type": "media",
                        "mediaFile": {
                            "fileId": 14,
                            "fileName": "Quotation_acne monty (1).pdf",
                            "fileSize": 51912,
                            "fileType": "document",
                            "fileCaption": "Sample Document"
                        }
                    }
                ]
            }
        }
        
        # Run the async method
        import asyncio
        result = asyncio.run(chatbot_service.prepare_conversation_completion_payload(
            conversation_id="f0b1474c-0f4a-4a62-8a75-d0f6161a72c0",
            entity_details=entity_details,
            conversation_state=conversation_state,
            token="test_token",
            completion_message="Conversation completed successfully",
            charge=0
        ))
        
        # Verify the result
        assert result["chatbotConversationId"] == "f0b1474c-0f4a-4a62-8a75-d0f6161a72c0"
        assert result["message"] == "Conversation completed successfully"
        assert result["completed"] is True
        assert result["charge"] == 0
        assert result["chatbotType"] == "RULE"
        
        # Check entity details
        assert len(result["entityDetails"]) == 2
        assert result["entityDetails"][0]["entityId"] == 213452
        assert result["entityDetails"][0]["entityType"] == "LEAD"
        assert result["entityDetails"][0]["ownerId"] == 159
        assert result["entityDetails"][1]["entityId"] == 312662
        assert result["entityDetails"][1]["entityType"] == "CONTACT"
        assert result["entityDetails"][1]["ownerId"] == 200
        
        # Check node details
        assert result["nodeDetails"]["id"] == "n_pdf"
        assert result["nodeDetails"]["name"] == "send_pdf"
        assert result["nodeDetails"]["type"] == "sendMessage"
        assert len(result["nodeDetails"]["data"]) == 1
        
        # Verify that get_entity_details was called for each entity
        assert mock_get_details.call_count == 2
        mock_get_details.assert_any_call("213452", "LEAD", "test_token")
        mock_get_details.assert_any_call("312662", "CONTACT", "test_token")
    
    print("✓ ChatbotService.prepare_conversation_completion_payload() - success test completed")

def test_prepare_conversation_completion_payload_no_owner_ids():
    """Test conversation completion payload preparation when owner IDs are not found"""
    
    with patch('app.services.entity_field_service.EntityFieldService.get_entity_details') as mock_get_details:
        # Mock entity details retrieval returning None values
        mock_get_details.return_value = {"ownerId": None, "entityName": None}
        
        chatbot_service = ChatbotService()
        
        entity_details = [
            {"id": 213452, "entityType": "LEAD"},
            {"id": 312662, "entityType": "CONTACT"}
        ]
        
        conversation_state = {
            "current_node": {
                "id": "n_pdf",
                "name": "send_pdf",
                "type": "sendMessage",
                "data": []
            }
        }
        
        # Run the async method
        import asyncio
        result = asyncio.run(chatbot_service.prepare_conversation_completion_payload(
            conversation_id="test-conversation-123",
            entity_details=entity_details,
            conversation_state=conversation_state,
            token="test_token",
            completion_message="Done",
            charge=1
        ))
        
        # Verify the result
        assert result["chatbotConversationId"] == "test-conversation-123"
        assert result["message"] == "Done"
        assert result["completed"] is True
        assert result["charge"] == 1
        assert result["chatbotType"] == "RULE"
        
        # Check entity details with None owner IDs
        assert len(result["entityDetails"]) == 2
        assert result["entityDetails"][0]["entityId"] == 213452
        assert result["entityDetails"][0]["entityType"] == "LEAD"
        assert result["entityDetails"][0]["ownerId"] is None
        assert result["entityDetails"][1]["entityId"] == 312662
        assert result["entityDetails"][1]["entityType"] == "CONTACT"
        assert result["entityDetails"][1]["ownerId"] is None
    
    print("✓ ChatbotService.prepare_conversation_completion_payload() - no owner IDs test completed")

def test_prepare_conversation_completion_payload_no_node_details():
    """Test conversation completion payload preparation without node details"""
    
    with patch('app.services.entity_field_service.EntityFieldService.get_entity_details') as mock_get_details:
        mock_get_details.return_value = {"ownerId": 159, "entityName": "John Doe"}
        
        chatbot_service = ChatbotService()
        
        entity_details = [
            {"id": 213452, "entityType": "LEAD"}
        ]
        
        conversation_state = {}  # No current_node
        
        # Run the async method
        import asyncio
        result = asyncio.run(chatbot_service.prepare_conversation_completion_payload(
            conversation_id="test-conversation-123",
            entity_details=entity_details,
            conversation_state=conversation_state,
            token="test_token",
            completion_message="Done",
            charge=1
        ))
        
        # Verify the result
        assert result["chatbotConversationId"] == "test-conversation-123"
        assert result["message"] == "Done"
        assert result["completed"] is True
        assert result["charge"] == 1
        assert result["chatbotType"] == "RULE"
        
        # Check entity details
        assert len(result["entityDetails"]) == 1
        assert result["entityDetails"][0]["entityId"] == 213452
        assert result["entityDetails"][0]["entityType"] == "LEAD"
        assert result["entityDetails"][0]["ownerId"] == 159
        
        # Check that node details are not included
        assert "nodeDetails" not in result
    
    print("✓ ChatbotService.prepare_conversation_completion_payload() - no node details test completed")

def test_prepare_conversation_completion_payload_empty_entities():
    """Test conversation completion payload preparation with empty entity details"""
    
    chatbot_service = ChatbotService()
    
    entity_details = []
    conversation_state = {
        "current_node": {
            "id": "n_pdf",
            "name": "send_pdf",
            "type": "sendMessage",
            "data": []
        }
    }
    
    # Run the async method
    import asyncio
    result = asyncio.run(chatbot_service.prepare_conversation_completion_payload(
        conversation_id="test-conversation-123",
        entity_details=entity_details,
        conversation_state=conversation_state,
        token="test_token",
        completion_message="Done",
        charge=1
    ))
    
    # Verify the result
    assert result["chatbotConversationId"] == "test-conversation-123"
    assert result["message"] == "Done"
    assert result["completed"] is True
    assert result["charge"] == 1
    assert result["chatbotType"] == "RULE"
    
    # Check that entity details is empty
    assert result["entityDetails"] == []
    
    # Check node details
    assert result["nodeDetails"]["id"] == "n_pdf"
    assert result["nodeDetails"]["name"] == "send_pdf"
    assert result["nodeDetails"]["type"] == "sendMessage"
    
    print("✓ ChatbotService.prepare_conversation_completion_payload() - empty entities test completed")

def test_prepare_conversation_completion_payload_exception():
    """Test conversation completion payload preparation when exception occurs"""
    
    with patch('app.services.entity_field_service.EntityFieldService.get_entity_details') as mock_get_details:
        # Mock exception in entity details retrieval
        mock_get_details.side_effect = Exception("API Error")
        
        chatbot_service = ChatbotService()
        
        entity_details = [
            {"id": 213452, "entityType": "LEAD"}
        ]
        
        conversation_state = {}
        
        # Run the async method
        import asyncio
        result = asyncio.run(chatbot_service.prepare_conversation_completion_payload(
            conversation_id="test-conversation-123",
            entity_details=entity_details,
            conversation_state=conversation_state,
            token="test_token",
            completion_message="Done",
            charge=1
        ))
        
        # Verify fallback result
        assert result["chatbotConversationId"] == "test-conversation-123"
        assert result["message"] == "Done"
        assert result["completed"] is True
        assert result["charge"] == 1
        assert result["chatbotType"] == "RULE"
        assert result["entityDetails"] == []
    
    print("✓ ChatbotService.prepare_conversation_completion_payload() - exception test completed")


# Tests for isFirstNode field changes
def test_get_chatbot_with_details_includes_isFirstNode_top_level():
    """Test that isFirstNode is included as top-level property in node response"""
    from unittest.mock import Mock
    
    chatbot_service = ChatbotService()
    tenant_id = 123
    chatbot_id = "test-chatbot-123"
    
    # Mock chatbot
    mock_chatbot = Mock()
    mock_chatbot.id = chatbot_id
    mock_chatbot.tenant_id = tenant_id
    mock_chatbot.name = "Test Chatbot"
    mock_chatbot.type = "RULE"
    mock_chatbot.description = "Test Description"
    mock_chatbot.welcome_message = "Welcome"
    mock_chatbot.thank_you_message = "Thank you"
    mock_chatbot.trigger = "NEW_ENTITY"
    mock_chatbot.status = "ACTIVE"
    mock_chatbot.created_at = "2025-01-01T00:00:00"
    mock_chatbot.updated_at = "2025-01-01T00:00:00"
    mock_chatbot.connected_account_display_name = "Test Account"
    mock_chatbot.connected_account_id = 456
    
    # Mock nodes with different isFirstNode values
    mock_node1 = Mock()
    mock_node1.node_id = "node1"
    mock_node1.name = "First Node"
    mock_node1.type = "question"
    mock_node1.position_x = 100.0
    mock_node1.position_y = 100.0
    mock_node1.is_first_node = True
    mock_node1.data = {
        "text": "Hello {{1}}",
        "options": [{"text": "Option 1", "name": "1"}],
        "uiIsfirstnode": True  # This should be removed
    }
    mock_node1.variable_mapping = []
    
    mock_node2 = Mock()
    mock_node2.node_id = "node2"
    mock_node2.name = "Second Node"
    mock_node2.type = "sendMessage"
    mock_node2.position_x = 200.0
    mock_node2.position_y = 200.0
    mock_node2.is_first_node = False
    mock_node2.data = {
        "text": "Goodbye",
        "uiIsfirstnode": False  # This should be removed
    }
    mock_node2.variable_mapping = []
    
    # Mock edges
    mock_edge = Mock()
    mock_edge.source_node = "node1"
    mock_edge.source_handle = "1"
    mock_edge.target_node = "node2"
    mock_edge.target_handle = None
    mock_edge.edge_id = "edge1"
    
    with patch('app.services.chatbot_service.get_db') as mock_get_db:
        # Mock database session
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot
        mock_db.query.return_value.filter.return_value.all.side_effect = [
            [],  # questions
            [],  # knowledgebase
            [mock_node1, mock_node2],  # nodes
            [mock_edge],  # edges
            [],  # node entity fields for node1
            []   # node entity fields for node2
        ]
        
        # Call the method
        result = chatbot_service.get_chatbot_with_details(chatbot_id, tenant_id)
        
        # Assertions
        assert "nodes" in result
        assert len(result["nodes"]) == 2
        
        # Check first node
        node1 = result["nodes"][0]
        assert node1["id"] == "node1"
        assert node1["isFirstNode"] is True  # Top-level property
        assert "isFirstNode" in node1["data"]  # Also in data
        assert node1["data"]["isFirstNode"] is True
        assert "uiIsfirstnode" not in node1["data"]  # Should be removed
        
        # Check second node
        node2 = result["nodes"][1]
        assert node2["id"] == "node2"
        assert node2["isFirstNode"] is False  # Top-level property
        assert "isFirstNode" in node2["data"]  # Also in data
        assert node2["data"]["isFirstNode"] is False
        assert "uiIsfirstnode" not in node2["data"]  # Should be removed
    
    print("✓ ChatbotService.get_chatbot_with_details() - isFirstNode top-level test completed")


def test_get_chatbot_with_details_removes_uiIsfirstnode():
    """Test that uiIsfirstnode field is removed from node data"""
    from unittest.mock import Mock
    
    chatbot_service = ChatbotService()
    tenant_id = 123
    chatbot_id = "test-chatbot-123"
    
    # Mock chatbot
    mock_chatbot = Mock()
    mock_chatbot.id = chatbot_id
    mock_chatbot.tenant_id = tenant_id
    mock_chatbot.name = "Test Chatbot"
    mock_chatbot.type = "RULE"
    mock_chatbot.description = "Test Description"
    mock_chatbot.welcome_message = "Welcome"
    mock_chatbot.thank_you_message = "Thank you"
    mock_chatbot.trigger = "NEW_ENTITY"
    mock_chatbot.status = "ACTIVE"
    mock_chatbot.created_at = "2025-01-01T00:00:00"
    mock_chatbot.updated_at = "2025-01-01T00:00:00"
    mock_chatbot.connected_account_display_name = "Test Account"
    mock_chatbot.connected_account_id = 456
    
    # Mock node with uiIsfirstnode
    mock_node = Mock()
    mock_node.node_id = "node1"
    mock_node.name = "Test Node"
    mock_node.type = "question"
    mock_node.position_x = 100.0
    mock_node.position_y = 100.0
    mock_node.is_first_node = True
    mock_node.data = {
        "text": "Hello",
        "uiIsfirstnode": True  # This should be removed
    }
    mock_node.variable_mapping = []
    
    with patch('app.services.chatbot_service.get_db') as mock_get_db:
        # Mock database session
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot
        mock_db.query.return_value.filter.return_value.all.side_effect = [
            [],  # questions
            [],  # knowledgebase
            [mock_node],  # nodes
            [],  # edges
            []   # node entity fields
        ]
        
        # Call the method
        result = chatbot_service.get_chatbot_with_details(chatbot_id, tenant_id)
        
        # Assertions
        node = result["nodes"][0]
        assert "uiIsfirstnode" not in node["data"]
        assert "isFirstNode" in node["data"]
        assert node["data"]["isFirstNode"] is True
    
    print("✓ ChatbotService.get_chatbot_with_details() - uiIsfirstnode removal test completed")


def test_get_chatbot_with_details_handles_null_data():
    """Test that method handles nodes with null data gracefully"""
    from unittest.mock import Mock
    
    chatbot_service = ChatbotService()
    tenant_id = 123
    chatbot_id = "test-chatbot-123"
    
    # Mock chatbot
    mock_chatbot = Mock()
    mock_chatbot.id = chatbot_id
    mock_chatbot.tenant_id = tenant_id
    mock_chatbot.name = "Test Chatbot"
    mock_chatbot.type = "RULE"
    mock_chatbot.description = "Test Description"
    mock_chatbot.welcome_message = "Welcome"
    mock_chatbot.thank_you_message = "Thank you"
    mock_chatbot.trigger = "NEW_ENTITY"
    mock_chatbot.status = "ACTIVE"
    mock_chatbot.created_at = "2025-01-01T00:00:00"
    mock_chatbot.updated_at = "2025-01-01T00:00:00"
    mock_chatbot.connected_account_display_name = "Test Account"
    mock_chatbot.connected_account_id = 456
    
    # Create node with null data
    mock_node_null_data = Mock()
    mock_node_null_data.node_id = "node_null"
    mock_node_null_data.name = "Null Data Node"
    mock_node_null_data.type = "question"
    mock_node_null_data.position_x = 100.0
    mock_node_null_data.position_y = 100.0
    mock_node_null_data.is_first_node = False
    mock_node_null_data.data = None  # Null data
    mock_node_null_data.variable_mapping = []
    
    with patch('app.services.chatbot_service.get_db') as mock_get_db:
        # Mock database session
        mock_db = Mock()
        mock_get_db.return_value.__next__.return_value = mock_db
        
        # Mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot
        mock_db.query.return_value.filter.return_value.all.side_effect = [
            [],  # questions
            [],  # knowledgebase
            [mock_node_null_data],  # nodes
            [],  # edges
            []   # node entity fields
        ]
        
        # Call the method
        result = chatbot_service.get_chatbot_with_details(chatbot_id, tenant_id)
        
        # Assertions
        node = result["nodes"][0]
        assert node["id"] == "node_null"
        assert node["isFirstNode"] is False  # Top-level property
        assert node["data"] == {"isFirstNode": False}  # Should have isFirstNode added
    
    print("✓ ChatbotService.get_chatbot_with_details() - null data handling test completed")

@pytest.mark.asyncio
async def test_prepare_conversation_completion_payload_with_entity_name():
    """Test conversation completion payload preparation with entityName field"""
    
    with patch('app.services.entity_field_service.EntityFieldService') as mock_entity_service_class:
        # Mock entity field service
        mock_service_instance = MagicMock()
        mock_entity_service_class.return_value = mock_service_instance
        
        # Mock entity details response with both ownerId and entityName
        mock_service_instance.get_entity_details.return_value = {
            "ownerId": 159,
            "entityName": "John Doe"
        }
        
        # Test data
        conversation_id = "test-conversation-123"
        entity_details = [
            {
                "id": "12345",
                "entityType": "LEAD"
            },
            {
                "id": "67890", 
                "entityType": "CONTACT"
            }
        ]
        conversation_state = {
            "current_node": {
                "id": "n1",
                "name": "Completion Node",
                "type": "sendMessage",
                "data": [{"type": "text", "text": "Thank you!"}]
            }
        }
        token = "test_token"
        completion_message = "Thank you for your time!"
        charge = 0
        tenant_id = 2048
        
        # Create service and call method
        chatbot_service = ChatbotService()
        result = await chatbot_service.prepare_conversation_completion_payload(
            conversation_id=conversation_id,
            entity_details=entity_details,
            conversation_state=conversation_state,
            token=token,
            completion_message=completion_message,
            charge=charge,
            tenant_id=tenant_id
        )
        
        # Verify result includes entityName
        assert result["entityDetails"][0]["entityName"] == "John Doe"
        assert result["entityDetails"][0]["ownerId"] == 159
        assert result["entityDetails"][1]["entityName"] == "John Doe"
        assert result["entityDetails"][1]["ownerId"] == 159
        assert result["tenantId"] == 2048
        assert mock_service_instance.get_entity_details.call_count == 2
    
    print("✓ ChatbotService.prepare_conversation_completion_payload() - with entityName test completed")

@pytest.mark.asyncio
async def test_prepare_conversation_completion_payload_entity_name_null():
    """Test conversation completion payload preparation when entityName is null"""
    
    with patch('app.services.entity_field_service.EntityFieldService') as mock_entity_service_class:
        # Mock entity field service
        mock_service_instance = MagicMock()
        mock_entity_service_class.return_value = mock_service_instance
        
        # Mock entity details response with null entityName
        mock_service_instance.get_entity_details.return_value = {
            "ownerId": 159,
            "entityName": None
        }
        
        # Test data
        conversation_id = "test-conversation-123"
        entity_details = [
            {
                "id": "12345",
                "entityType": "LEAD"
            }
        ]
        conversation_state = {}
        token = "test_token"
        completion_message = "Thank you for your time!"
        charge = 0
        tenant_id = 2048
        
        # Create service and call method
        chatbot_service = ChatbotService()
        result = await chatbot_service.prepare_conversation_completion_payload(
            conversation_id=conversation_id,
            entity_details=entity_details,
            conversation_state=conversation_state,
            token=token,
            completion_message=completion_message,
            charge=charge,
            tenant_id=tenant_id
        )
        
        # Verify result with null entityName
        assert result["entityDetails"][0]["entityName"] is None
        assert result["entityDetails"][0]["ownerId"] == 159
    
    print("✓ ChatbotService.prepare_conversation_completion_payload() - entityName null test completed")

@pytest.mark.asyncio
async def test_prepare_conversation_completion_payload_single_api_call():
    """Test that prepare_conversation_completion_payload uses single API call per entity"""
    
    with patch('app.services.entity_field_service.EntityFieldService') as mock_entity_service_class:
        # Mock entity field service
        mock_service_instance = MagicMock()
        mock_entity_service_class.return_value = mock_service_instance
        
        # Mock entity details response
        mock_service_instance.get_entity_details.return_value = {
            "ownerId": 159,
            "entityName": "John Doe"
        }
        
        # Test data with 3 entities
        conversation_id = "test-conversation-123"
        entity_details = [
            {"id": "12345", "entityType": "LEAD"},
            {"id": "67890", "entityType": "CONTACT"},
            {"id": "11111", "entityType": "LEAD"}
        ]
        conversation_state = {}
        token = "test_token"
        completion_message = "Thank you for your time!"
        charge = 0
        tenant_id = 2048
        
        # Create service and call method
        chatbot_service = ChatbotService()
        result = await chatbot_service.prepare_conversation_completion_payload(
            conversation_id=conversation_id,
            entity_details=entity_details,
            conversation_state=conversation_state,
            token=token,
            completion_message=completion_message,
            charge=charge,
            tenant_id=tenant_id
        )
        
        # Verify that get_entity_details was called exactly 3 times (once per entity)
        assert mock_service_instance.get_entity_details.call_count == 3
        
        # Verify all entities have both ownerId and entityName
        for entity_detail in result["entityDetails"]:
            assert "ownerId" in entity_detail
            assert "entityName" in entity_detail
    
    print("✓ ChatbotService.prepare_conversation_completion_payload() - single API call test completed")


async def run_async_tests():
    """Run async test functions"""
    import asyncio
    
    # Run new entityName and optimization tests
    await test_prepare_conversation_completion_payload_with_entity_name()
    await test_prepare_conversation_completion_payload_entity_name_null()
    await test_prepare_conversation_completion_payload_single_api_call()
    
    print("\n🎉 All ChatbotService payload preparation tests completed!")

def test_start_conversation_chaining_variable_scope():
    """Test that the variable scoping fix allows sendMessage chaining to work properly"""
    from app.services.chatbot_service import ChatbotService
    from unittest.mock import Mock, patch, AsyncMock
    
    chatbot_service = ChatbotService()
    
    # Create mock chatbot
    mock_chatbot = Mock()
    mock_chatbot.id = "test-chatbot"
    mock_chatbot.type = "RULE"
    mock_chatbot.name = "Test Chatbot"
    
    # Create mock start node
    mock_start_node = Mock()
    mock_start_node.node_id = "start-node-1"
    mock_start_node.type = "sendMessage"
    mock_start_node.is_first_node = True
    mock_start_node.data = {"options": [{"type": "text", "text": "Hello"}]}
    mock_start_node.variable_mapping = []
    
    # Test that chaining_start_node is properly identified
    with patch('app.services.chatbot_service.get_db') as mock_get_db, \
         patch('app.services.chatbot_service.conversation_event_publisher') as mock_publisher:
        
        mock_db = Mock()
        mock_get_db.return_value = iter([mock_db])
        
        # Mock the query to return our start node when searching by is_first_node
        def mock_query(model):
            query_mock = Mock()
            filter_mock = Mock()
            query_mock.filter.return_value = filter_mock
            
            if hasattr(model, '__name__') and model.__name__ == 'ChatbotNode':
                filter_mock.first.return_value = mock_start_node
                filter_mock.count.return_value = 1
            else:
                filter_mock.all.return_value = []
                filter_mock.count.return_value = 0
            
            return query_mock
        
        mock_db.query.side_effect = mock_query
        mock_publisher.publish_conversation_response = AsyncMock()
        
        # This should not raise an exception and should properly identify the chaining_start_node
        try:
            import asyncio
            result = asyncio.run(chatbot_service.publish_start_conversation_event(
                chatbot=mock_chatbot,
                tenant_id=123,
                conversation_id="test-conv",
                welcome_message="Welcome",
                first_question={"question": None},
                charge=0,
                message_conversation_id=456,
                db=mock_db,
                entity_details=[],
                auth_token="test-token"
            ))
            # If we get here without exception, the variable scoping is working
            assert result is True
        except Exception as e:
            # If there's an exception related to variable scoping, the test should fail
            if "start_node" in str(e) or "NameError" in str(type(e)):
                assert False, f"Variable scoping issue detected: {e}"
            # Other exceptions might be expected due to mocking limitations
            pass


def test_build_start_conversation_node_details_list():
    """Test building start conversation node details for list nodes"""
    from app.services.chatbot_service import ChatbotService
    from unittest.mock import Mock
    
    chatbot_service = ChatbotService()
    
    # Create mock list node
    mock_node = Mock()
    mock_node.type = "list"
    mock_node.node_id = "list-1"
    mock_node.data = {
        "header": "Choose an option",
        "body": "Please select from the list below",
        "footer": "Thank you for your selection",
        "menuButton": "View All",
        "sections": [
            {
                "title": "Main Options",
                "rows": [
                    {
                        "id": "option1",
                        "text": "First Option"
                    },
                    {
                        "id": "option2",
                        "text": "Second Option"
                    }
                ]
            }
        ]
    }
    
    # Test building node details
    from app.services.entity_field_service import EntityFieldService
    entity_field_service = EntityFieldService()
    node_details = chatbot_service._build_start_conversation_node_details(mock_node, {}, entity_field_service, [], "dummy_token")
    
    assert node_details is not None
    assert node_details["id"] == "list-1"
    assert node_details["type"] == "list"
    
    data = node_details["data"]
    assert data["header"] == "Choose an option"
    assert data["body"] == "Please select from the list below"
    assert data["footer"] == "Thank you for your selection"
    assert data["menuButton"] == "View All"
    
    sections = data["sections"]
    assert len(sections) == 1
    assert sections[0]["title"] == "Main Options"
    assert len(sections[0]["rows"]) == 2
    
    # Check first row
    first_row = sections[0]["rows"][0]
    assert first_row["id"] == "option1"
    assert first_row["text"] == "First Option"
    
    # Check second row
    second_row = sections[0]["rows"][1]
    assert second_row["id"] == "option2"
    assert second_row["text"] == "Second Option"
    
    print("✅ List node start conversation details test passed")


def test_build_start_conversation_node_details_list_with_variables():
    """Test building start conversation node details for list nodes with variable substitution"""
    from app.services.chatbot_service import ChatbotService
    from unittest.mock import Mock, patch
    
    chatbot_service = ChatbotService()
    
    # Create mock list node with variables
    mock_node = Mock()
    mock_node.type = "list"
    mock_node.node_id = "list-1"
    mock_node.data = {
        "header": "Question to be ask {{1}}",
        "body": "This is list variable {{2}}",
        "footer": "this is footer",
        "menuButton": "Click to view all",
        "sections": [
            {
                "title": "adasda",
                "rows": [
                    {
                        "id": "abc",
                        "text": "row title"
                    }
                ]
            }
        ]
    }
    
    field_values = {
        "{{1}}": "John",
        "{{2}}": "Doe"
    }
    
    # Mock the entity field service
    with patch('app.services.chatbot_service.EntityFieldService') as mock_efs:
        mock_efs_instance = Mock()
        mock_efs.return_value = mock_efs_instance
        mock_efs_instance.substitute_node_variables.return_value = {
            "header": "Question to be ask John",
            "body": "This is list variable Doe",
            "footer": "this is footer",
            "menuButton": "Click to view all",
            "sections": mock_node.data["sections"]
        }
        
        # Test building node details with variable substitution
        from app.services.entity_field_service import EntityFieldService
        entity_field_service = EntityFieldService()
        node_details = chatbot_service._build_start_conversation_node_details(mock_node, field_values, entity_field_service, [], "dummy_token")
        
        assert node_details is not None
        data = node_details["data"]
        assert data["header"] == "Question to be ask John"
        assert data["body"] == "This is list variable Doe"
        
        # Verify that variable substitution was called
        mock_efs_instance.substitute_node_variables.assert_called_once()
        
        print("✅ List node with variable substitution test passed")


def test_build_start_conversation_node_details_list_empty_sections():
    """Test building start conversation node details for list nodes with empty sections"""
    from app.services.chatbot_service import ChatbotService
    from unittest.mock import Mock
    
    chatbot_service = ChatbotService()
    
    # Create mock list node with empty sections
    mock_node = Mock()
    mock_node.type = "list"
    mock_node.node_id = "empty-list"
    mock_node.data = {
        "header": "Empty list",
        "body": "No options available",
        "footer": "Thank you",
        "menuButton": "View All",
        "sections": []
    }
    
    # Test building node details
    from app.services.entity_field_service import EntityFieldService
    entity_field_service = EntityFieldService()
    node_details = chatbot_service._build_start_conversation_node_details(mock_node, {}, entity_field_service, [], "dummy_token")
    
    assert node_details is not None
    data = node_details["data"]
    assert data["sections"] == []
    assert data["header"] == "Empty list"
    assert data["body"] == "No options available"
    
    print("✅ List node with empty sections test passed")


class TestChatbotEntitiesField:
    """Test cases for chatbot entities field (SD-26938)"""
    
    @pytest.mark.asyncio
    async def test_create_chatbot_with_entities(self):
        """Test creating a chatbot with entities configuration"""
        print("🧪 Testing create chatbot with entities...")
        
        from app.models import ChatbotCreate, ConnectedAccount, EntityConfig
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        from unittest.mock import Mock, AsyncMock, patch
        
        chatbot_service = ChatbotService()
        
        # Create test data with entities
        entities = [
            EntityConfig(entity="LEAD"),
            EntityConfig(entity="CONTACT")
        ]
        
        chatbot_data = ChatbotCreate(
            name="Test Chatbot with Entities",
            type="RULE",
            description="Test description",
            connectedAccount=ConnectedAccount(displayName="Test Account", accountId=1),
            trigger="NEW_ENTITY",
            entities=entities
        )
        
        # Mock database
        mock_db = Mock()
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "test-id-123"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.name = chatbot_data.name
        mock_chatbot.type = "RULE"
        mock_chatbot.entities = [{"entity": "LEAD"}, {"entity": "CONTACT"}]
        mock_chatbot.trigger = "NEW_ENTITY"
        mock_chatbot.connected_account_display_name = "Test Account"
        mock_chatbot.connected_account_id = 1
        mock_chatbot.status = "DRAFT"
        mock_chatbot.created_at = "2025-10-11T12:00:00Z"
        mock_chatbot.welcome_message = None
        mock_chatbot.thank_you_message = None
        mock_chatbot.description = "Test description"
        
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db, \
             patch('app.services.chatbot_service.user_service.validate_and_get_user') as mock_validate_user, \
             patch.object(chatbot_service, 'validate_connected_account', new_callable=AsyncMock) as mock_validate_account, \
             patch.object(chatbot_service, 'get_chatbot_count_by_tenant', return_value=0):
            
            mock_get_db.return_value = iter([mock_db])
            mock_validate_user.return_value = Mock(id="user-123", name="Test User")
            mock_validate_account.return_value = {"displayName": "Test Account", "active": True}
            
            # Call create_chatbot
            with patch('app.models.Chatbot', return_value=mock_chatbot):
                result = await chatbot_service.create_chatbot(
                    chatbot_data, 
                    tenant_id=2048,
                    user_id="user-123",
                    token="test-token",
                    auth_context=None
                )
            
            # Verify entities are in response
            assert result["entities"] == [{"entity": "LEAD"}, {"entity": "CONTACT"}]
            print("✅ Test passed: Chatbot created with entities")
    
    @pytest.mark.asyncio
    async def test_update_chatbot_entities(self):
        """Test updating chatbot entities configuration"""
        print("🧪 Testing update chatbot entities...")
        
        from app.models import ChatbotUpdate, EntityConfig
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        from unittest.mock import Mock, AsyncMock, patch
        
        chatbot_service = ChatbotService()
        
        # Create update data with new entities
        new_entities = [
            EntityConfig(entity="DEAL")
        ]
        
        update_data = ChatbotUpdate(
            entities=new_entities
        )
        
        # Mock existing chatbot
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "test-id-123"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.entities = [{"entity": "LEAD"}]  # Old entities
        mock_chatbot.name = "Test Chatbot"
        mock_chatbot.description = "Test"
        mock_chatbot.welcome_message = None
        mock_chatbot.thank_you_message = None
        mock_chatbot.connected_account_display_name = "Test Account"
        mock_chatbot.connected_account_id = 1
        mock_chatbot.trigger = "NEW_ENTITY"
        mock_chatbot.updated_at = "2025-10-11T12:00:00Z"
        
        # Mock database
        mock_db = Mock()
        
        # Mock chatbot query
        mock_chatbot_filter = Mock()
        mock_chatbot_filter.first.return_value = mock_chatbot
        
        # Mock questions query
        mock_questions_filter = Mock()
        mock_questions_filter.all.return_value = []
        
        # Mock knowledgebase query
        mock_kb_filter = Mock()
        mock_kb_filter.all.return_value = []
        
        def query_side_effect(model):
            from app.models import Chatbot, ChatbotQuestion, ChatbotKnowledgebase
            q = Mock()
            if model == Chatbot:
                q.filter.return_value = mock_chatbot_filter
            elif model == ChatbotQuestion:
                q.filter.return_value = mock_questions_filter
            elif model == ChatbotKnowledgebase:
                q.filter.return_value = mock_kb_filter
            else:
                # For other models, return empty
                q.filter.return_value.all.return_value = []
                q.filter.return_value.first.return_value = None
            return q
        
        mock_db.query = Mock(side_effect=query_side_effect)
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db, \
             patch('app.services.chatbot_service.user_service.validate_and_get_user') as mock_validate_user:
            
            mock_get_db.return_value = iter([mock_db])
            mock_validate_user.return_value = Mock(id="user-123", name="Test User")
            
            # Call update_chatbot
            result = await chatbot_service.update_chatbot(
                chatbot_id="test-id-123",
                chatbot_data=update_data,
                tenant_id=2048,
                user_id="user-123",
                token="test-token",
                auth_context=None
            )
            
            # Verify entities were updated on the chatbot object
            assert mock_chatbot.entities == [{"entity": "DEAL"}]
            
            # Verify entities are returned in response
            assert "entities" in result
            assert result["entities"] == [{"entity": "DEAL"}]
            
            print("✅ Test passed: Chatbot entities updated and returned in response")
    
    @pytest.mark.asyncio
    async def test_entities_field_serialization(self):
        """Test that entities field is properly serialized and deserialized"""
        print("🧪 Testing entities field serialization...")
        
        from app.models import EntityConfig
        
        # Test EntityConfig model
        entity = EntityConfig(entity="LEAD")
        
        # Serialize to dict
        entity_dict = entity.model_dump()
        
        assert entity_dict == {"entity": "LEAD"}
        print("✅ Test passed: EntityConfig serialization works")
    
    @pytest.mark.asyncio
    async def test_create_chatbot_without_entities(self):
        """Test creating a chatbot without entities (backward compatibility)"""
        print("🧪 Testing create chatbot without entities...")
        
        from app.models import ChatbotCreate, ConnectedAccount
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        from unittest.mock import Mock, AsyncMock, patch
        
        chatbot_service = ChatbotService()
        
        # Create test data WITHOUT entities
        chatbot_data = ChatbotCreate(
            name="Test Chatbot No Entities",
            type="RULE",
            description="Test",
            connectedAccount=ConnectedAccount(displayName="Test Account", accountId=1),
            trigger="NEW_ENTITY"
            # No entities field
        )
        
        # Mock database
        mock_db = Mock()
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "test-id-456"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.name = chatbot_data.name
        mock_chatbot.type = "RULE"
        mock_chatbot.entities = None  # Should be None when not provided
        mock_chatbot.trigger = "NEW_ENTITY"
        mock_chatbot.connected_account_display_name = "Test Account"
        mock_chatbot.connected_account_id = 1
        mock_chatbot.status = "DRAFT"
        mock_chatbot.created_at = "2025-10-11T12:00:00Z"
        mock_chatbot.welcome_message = None
        mock_chatbot.thank_you_message = None
        mock_chatbot.description = "Test"
        
        mock_db.add = Mock()
        mock_db.commit = Mock()
        mock_db.refresh = Mock()
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db, \
             patch('app.services.chatbot_service.user_service.validate_and_get_user') as mock_validate_user, \
             patch.object(chatbot_service, 'validate_connected_account', new_callable=AsyncMock) as mock_validate_account, \
             patch.object(chatbot_service, 'get_chatbot_count_by_tenant', return_value=0):
            
            mock_get_db.return_value = iter([mock_db])
            mock_validate_user.return_value = Mock(id="user-123", name="Test User")
            mock_validate_account.return_value = {"displayName": "Test Account", "active": True}
            
            # Call create_chatbot
            with patch('app.models.Chatbot', return_value=mock_chatbot):
                result = await chatbot_service.create_chatbot(
                    chatbot_data, 
                    tenant_id=2048,
                    user_id="user-123",
                    token="test-token",
                    auth_context=None
                )
            
            # Verify entities is None (not provided)
            assert result["entities"] is None
            print("✅ Test passed: Chatbot created without entities (backward compatible)")
    
    @pytest.mark.asyncio
    async def test_entities_field_validation(self):
        """Test entities field validation in Pydantic models"""
        print("🧪 Testing entities field validation...")
        
        from app.models import EntityConfig
        from pydantic import ValidationError
        
        # Valid entity config
        valid_entity = EntityConfig(entity="LEAD")
        assert valid_entity.entity == "LEAD"
        
        # Test that missing fields raise validation error
        try:
            invalid_entity = EntityConfig()  # Missing 'entity'
            assert False, "Should have raised ValidationError"
        except (ValidationError, TypeError):
            print("✅ Validation error raised for missing 'entity' field")
        
        # Test that extra fields are allowed/ignored
        entity_with_extra = EntityConfig(entity="CONTACT", extra_field="ignored")
        assert entity_with_extra.entity == "CONTACT"
        
        print("✅ Test passed: Entity field validation works correctly")
    
    @pytest.mark.asyncio
    async def test_multiple_entities_configuration(self):
        """Test chatbot with multiple entities"""
        print("🧪 Testing multiple entities configuration...")
        
        from app.models import EntityConfig
        
        # Create multiple entities
        entities = [
            EntityConfig(entity="LEAD"),
            EntityConfig(entity="CONTACT"),
            EntityConfig(entity="DEAL"),
            EntityConfig(entity="COMPANY")
        ]
        
        # Serialize to dict (as it would be stored in DB)
        entities_json = [entity.model_dump() for entity in entities]
        
        assert len(entities_json) == 4
        assert entities_json[0] == {"entity": "LEAD"}
        assert entities_json[1] == {"entity": "CONTACT"}
        assert entities_json[2] == {"entity": "DEAL"}
        assert entities_json[3] == {"entity": "COMPANY"}
        
        print("✅ Test passed: Multiple entities can be configured")
    
    @pytest.mark.asyncio
    async def test_entities_in_get_chatbot_response(self):
        """Test that entities are included in get chatbot response"""
        print("🧪 Testing entities in get chatbot response...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot, ChatbotQuestion, ChatbotKnowledgebase
        from unittest.mock import Mock, patch
        
        chatbot_service = ChatbotService()
        
        # Mock chatbot with entities
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "test-id-789"
        mock_chatbot.tenant_id = 2048
        mock_chatbot.name = "Test Chatbot"
        mock_chatbot.type = "RULE"
        mock_chatbot.entities = [
            {"entity": "LEAD"},
            {"entity": "CONTACT"}
        ]
        mock_chatbot.trigger = "NEW_ENTITY"
        mock_chatbot.connected_account_display_name = "Test"
        mock_chatbot.connected_account_id = 1
        mock_chatbot.status = "ACTIVE"
        mock_chatbot.created_at = "2025-10-11T12:00:00Z"
        mock_chatbot.updated_at = "2025-10-11T12:00:00Z"
        mock_chatbot.welcome_message = None
        mock_chatbot.thank_you_message = None
        mock_chatbot.description = "Test"
        
        # Mock database
        mock_db = Mock()
        mock_query = Mock()
        
        # Mock chatbot query
        mock_chatbot_filter = Mock()
        mock_chatbot_filter.first.return_value = mock_chatbot
        
        # Mock questions query
        mock_questions_filter = Mock()
        mock_questions_filter.all.return_value = []
        
        # Mock knowledgebase query
        mock_kb_filter = Mock()
        mock_kb_filter.all.return_value = []
        
        # Mock nodes query
        mock_nodes_filter = Mock()
        mock_nodes_filter.all.return_value = []
        
        # Mock edges query
        mock_edges_filter = Mock()
        mock_edges_filter.all.return_value = []
        
        def query_side_effect(model):
            q = Mock()
            if model == Chatbot:
                q.filter.return_value = mock_chatbot_filter
            elif model == ChatbotQuestion:
                q.filter.return_value = mock_questions_filter
            elif model == ChatbotKnowledgebase:
                q.filter.return_value = mock_kb_filter
            else:
                # For nodes, edges, etc.
                q.filter.return_value = mock_nodes_filter if 'Node' in str(model) else mock_edges_filter
            return q
        
        mock_db.query.side_effect = query_side_effect
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db:
            mock_get_db.return_value = iter([mock_db])
            
            # Call get_chatbot_with_details
            result = chatbot_service.get_chatbot_with_details(
                chatbot_id="test-id-789",
                tenant_id=2048
            )
        
        # Verify entities are in response
        assert "entities" in result
        assert result["entities"] == [{"entity": "LEAD"}, {"entity": "CONTACT"}]
        
        print("✅ Test passed: Entities included in get chatbot response")
    
    @pytest.mark.asyncio
    async def test_entities_in_list_chatbots_response(self):
        """Test that entities are included in list chatbots response"""
        print("🧪 Testing entities in list chatbots response...")
        
        from app.services.chatbot_service import ChatbotService
        from app.models import Chatbot
        from unittest.mock import Mock, patch
        
        chatbot_service = ChatbotService()
        
        # Mock chatbots with different entities configurations
        mock_chatbot1 = Mock(spec=Chatbot)
        mock_chatbot1.id = "chatbot-1"
        mock_chatbot1.name = "Chatbot 1"
        mock_chatbot1.type = "RULE"
        mock_chatbot1.entities = [{"entity": "LEAD"}]
        mock_chatbot1.status = "ACTIVE"
        mock_chatbot1.created_by = "user-1"
        mock_chatbot1.updated_by = "user-1"
        mock_chatbot1.created_at = "2025-10-11T12:00:00Z"
        mock_chatbot1.updated_at = "2025-10-11T12:00:00Z"
        mock_chatbot1.connected_account_display_name = "Account 1"
        mock_chatbot1.connected_account_id = 1
        mock_chatbot1.trigger = "NEW_ENTITY"
        mock_chatbot1.welcome_message = None
        mock_chatbot1.thank_you_message = None
        mock_chatbot1.description = "Test 1"
        
        mock_chatbot2 = Mock(spec=Chatbot)
        mock_chatbot2.id = "chatbot-2"
        mock_chatbot2.name = "Chatbot 2"
        mock_chatbot2.type = "AI"
        mock_chatbot2.entities = None  # No entities
        mock_chatbot2.status = "DRAFT"
        mock_chatbot2.created_by = "user-2"
        mock_chatbot2.updated_by = "user-2"
        mock_chatbot2.created_at = "2025-10-11T13:00:00Z"
        mock_chatbot2.updated_at = "2025-10-11T13:00:00Z"
        mock_chatbot2.connected_account_display_name = "Account 2"
        mock_chatbot2.connected_account_id = 2
        mock_chatbot2.trigger = None
        mock_chatbot2.welcome_message = "Welcome"
        mock_chatbot2.thank_you_message = "Thank you"
        mock_chatbot2.description = "Test 2"
        
        # Mock database
        mock_db = Mock()
        mock_query = Mock()
        mock_filter = Mock()
        mock_order_by = Mock()
        mock_order_by.all.return_value = [mock_chatbot1, mock_chatbot2]
        mock_filter.order_by.return_value = mock_order_by
        mock_query.filter.return_value = mock_filter
        mock_db.query.return_value = mock_query
        
        # Mock question and kb count queries
        def query_count_side_effect(*args, **kwargs):
            count_query = Mock()
            count_query.filter.return_value.count.return_value = 0
            return count_query
        
        with patch('app.services.chatbot_service.get_db') as mock_get_db, \
             patch('app.services.chatbot_service.user_service.get_users_by_ids', return_value={}):
            
            mock_get_db.return_value = iter([mock_db])
            
            # Override the query method to handle count queries
            original_query = mock_db.query
            def custom_query(model):
                if model == Chatbot:
                    return original_query(model)
                # For ChatbotQuestion and ChatbotKnowledgebase count queries
                count_mock = Mock()
                count_mock.filter.return_value.count.return_value = 0
                return count_mock
            
            mock_db.query = custom_query
            
            # Call list_chatbots
            result = chatbot_service.list_chatbots(tenant_id=2048, include_draft=True)
        
        # Verify both chatbots have entities field
        assert len(result) == 2
        assert result[0]["entities"] == [{"entity": "LEAD"}]
        assert result[1]["entities"] is None
        
        print("✅ Test passed: Entities included in list chatbots response")
    
    @pytest.mark.asyncio
    async def test_entities_field_empty_list(self):
        """Test chatbot with empty entities list"""
        print("🧪 Testing chatbot with empty entities list...")
        
        from app.models import ChatbotCreate, ConnectedAccount
        
        # Create chatbot with empty entities list
        chatbot_data = ChatbotCreate(
            name="Test Chatbot",
            type="RULE",
            connectedAccount=ConnectedAccount(displayName="Test", accountId=1),
            entities=[]  # Empty list
        )
        
        # Should accept empty list
        assert chatbot_data.entities == []
        
        # Serialize
        entities_json = [entity.model_dump() for entity in chatbot_data.entities]
        assert entities_json == []
        
        print("✅ Test passed: Empty entities list handled correctly")


class TestChatbotDeactivationEvents:
    """Test chatbot deactivation event functionality"""

    @pytest.mark.asyncio
    @patch('app.services.chatbot_service.get_db')
    @patch('app.services.chatbot_service.user_service')
    async def test_chatbot_service_deactivation_last_active(self, mock_user_service, mock_get_db):
        """Test ChatbotService publishes correct event when deactivating last active chatbot"""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = iter([mock_db])
        mock_user_service.get_user_by_id.return_value = {"name": "Test User"}

        # Create mock chatbot
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "test-chatbot-123"
        mock_chatbot.name = "Test Chatbot"
        mock_chatbot.status = "ACTIVE"
        mock_chatbot.connected_account_id = 456
        mock_chatbot.connected_account_display_name = "Test Account"
        mock_chatbot.tenant_id = 789

        # Mock database query to return the chatbot
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot

        # Mock the count query to return 0 (this is the last active chatbot)
        mock_db.query.return_value.filter.return_value.count.return_value = 0

        # Mock event publisher
        with patch('app.services.chatbot_service.ChatbotEventPublisher') as mock_publisher_class:
            mock_publisher = Mock()
            mock_publisher_class.return_value = mock_publisher
            mock_publisher.ensure_exchange_exists = AsyncMock()
            mock_publisher.publish_chatbot_deactivated_event = AsyncMock(return_value=True)
            mock_publisher.publish_status_updated_event = AsyncMock(return_value=True)

            # Test the service
            service = ChatbotService()
            result = await service.update_chatbot_status(
                chatbot_id="test-chatbot-123",
                new_status="INACTIVE",
                tenant_id=789,
                user_id="user-123",
                token="test-token"
            )

            # Verify the regular status updated event was published (for last active)
            mock_publisher.publish_status_updated_event.assert_called_once_with(
                status="INACTIVE",
                connected_account_id=456,
                connected_account_name="Test Account",
                chatbot_id="test-chatbot-123",
                tenant_id=789
            )

            # Verify chatbot deactivated event was NOT called (we use regular status update now)
            mock_publisher.publish_chatbot_deactivated_event.assert_not_called()

            # Verify response
            assert result["status"] == "INACTIVE"
            assert result["id"] == "test-chatbot-123"

    @pytest.mark.asyncio
    @patch('app.services.chatbot_service.get_db')
    @patch('app.services.chatbot_service.user_service')
    async def test_chatbot_service_deactivation_not_last_active_no_event(self, mock_user_service, mock_get_db):
        """Test ChatbotService does NOT publish any event when deactivating non-last active chatbot"""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = iter([mock_db])
        mock_user_service.get_user_by_id.return_value = {"name": "Test User"}

        # Create mock chatbot
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "test-chatbot-123"
        mock_chatbot.name = "Test Chatbot"
        mock_chatbot.status = "ACTIVE"
        mock_chatbot.connected_account_id = 456
        mock_chatbot.connected_account_display_name = "Test Account"
        mock_chatbot.tenant_id = 789

        # Mock database query to return the chatbot
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot

        # Mock the count query to return 1 (there are other active chatbots)
        mock_db.query.return_value.filter.return_value.count.return_value = 1

        # Mock event publisher
        with patch('app.services.chatbot_service.ChatbotEventPublisher') as mock_publisher_class:
            mock_publisher = Mock()
            mock_publisher_class.return_value = mock_publisher
            mock_publisher.ensure_exchange_exists = AsyncMock()
            mock_publisher.publish_chatbot_deactivated_event = AsyncMock(return_value=True)
            mock_publisher.publish_status_updated_event = AsyncMock(return_value=True)

            # Test the service
            service = ChatbotService()
            result = await service.update_chatbot_status(
                chatbot_id="test-chatbot-123",
                new_status="INACTIVE",
                tenant_id=789,
                user_id="user-123",
                token="test-token"
            )

            # Verify NO events were published for non-last active chatbot deactivation
            mock_publisher.publish_status_updated_event.assert_not_called()
            mock_publisher.publish_chatbot_deactivated_event.assert_not_called()

            # Verify response
            assert result["status"] == "INACTIVE"
            assert result["id"] == "test-chatbot-123"

    @pytest.mark.asyncio
    @patch('app.services.chatbot_service.get_db')
    @patch('app.services.chatbot_service.user_service')
    async def test_chatbot_service_activation_always_publishes_event(self, mock_user_service, mock_get_db):
        """Test ChatbotService always publishes event when activating any chatbot"""
        # Setup mocks
        mock_db = Mock()
        mock_get_db.return_value = iter([mock_db])
        mock_user_service.get_user_by_id.return_value = {"name": "Test User"}

        # Create mock chatbot
        mock_chatbot = Mock(spec=Chatbot)
        mock_chatbot.id = "test-chatbot-123"
        mock_chatbot.name = "Test Chatbot"
        mock_chatbot.status = "DRAFT"
        mock_chatbot.connected_account_id = 456
        mock_chatbot.connected_account_display_name = "Test Account"
        mock_chatbot.tenant_id = 789

        # Mock database query to return the chatbot
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chatbot

        # Mock event publisher
        with patch('app.services.chatbot_service.ChatbotEventPublisher') as mock_publisher_class:
            mock_publisher = Mock()
            mock_publisher_class.return_value = mock_publisher
            mock_publisher.ensure_exchange_exists = AsyncMock()
            mock_publisher.publish_status_updated_event = AsyncMock(return_value=True)

            # Test the service
            service = ChatbotService()
            result = await service.update_chatbot_status(
                chatbot_id="test-chatbot-123",
                new_status="ACTIVE",
                tenant_id=789,
                user_id="user-123",
                token="test-token"
            )

            # Verify the activation event was published
            mock_publisher.publish_status_updated_event.assert_called_once_with(
                status="ACTIVE",
                connected_account_id=456,
                connected_account_name="Test Account",
                chatbot_id="test-chatbot-123",
                tenant_id=789
            )

            # Verify response
            assert result["status"] == "ACTIVE"
            assert result["id"] == "test-chatbot-123"


if __name__ == "__main__":
    import asyncio
    
    main()
    
    # Run async tests
    asyncio.run(run_async_tests())
