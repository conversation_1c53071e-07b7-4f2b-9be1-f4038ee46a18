#!/usr/bin/env python3
"""
Test cases for ConversationEventPublisher
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from app.services.conversation_event_publisher import ConversationEventPublisher


class TestConversationEventPublisher:
    """Test cases for ConversationEventPublisher"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.publisher = ConversationEventPublisher()
    
    @pytest.mark.asyncio
    async def test_publish_conversation_response_basic(self):
        """Test basic conversation response publishing"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            result = await self.publisher.publish_conversation_response(
                chatbot_conversation_id="test-conversation-123",
                message="Hello, how can I help?",
                completed=False,
                charge=1
            )
            
            assert result is True
            mock_publish.assert_called_once()
            
            # Check the payload structure
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert payload["chatbotConversationId"] == "test-conversation-123"
            assert payload["message"] == "Hello, how can I help?"
            assert payload["completed"] is False
            assert payload["charge"] == 1
    
    @pytest.mark.asyncio
    async def test_publish_conversation_response_with_extra_data(self):
        """Test conversation response publishing with extra data"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            extra_data = {
                "messageConversationId": "msg-123",
                "customField": "customValue"
            }
            
            result = await self.publisher.publish_conversation_response(
                chatbot_conversation_id="test-conversation-123",
                message="Hello, how can I help?",
                completed=False,
                charge=1,
                message_conversation_id="msg-123",
                extra=extra_data
            )
            
            assert result is True
            
            # Check the payload includes extra data
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert payload["messageConversationId"] == "msg-123"
            assert payload["customField"] == "customValue"
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion_new_format(self):
        """Test conversation completion publishing with new payload format"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            entity_details = [
                {
                    "entityId": 213452,
                    "entityType": "LEAD",
                    "ownerId": 159
                },
                {
                    "entityId": 312662,
                    "entityType": "CONTACT",
                    "ownerId": 159
                }
            ]
            
            node_details = {
                "id": "n_pdf",
                "name": "send_pdf",
                "type": "sendMessage",
                "data": [
                    {
                        "type": "media",
                        "mediaFile": {
                            "fileId": 14,
                            "fileName": "Quotation_acne monty (1).pdf",
                            "fileSize": 51912,
                            "fileType": "document",
                            "fileCaption": "Sample Document"
                        }
                    }
                ]
            }
            
            result = await self.publisher.publish_conversation_completion(
                chatbot_conversation_id="f0b1474c-0f4a-4a62-8a75-d0f6161a72c0",
                completion_message="Conversation completed successfully",
                charge=0,
                chatbot_type="RULE",
                entity_details=entity_details,
                node_details=node_details
            )
            
            assert result is True
            
            # Check the payload structure matches the new format
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert payload["chatbotConversationId"] == "f0b1474c-0f4a-4a62-8a75-d0f6161a72c0"
            assert payload["message"] == "Conversation completed successfully"
            assert payload["completed"] is True
            assert payload["charge"] == 0
            assert payload["chatbotType"] == "RULE"
            assert payload["entityDetails"] == entity_details
            assert payload["nodeDetails"] == node_details
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion_minimal(self):
        """Test conversation completion publishing with minimal data"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            result = await self.publisher.publish_conversation_completion(
                chatbot_conversation_id="test-conversation-123",
                completion_message="Done",
                charge=2
            )
            
            assert result is True
            
            # Check the payload structure
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert payload["chatbotConversationId"] == "test-conversation-123"
            assert payload["message"] == "Done"
            assert payload["completed"] is True
            assert payload["charge"] == 2
            assert payload["chatbotType"] == "RULE"  # Default value
            assert "entityDetails" not in payload
            assert "nodeDetails" not in payload
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion_with_entity_details_only(self):
        """Test conversation completion publishing with entity details only"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            entity_details = [
                {
                    "entityId": 123,
                    "entityType": "LEAD",
                    "ownerId": 456
                }
            ]
            
            result = await self.publisher.publish_conversation_completion(
                chatbot_conversation_id="test-conversation-123",
                completion_message="Done",
                charge=1,
                entity_details=entity_details
            )
            
            assert result is True
            
            # Check the payload structure
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert payload["entityDetails"] == entity_details
            assert "nodeDetails" not in payload
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion_with_node_details_only(self):
        """Test conversation completion publishing with node details only"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            node_details = {
                "id": "node_123",
                "name": "test_node",
                "type": "sendMessage",
                "data": []
            }
            
            result = await self.publisher.publish_conversation_completion(
                chatbot_conversation_id="test-conversation-123",
                completion_message="Done",
                charge=1,
                node_details=node_details
            )
            
            assert result is True
            
            # Check the payload structure
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert payload["nodeDetails"] == node_details
            assert "entityDetails" not in payload
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion_publish_failure(self):
        """Test conversation completion publishing when _publish_event fails"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = False
            
            result = await self.publisher.publish_conversation_completion(
                chatbot_conversation_id="test-conversation-123",
                completion_message="Done",
                charge=1
            )
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion_exception(self):
        """Test conversation completion publishing when exception occurs"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.side_effect = Exception("Publish error")
            
            result = await self.publisher.publish_conversation_completion(
                chatbot_conversation_id="test-conversation-123",
                completion_message="Done",
                charge=1
            )
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_publish_conversation_response_exception(self):
        """Test conversation response publishing when exception occurs"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.side_effect = Exception("Publish error")
            
            result = await self.publisher.publish_conversation_response(
                chatbot_conversation_id="test-conversation-123",
                message="Hello",
                completed=False,
                charge=1
            )
            
            assert result is False

    @pytest.mark.asyncio
    async def test_publish_conversation_response_with_tenant_id(self):
        """Test conversation response publishing with tenantId"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            result = await self.publisher.publish_conversation_response(
                chatbot_conversation_id="test-conversation-123",
                message="Hello, how can I help?",
                completed=False,
                charge=1,
                tenant_id=2048
            )
            
            assert result is True
            
            # Check the payload includes tenantId
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert payload["tenantId"] == 2048
    
    @pytest.mark.asyncio
    async def test_publish_conversation_response_without_tenant_id(self):
        """Test conversation response publishing without tenantId"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            result = await self.publisher.publish_conversation_response(
                chatbot_conversation_id="test-conversation-123",
                message="Hello, how can I help?",
                completed=False,
                charge=1
            )
            
            assert result is True
            
            # Check the payload does not include tenantId
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert "tenantId" not in payload
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion_with_tenant_id(self):
        """Test conversation completion publishing with tenantId"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            entity_details = [
                {
                    "entityId": 123,
                    "entityType": "LEAD",
                    "ownerId": 456,
                    "entityName": "John Doe"
                }
            ]
            
            result = await self.publisher.publish_conversation_completion(
                chatbot_conversation_id="test-conversation-123",
                completion_message="Done",
                charge=1,
                tenant_id=2048,
                entity_details=entity_details
            )
            
            assert result is True
            
            # Check the payload includes tenantId
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert payload["tenantId"] == 2048
            assert payload["entityDetails"] == entity_details
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion_without_tenant_id(self):
        """Test conversation completion publishing without tenantId"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            result = await self.publisher.publish_conversation_completion(
                chatbot_conversation_id="test-conversation-123",
                completion_message="Done",
                charge=1
            )
            
            assert result is True
            
            # Check the payload does not include tenantId
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert "tenantId" not in payload
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion_with_entity_name(self):
        """Test conversation completion publishing with entityName in entityDetails"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            entity_details = [
                {
                    "entityId": 123,
                    "entityType": "LEAD",
                    "ownerId": 456,
                    "entityName": "John Doe"
                },
                {
                    "entityId": 789,
                    "entityType": "CONTACT",
                    "ownerId": 456,
                    "entityName": "Jane Smith"
                }
            ]
            
            result = await self.publisher.publish_conversation_completion(
                chatbot_conversation_id="test-conversation-123",
                completion_message="Done",
                charge=1,
                tenant_id=2048,
                entity_details=entity_details
            )
            
            assert result is True
            
            # Check the payload includes entityName
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert payload["entityDetails"][0]["entityName"] == "John Doe"
            assert payload["entityDetails"][1]["entityName"] == "Jane Smith"
            assert payload["tenantId"] == 2048
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion_with_null_entity_name(self):
        """Test conversation completion publishing with null entityName"""
        
        with patch.object(self.publisher, '_publish_event', new_callable=AsyncMock) as mock_publish:
            mock_publish.return_value = True
            
            entity_details = [
                {
                    "entityId": 123,
                    "entityType": "LEAD",
                    "ownerId": 456,
                    "entityName": None
                }
            ]
            
            result = await self.publisher.publish_conversation_completion(
                chatbot_conversation_id="test-conversation-123",
                completion_message="Done",
                charge=1,
                tenant_id=2048,
                entity_details=entity_details
            )
            
            assert result is True
            
            # Check the payload includes null entityName
            call_args = mock_publish.call_args
            payload = call_args[1]["payload"]
            
            assert payload["entityDetails"][0]["entityName"] is None
            assert payload["entityDetails"][0]["ownerId"] == 456


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
