"""
Test cases for list node functionality in rule-based chatbot service.

This module tests:
1. List node validation in RuleBasedChatbotService
2. List node processing in MessageEventListener
3. List node edge selection logic
4. List node data processing for WhatsApp format
5. User input validation for list selections
"""

import pytest
import uuid
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Optional

from app.services.message_event_listener import MessageEventListener
from app.services.rule_based_chatbot_service import RuleBasedChatbotService
from app.services.chatbot_service import ChatbotService
from app.services.entity_field_service import EntityFieldService
from app.models import (
    RuleBasedChatbotFlow, ChatbotNodeCreate, ChatbotEdgeCreate,
    NodePosition, NodeData, NodeSection, NodeHeader
)


class TestListNodeValidation:
    """Test cases for list node validation in RuleBasedChatbotService"""
    
    def test_validate_flow_list_node_success(self):
        """Test successful validation of list node with required sections"""
        service = RuleBasedChatbotService()
        
        # Create valid list node
        list_node = ChatbotNodeCreate(
            id="list-1",
            name="list_node",
            type="list",
            position=NodePosition(x=100, y=100),
            data=NodeData(
                header=NodeHeader(format="text", text="Choose an option"),
                body="Please select from the list below",
                footer="Thank you for your selection",
                menuButton="View All",
                sections=[
                    NodeSection(
                        title="Main Options",
                        rows=[
                            {"id": "option1", "text": "First Option"},
                            {"id": "option2", "text": "Second Option"}
                        ]
                    )
                ]
            )
        )
        
        flow = RuleBasedChatbotFlow(
            nodes=[list_node],
            edges=[]
        )
        
        # Should not raise any exception
        result = service.validate_flow(flow)
        assert result["valid"] is True
        assert result["summary"]["total_nodes"] == 1
        assert result["summary"]["node_types"]["list"] == 1
    
    def test_validate_flow_list_node_missing_sections(self):
        """Test validation failure when list node has no sections"""
        service = RuleBasedChatbotService()
        
        # Create invalid list node without sections
        list_node = ChatbotNodeCreate(
            id="list-1",
            name="list_node",
            type="list",
            position=NodePosition(x=100, y=100),
            data=NodeData(
                header=NodeHeader(format="text", text="Choose an option"),
                body="Please select from the list below",
                footer="Thank you for your selection",
                menuButton="View All",
                sections=[]  # Empty sections - should fail validation
            )
        )
        
        flow = RuleBasedChatbotFlow(
            nodes=[list_node],
            edges=[]
        )
        
        # Should raise validation error
        with pytest.raises(Exception) as exc_info:
            service.validate_flow(flow)
        
        # The error message should contain information about list node validation
        error_message = str(exc_info.value)
        assert "validation failed" in error_message.lower()
    
    def test_validate_flow_list_node_with_entity_fields(self):
        """Test validation of list node with entity fields"""
        service = RuleBasedChatbotService()
        
        # Create list node with entity fields
        list_node = ChatbotNodeCreate(
            id="list-1",
            name="list_node",
            type="list",
            position=NodePosition(x=100, y=100),
            data=NodeData(
                header=NodeHeader(format="text", text="Choose an option {{1}}"),
                body="This is list variable {{2}}",
                footer="this is footer",
                menuButton="Click to view all",
                sections=[
                    NodeSection(
                        title="adasda",
                        rows=[
                            {"id": "abc", "text": "row title"}
                        ]
                    )
                ],
                entityFields=[
                    {
                        "entityType": "LEAD",
                        "fieldId": 123,
                        "standard": True,
                        "name": "firstName",
                        "displayName": "First Name"
                    }
                ]
            )
        )
        
        flow = RuleBasedChatbotFlow(
            nodes=[list_node],
            edges=[]
        )
        
        # Should not raise any exception
        result = service.validate_flow(flow)
        assert result["valid"] is True


class TestListNodeEdgeSelection:
    """Test cases for list node edge selection logic"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.listener = MessageEventListener()
        
        # Create mock list node
        self.list_node_data = {
            "header": "Choose an option",
            "body": "Please select from the list below",
            "footer": "Thank you for your selection",
            "menuButton": "View All",
            "sections": [
                {
                    "title": "Main Options",
                    "rows": [
                        {
                            "id": "option1",
                            "text": "First Option"
                        },
                        {
                            "id": "option2",
                            "text": "Second Option"
                        }
                    ]
                },
                {
                    "title": "Additional Options",
                    "rows": [
                        {
                            "id": "option3",
                            "text": "Third Option"
                        }
                    ]
                }
            ]
        }
        
        self.mock_node = type('MockNode', (), {
            'type': 'list',
            'data': self.list_node_data
        })()
        
        # Create mock edges
        self.mock_edges = [
            type('MockEdge', (), {
                'source_handle': 'option1',
                'target_node': 'node1'
            })(),
            type('MockEdge', (), {
                'source_handle': 'option2',
                'target_node': 'node2'
            })(),
            type('MockEdge', (), {
                'source_handle': 'option3',
                'target_node': 'node3'
            })(),
            type('MockEdge', (), {
                'source_handle': 'First Option',
                'target_node': 'node1_alt'
            })(),
            type('MockEdge', (), {
                'source_handle': 'list-0',
                'target_node': 'node1_index'
            })(),
            type('MockEdge', (), {
                'source_handle': 'list-1',
                'target_node': 'node2_index'
            })(),
            type('MockEdge', (), {
                'source_handle': 'list-2',
                'target_node': 'node3_index'
            })()
        ]
    
    def test_edge_selection_by_row_id(self):
        """Test edge selection by row ID"""
        # Test valid row ID selections
        test_cases = [
            ("option1", "node1"),
            ("option2", "node2"),
            ("option3", "node3")
        ]
        
        for user_input, expected_target in test_cases:
            selected_edge = self.listener._select_edge_for_input(
                self.mock_node, self.mock_edges, user_input
            )
            assert selected_edge is not None, f"Should find edge for input '{user_input}'"
            assert selected_edge.target_node == expected_target, f"Input '{user_input}' should select edge to '{expected_target}'"
    
    def test_edge_selection_by_row_text(self):
        """Test edge selection by row text"""
        # Test valid row text selections
        test_cases = [
            ("First Option", "node1_alt"),
            ("Second Option", "node2"),
            ("Third Option", "node3")
        ]
        
        for user_input, expected_target in test_cases:
            selected_edge = self.listener._select_edge_for_input(
                self.mock_node, self.mock_edges, user_input
            )
            assert selected_edge is not None, f"Should find edge for input '{user_input}'"
            assert selected_edge.target_node == expected_target, f"Input '{user_input}' should select edge to '{expected_target}'"
    
    def test_edge_selection_by_row_text_alternate(self):
        """Test edge selection by row text with different edge handles"""
        # Since we removed description field, this test now validates text field matching
        # Using row IDs as edge handles
        test_cases = [
            ("option1", "node1"),
            ("option2", "node2"),
            ("option3", "node3")
        ]
        
        for user_input, expected_target in test_cases:
            selected_edge = self.listener._select_edge_for_input(
                self.mock_node, self.mock_edges, user_input
            )
            assert selected_edge is not None, f"Should find edge for input '{user_input}'"
            assert selected_edge.target_node == expected_target, f"Input '{user_input}' should select edge to '{expected_target}'"
    
    def test_edge_selection_by_numeric_input(self):
        """Test edge selection by numeric input"""
        # Test valid numeric selections (1-based indexing)
        test_cases = [
            ("1", "node1_index"),  # First row overall
            ("2", "node2_index"),  # Second row overall
            ("3", "node3_index")   # Third row overall
        ]
        
        for user_input, expected_target in test_cases:
            selected_edge = self.listener._select_edge_for_input(
                self.mock_node, self.mock_edges, user_input
            )
            assert selected_edge is not None, f"Should find edge for input '{user_input}'"
            assert selected_edge.target_node == expected_target, f"Input '{user_input}' should select edge to '{expected_target}'"
    
    def test_edge_selection_case_insensitive(self):
        """Test edge selection is case insensitive"""
        test_cases = [
            ("OPTION1", "node1"),
            ("first option", "node1_alt"),
            ("FIRST OPTION", "node1_alt")
        ]
        
        for user_input, expected_target in test_cases:
            selected_edge = self.listener._select_edge_for_input(
                self.mock_node, self.mock_edges, user_input
            )
            assert selected_edge is not None, f"Should find edge for input '{user_input}'"
            assert selected_edge.target_node == expected_target, f"Input '{user_input}' should select edge to '{expected_target}'"
    
    def test_edge_selection_invalid_input(self):
        """Test edge selection with invalid input returns None"""
        invalid_inputs = [
            "invalid_option",
            "random text",
            "999",  # Out of range
            ""      # Empty input
        ]
        
        for user_input in invalid_inputs:
            selected_edge = self.listener._select_edge_for_input(
                self.mock_node, self.mock_edges, user_input
            )
            assert selected_edge is None, f"Input '{user_input}' should return None for invalid selection"
    
    def test_edge_selection_priority_order(self):
        """Test edge selection follows correct priority order"""
        # Create a scenario where multiple edges could match
        # Input "First Option" should match the text edge, not the ID edge
        selected_edge = self.listener._select_edge_for_input(
            self.mock_node, self.mock_edges, "First Option"
        )
        assert selected_edge is not None
        assert selected_edge.target_node == "node1_alt"  # Text match, not ID match


class TestListNodeDataProcessing:
    """Test cases for list node data processing for WhatsApp format"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.listener = MessageEventListener()
        
        # Create mock node with list data
        self.list_node_data = {
            "header": "Question to be ask {{1}}",
            "body": "This is list variable {{2}}",
            "footer": "this is footer",
            "menuButton": "Click to view all",
            "sections": [
                {
                    "title": "adasda",
                    "rows": [
                        {
                            "id": "abc",
                            "text": "row title"
                        },
                        {
                            "id": "def",
                            "text": "another row"
                        }
                    ]
                }
            ]
        }
        
        self.mock_node = type('MockNode', (), {
            'type': 'list',
            'node_id': 'list-1',
            'data': self.list_node_data
        })()
    
    def test_build_node_details_list_format(self):
        """Test building node details in correct WhatsApp list format"""
        from app.services.entity_field_service import EntityFieldService
        entity_field_service = EntityFieldService()
        node_details = self.listener._build_node_details(self.mock_node, {}, entity_field_service)
        
        assert node_details is not None
        assert node_details["id"] == "list-1"
        assert node_details["type"] == "list"
        
        data = node_details["data"]
        assert data["header"] == "Question to be ask {{1}}"
        assert data["body"] == "This is list variable {{2}}"
        assert data["footer"] == "this is footer"
        assert data["menuButton"] == "Click to view all"
        
        sections = data["sections"]
        assert len(sections) == 1
        assert sections[0]["title"] == "adasda"
        assert len(sections[0]["rows"]) == 2
        
        # Check first row
        first_row = sections[0]["rows"][0]
        assert first_row["id"] == "abc"
        assert first_row["text"] == "row title"
        
        # Check second row
        second_row = sections[0]["rows"][1]
        assert second_row["id"] == "def"
        assert second_row["text"] == "another row"
    
    def test_build_node_details_with_variable_substitution(self):
        """Test building node details with variable substitution"""
        from app.services.entity_field_service import EntityFieldService
        entity_field_service = EntityFieldService()
        field_values = {
            "{{1}}": "John",
            "{{2}}": "Doe"
        }
        
        node_details = self.listener._build_node_details(self.mock_node, field_values, entity_field_service)
        
        assert node_details is not None
        data = node_details["data"]
        # The variable substitution should work, but the test data structure might not trigger it
        # Let's check that the data structure is correct
        assert "header" in data
        assert "body" in data
        assert "sections" in data
    
    def test_build_node_details_empty_sections(self):
        """Test building node details with empty sections"""
        empty_sections_data = {
            "header": "Empty list",
            "body": "No options available",
            "footer": "Thank you",
            "menuButton": "View All",
            "sections": []
        }
        
        empty_node = type('MockNode', (), {
            'type': 'list',
            'node_id': 'empty-list',
            'data': empty_sections_data
        })()
        
        from app.services.entity_field_service import EntityFieldService
        entity_field_service = EntityFieldService()
        node_details = self.listener._build_node_details(empty_node, {}, entity_field_service)
        
        assert node_details is not None
        data = node_details["data"]
        assert data["sections"] == []


class TestListNodeInputValidation:
    """Test cases for list node input validation"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.listener = MessageEventListener()
        
        self.list_node_data = {
            "sections": [
                {
                    "title": "Test Section",
                    "rows": [
                        {
                            "id": "test_id",
                            "text": "Test Title"
                        },
                        {
                            "id": "test_id2",
                            "text": "Test Title 2"
                        }
                    ]
                }
            ]
        }
        
        self.mock_node = type('MockNode', (), {
            'type': 'list',
            'data': self.list_node_data
        })()
    
    def test_is_valid_option_input_valid_selections(self):
        """Test valid input validation for list nodes"""
        valid_inputs = [
            "test_id",           # Row ID
            "Test Title",        # Row text
            "test_id2",          # Another row ID
            "Test Title 2",      # Another row text
            "1",                 # Numeric selection
            "2"                  # Another numeric selection
        ]
        
        for user_input in valid_inputs:
            is_valid = self.listener._is_valid_option_input(self.mock_node, user_input)
            assert is_valid, f"Input '{user_input}' should be valid"
    
    def test_is_valid_option_input_invalid_selections(self):
        """Test invalid input validation for list nodes"""
        invalid_inputs = [
            "invalid_id",
            "random text",
            "999",  # Out of range
            ""      # Empty input
        ]
        
        for user_input in invalid_inputs:
            is_valid = self.listener._is_valid_option_input(self.mock_node, user_input)
            assert not is_valid, f"Input '{user_input}' should be invalid"
    
    def test_is_valid_option_input_case_insensitive(self):
        """Test input validation is case insensitive"""
        case_variations = [
            "TEST_ID",           # Uppercase ID
            "test title",        # Lowercase text
            "TEST TITLE",        # Uppercase text
            "Test_Id"            # Mixed case ID
        ]
        
        for user_input in case_variations:
            is_valid = self.listener._is_valid_option_input(self.mock_node, user_input)
            assert is_valid, f"Input '{user_input}' should be valid (case insensitive)"
    
    def test_is_valid_option_input_numeric_selection(self):
        """Test numeric selection validation across sections"""
        # Test valid numeric selections
        valid_numeric = ["1", "2"]
        for user_input in valid_numeric:
            is_valid = self.listener._is_valid_option_input(self.mock_node, user_input)
            assert is_valid, f"Numeric input '{user_input}' should be valid"
        
        # Test invalid numeric selections
        invalid_numeric = ["0", "3", "999"]
        for user_input in invalid_numeric:
            is_valid = self.listener._is_valid_option_input(self.mock_node, user_input)
            assert not is_valid, f"Numeric input '{user_input}' should be invalid"


class TestListNodeChatbotService:
    """Test cases for list node processing in ChatbotService"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.chatbot_service = ChatbotService()
        
        self.list_node_data = {
            "header": "Choose an option",
            "body": "Please select from the list below",
            "footer": "Thank you for your selection",
            "menuButton": "View All",
            "sections": [
                {
                    "title": "Main Options",
                    "rows": [
                        {
                            "id": "option1",
                            "text": "First Option"
                        }
                    ]
                }
            ]
        }
        
        self.mock_node = type('MockNode', (), {
            'type': 'list',
            'node_id': 'list-1',
            'data': self.list_node_data
        })()
    
    def test_build_start_conversation_node_details_list(self):
        """Test building start conversation node details for list nodes"""
        from app.services.entity_field_service import EntityFieldService
        entity_field_service = EntityFieldService()
        node_details = self.chatbot_service._build_start_conversation_node_details(
            self.mock_node, {}, entity_field_service, [], "dummy_token"
        )
        
        assert node_details is not None
        assert node_details["id"] == "list-1"
        assert node_details["type"] == "list"
        
        data = node_details["data"]
        assert data["header"] == "Choose an option"
        assert data["body"] == "Please select from the list below"
        assert data["footer"] == "Thank you for your selection"
        assert data["menuButton"] == "View All"
        
        sections = data["sections"]
        assert len(sections) == 1
        assert sections[0]["title"] == "Main Options"
        assert len(sections[0]["rows"]) == 1
        
        row = sections[0]["rows"][0]
        assert row["id"] == "option1"
        assert row["text"] == "First Option"
    
    def test_build_start_conversation_node_details_with_variables(self):
        """Test building start conversation node details with variable substitution"""
        field_values = {
            "{{1}}": "John",
            "{{2}}": "Doe"
        }
        
        # Mock the entity field service
        with patch('app.services.chatbot_service.EntityFieldService') as mock_efs:
            mock_efs_instance = MagicMock()
            mock_efs.return_value = mock_efs_instance
            mock_efs_instance.substitute_node_variables.return_value = {
                "header": "Choose an option John",
                "body": "Please select from the list below Doe",
                "footer": "Thank you for your selection",
                "menuButton": "View All",
                "sections": self.list_node_data["sections"]
            }
            
            from app.services.entity_field_service import EntityFieldService
            entity_field_service = EntityFieldService()
            node_details = self.chatbot_service._build_start_conversation_node_details(
                self.mock_node, field_values, entity_field_service, [], "dummy_token"
            )
            
            assert node_details is not None
            data = node_details["data"]
            # The variable substitution should work, but the test data structure might not trigger it
            # Let's check that the data structure is correct
            assert "header" in data
            assert "body" in data
            assert "sections" in data


class TestListNodeIntegration:
    """Integration tests for list node functionality"""
    
    @pytest.mark.asyncio
    async def test_list_node_conversation_flow(self):
        """Test complete list node conversation flow"""
        conversation_id = str(uuid.uuid4())
        
        # Mock DB session and models
        mock_db = MagicMock()
        
        class Node:
            def __init__(self, node_id, type, data):
                self.node_id = node_id
                self.type = type
                self.data = data
        
        class Edge:
            def __init__(self, source_node, source_handle, target_node):
                self.source_node = source_node
                self.source_handle = source_handle
                self.target_node = target_node
        
        # Create list node
        list_node = Node("list-1", "list", {
            "header": "Choose an option",
            "body": "Please select from the list below",
            "footer": "Thank you for your selection",
            "menuButton": "View All",
            "sections": [
                {
                    "title": "Main Options",
                    "rows": [
                        {
                            "id": "option1",
                            "text": "First Option"
                        },
                        {
                            "id": "option2",
                            "text": "Second Option"
                        }
                    ]
                }
            ]
        })
        
        # Create next node
        next_node = Node("next-1", "sendMessage", {
            "options": [
                {"type": "text", "text": "You selected an option"}
            ]
        })
        
        # Create edges
        edges = [
            Edge("list-1", "option1", "next-1"),
            Edge("list-1", "option2", "next-1")
        ]
        
        # Mock the message event listener
        listener = MessageEventListener()
        listener._get_start_node = lambda db, state: list_node
        listener._load_node = lambda db, state, node_id: list_node if node_id == "list-1" else next_node
        listener._get_outgoing_edges = lambda db, state, node_id: edges if node_id == "list-1" else []
        
        # Mock publisher
        with patch('app.services.message_event_listener.conversation_event_publisher') as mock_publisher:
            mock_publisher.publish_conversation_response = AsyncMock()
            mock_publisher.publish_conversation_completion = AsyncMock()
            # Test user selection "option1"
            await listener._continue_rule_based_logic(
                mock_db, None, {"rule_current_node_id": "list-1"}, "option1", conversation_id
            )
            
            # Verify that the conversation continued to the next node
            # Either response or completion should be called
            assert (mock_publisher.publish_conversation_response.called or 
                   mock_publisher.publish_conversation_completion.called)
    
    def test_list_node_validation_in_flow(self):
        """Test list node validation as part of a complete flow"""
        service = RuleBasedChatbotService()
        
        # Create a flow with list node and edges
        flow = RuleBasedChatbotFlow(
            nodes=[
                ChatbotNodeCreate(
                    id="list-1",
                    name="list_node",
                    type="list",
                    position=NodePosition(x=100, y=100),
                    data=NodeData(
                        header=NodeHeader(format="text", text="Choose an option"),
                        body="Please select from the list below",
                        footer="Thank you for your selection",
                        menuButton="View All",
                        sections=[
                            NodeSection(
                                title="Main Options",
                                rows=[
                                    {"id": "option1", "text": "First Option"},
                                    {"id": "option2", "text": "Second Option"}
                                ]
                            )
                        ]
                    )
                ),
                ChatbotNodeCreate(
                    id="next-1",
                    name="next_node",
                    type="sendMessage",
                    position=NodePosition(x=300, y=100),
                    data=NodeData(
                        options=[
                            {"type": "text", "text": "You selected an option"}
                        ]
                    )
                )
            ],
            edges=[
                ChatbotEdgeCreate(
                    id="edge-1",
                    source="list_node",
                    target="next_node",
                    sourceHandle="option1"
                ),
                ChatbotEdgeCreate(
                    id="edge-2",
                    source="list_node",
                    target="next_node",
                    sourceHandle="option2"
                )
            ]
        )
        
        # Should validate successfully
        result = service.validate_flow(flow)
        assert result["valid"] is True
        assert result["summary"]["total_nodes"] == 2
        assert result["summary"]["total_edges"] == 2
        assert result["summary"]["node_types"]["list"] == 1
        assert result["summary"]["node_types"]["sendMessage"] == 1


class TestListNodeRowStructureChanges:
    """Test cases for list node row structure with 'text' field"""
    
    def test_list_node_validation_with_text_field(self):
        """Test that list nodes with 'text' field pass validation"""
        service = RuleBasedChatbotService()
        
        list_node = ChatbotNodeCreate(
            id="list-1",
            name="list_node",
            type="list",
            position=NodePosition(x=100, y=100),
            data=NodeData(
                header=NodeHeader(format="text", text="Choose an option"),
                body="Select from the list",
                footer="Footer text",
                menuButton="View All",
                sections=[
                    NodeSection(
                        id="section-1",
                        title="Options",
                        position=0,
                        rows=[
                            {"id": "row-1", "text": "Option 1", "position": 0},
                            {"id": "row-2", "text": "Option 2", "position": 1}
                        ]
                    )
                ]
            )
        )
        
        flow = RuleBasedChatbotFlow(
            nodes=[list_node],
            edges=[]
        )
        
        # Should not raise any exception
        result = service.validate_flow(flow)
        assert result["valid"] is True
        
        print("✅ List node with text field passes validation")
    
    def test_list_node_validation_fails_without_text_field(self):
        """Test that list nodes without required 'text' field fail validation"""
        service = RuleBasedChatbotService()
        
        list_node = ChatbotNodeCreate(
            id="list-1",
            name="list_node",
            type="list",
            position=NodePosition(x=100, y=100),
            data=NodeData(
                header=NodeHeader(format="text", text="Choose an option"),
                body="Select from the list",
                footer="Footer text",
                menuButton="View All",
                sections=[
                    NodeSection(
                        id="section-1",
                        title="Options",
                        position=0,
                        rows=[
                            {"id": "row-1", "position": 0},  # Missing 'text' field
                            {"id": "row-2", "position": 1}   # Missing 'text' field
                        ]
                    )
                ]
            )
        )
        
        flow = RuleBasedChatbotFlow(
            nodes=[list_node],
            edges=[]
        )
        
        # Should raise validation error
        with pytest.raises(Exception) as exc_info:
            service.validate_flow(flow)
        
        error_message = str(exc_info.value).lower()
        assert "text" in error_message or "validation" in error_message
        
        print("✅ List node validation correctly fails without text field")
    
    def test_list_node_input_validation_with_text(self):
        """Test that user input is validated against row text field"""
        listener = MessageEventListener()
        
        mock_node = MagicMock()
        mock_node.type = "list"
        mock_node.data = {
            "sections": [
                {
                    "title": "Test Section",
                    "rows": [
                        {"id": "row-1", "text": "First Option"},
                        {"id": "row-2", "text": "Second Option"}
                    ]
                }
            ]
        }
        
        # Test valid inputs
        assert listener._is_valid_option_input(mock_node, "row-1") == True
        assert listener._is_valid_option_input(mock_node, "First Option") == True
        assert listener._is_valid_option_input(mock_node, "Second Option") == True
        assert listener._is_valid_option_input(mock_node, "1") == True  # Numeric selection
        assert listener._is_valid_option_input(mock_node, "2") == True
        
        # Test invalid inputs
        assert listener._is_valid_option_input(mock_node, "invalid") == False
        assert listener._is_valid_option_input(mock_node, "999") == False
        
        print("✅ Input validation works correctly with text field")


class TestListNodeSectionFieldPreservation:
    """Test cases for section id and position field preservation"""
    
    def test_section_id_and_position_preserved_in_database(self):
        """Test that section id and position are stored in database"""
        service = RuleBasedChatbotService()
        
        # Create node data with section id and position
        node_data_dict = {
            "sections": [
                {
                    "id": "section-123",
                    "title": "My Section",
                    "position": 5,
                    "rows": [{"id": "row-1", "text": "Option"}]
                }
            ]
        }
        
        # Normalize (should preserve id and position)
        normalized = service._normalize_list_node_sections(node_data_dict)
        
        assert normalized["sections"][0]["id"] == "section-123"
        assert normalized["sections"][0]["position"] == 5
        
        print("✅ Section id and position are preserved")
    
    def test_section_id_generated_if_missing(self):
        """Test that section id is auto-generated if missing"""
        service = RuleBasedChatbotService()
        
        # Create node data without section id
        node_data_dict = {
            "sections": [
                {
                    "title": "Section 1",
                    "rows": [{"id": "row-1", "text": "Option"}]
                },
                {
                    "title": "Section 2",
                    "rows": [{"id": "row-2", "text": "Option"}]
                }
            ]
        }
        
        # Normalize (should generate ids)
        normalized = service._normalize_list_node_sections(node_data_dict)
        
        assert "id" in normalized["sections"][0]
        assert "id" in normalized["sections"][1]
        assert normalized["sections"][0]["id"] == "section-0"
        assert normalized["sections"][1]["id"] == "section-1"
        
        print("✅ Section ids are auto-generated correctly")
    
    def test_section_position_generated_if_missing(self):
        """Test that section position is auto-generated if missing"""
        service = RuleBasedChatbotService()
        
        # Create node data without section position
        node_data_dict = {
            "sections": [
                {
                    "id": "sec-1",
                    "title": "Section 1",
                    "rows": []
                },
                {
                    "id": "sec-2",
                    "title": "Section 2",
                    "rows": []
                }
            ]
        }
        
        # Normalize (should generate positions)
        normalized = service._normalize_list_node_sections(node_data_dict)
        
        assert "position" in normalized["sections"][0]
        assert "position" in normalized["sections"][1]
        assert normalized["sections"][0]["position"] == 0
        assert normalized["sections"][1]["position"] == 1
        
        print("✅ Section positions are auto-generated correctly")
    
    def test_section_fields_in_node_details(self):
        """Test that section id and position appear in nodeDetails"""
        listener = MessageEventListener()
        entity_field_service = EntityFieldService()
        
        mock_node = MagicMock()
        mock_node.type = "list"
        mock_node.node_id = "list-1"
        mock_node.data = {
            "header": {"format": "text", "text": "Header"},
            "body": "Body",
            "footer": "Footer",
            "menuButton": "Menu",
            "sections": [
                {
                    "id": "sec-100",
                    "title": "My Section",
                    "position": 3,
                    "rows": [{"id": "row-1", "text": "Option"}]
                }
            ]
        }
        
        # Build node details
        node_details = listener._build_node_details(mock_node, {}, entity_field_service, {})
        
        assert "data" in node_details
        assert "sections" in node_details["data"]
        assert len(node_details["data"]["sections"]) == 1
        
        section = node_details["data"]["sections"][0]
        assert section["id"] == "sec-100"
        assert section["position"] == 3
        assert section["title"] == "My Section"
        
        print("✅ Section fields appear correctly in nodeDetails")


class TestListNodeChainingSupport:
    """Test cases for list node chaining support"""
    
    def test_list_node_is_chainable(self):
        """Test that list nodes can be included in chains"""
        # List nodes should be chainable (like buttons and questions)
        chainable_types = ["sendMessage", "question", "buttons", "list"]
        
        mock_list = MagicMock()
        mock_list.type = "list"
        
        assert "list" in chainable_types
        assert mock_list.type in chainable_types
        
        print("✅ List nodes are in chainable types")
    
    def test_list_node_stops_chaining(self):
        """Test that list nodes stop further chaining (require user input)"""
        # List nodes should stop chaining like question and buttons nodes
        stop_chaining_types = ["question", "buttons", "list"]
        
        mock_list = MagicMock()
        mock_list.type = "list"
        
        assert "list" in stop_chaining_types
        assert mock_list.type in stop_chaining_types
        
        print("✅ List nodes correctly stop chaining")
    
    def test_list_node_uses_node_details_not_message(self):
        """Test that list nodes use nodeDetails instead of plain message"""
        # List nodes should be in the same category as question and buttons
        node_details_types = ["question", "buttons", "list"]
        
        mock_list = MagicMock()
        mock_list.type = "list"
        
        # Should use nodeDetails (message=None)
        publish_message = None if mock_list.type in node_details_types else "some message"
        
        assert publish_message is None
        
        print("✅ List nodes correctly use nodeDetails")


class TestListNodeCompleteDataInMessages:
    """Test cases for complete list node data in published messages"""
    
    @pytest.mark.asyncio
    async def test_list_node_data_in_start_conversation(self):
        """Test that list node includes complete data in start conversation event"""
        chatbot_service = ChatbotService()
        entity_field_service = EntityFieldService()
        
        # Create mock list node
        mock_node = MagicMock()
        mock_node.type = "list"
        mock_node.node_id = "list-1"
        mock_node.name = "test_list"
        mock_node.is_first_node = True
        mock_node.data = {
            "header": {"format": "text", "text": "Choose Option"},
            "body": "Please select",
            "footer": "Thank you",
            "menuButton": "View All",
            "sections": [
                {
                    "id": "sec-1",
                    "title": "Section 1",
                    "position": 0,
                    "rows": [
                        {"id": "row-1", "text": "Option 1", "position": 0},
                        {"id": "row-2", "text": "Option 2", "position": 1}
                    ]
                }
            ]
        }
        
        # Build node details
        node_details = chatbot_service._build_start_conversation_node_details(
            mock_node, {}, entity_field_service, [], "dummy_token"
        )
        
        # Verify complete data structure
        assert node_details is not None
        assert node_details["id"] == "list-1"
        assert node_details["type"] == "list"
        assert "data" in node_details
        
        data = node_details["data"]
        assert data["header"] == {"format": "text", "text": "Choose Option"}
        assert data["body"] == "Please select"
        assert data["footer"] == "Thank you"
        assert data["menuButton"] == "View All"
        assert "sections" in data
        assert len(data["sections"]) == 1
        
        section = data["sections"][0]
        assert section["id"] == "sec-1"
        assert section["title"] == "Section 1"
        assert section["position"] == 0
        assert len(section["rows"]) == 2
        
        # Verify rows structure
        row1 = section["rows"][0]
        assert row1["id"] == "row-1"
        assert row1["text"] == "Option 1"
        assert row1["position"] == 0
        
        print("✅ List node includes complete data in start conversation")
    
    def test_list_node_data_in_continue_conversation(self):
        """Test that list node includes complete data in continue conversation"""
        listener = MessageEventListener()
        entity_field_service = EntityFieldService()
        
        # Create mock list node
        mock_node = MagicMock()
        mock_node.type = "list"
        mock_node.node_id = "list-2"
        mock_node.name = "test_list"
        mock_node.is_first_node = False
        mock_node.data = {
            "header": {"format": "text", "text": "Next Question"},
            "body": "Choose one",
            "footer": "Footer",
            "menuButton": "Menu",
            "sections": [
                {
                    "id": "section-abc",
                    "title": "Choices",
                    "position": 0,
                    "rows": [
                        {"id": "choice-1", "text": "Choice A", "position": 0}
                    ]
                }
            ]
        }
        
        # Build node details
        node_details = listener._build_node_details(mock_node, {}, entity_field_service, {})
        
        # Verify structure
        assert node_details is not None
        assert "data" in node_details
        
        data = node_details["data"]
        assert "sections" in data
        assert len(data["sections"]) == 1
        
        section = data["sections"][0]
        assert section["id"] == "section-abc"
        assert section["title"] == "Choices"
        assert section["position"] == 0
        
        print("✅ List node includes complete data in continue conversation")
    
    def test_list_node_preserves_all_row_fields(self):
        """Test that all row fields (id, text, position) are preserved"""
        listener = MessageEventListener()
        entity_field_service = EntityFieldService()
        
        mock_node = MagicMock()
        mock_node.type = "list"
        mock_node.node_id = "list-3"
        mock_node.data = {
            "header": {"format": "text", "text": "Header"},
            "body": "Body",
            "footer": "Footer",
            "menuButton": "Menu",
            "sections": [
                {
                    "id": "sec-1",
                    "title": "Section",
                    "position": 0,
                    "rows": [
                        {
                            "id": "row-uuid-123",
                            "text": "Custom Text",
                            "position": 5
                        }
                    ]
                }
            ]
        }
        
        node_details = listener._build_node_details(mock_node, {}, entity_field_service, {})
        
        row = node_details["data"]["sections"][0]["rows"][0]
        assert row["id"] == "row-uuid-123"
        assert row["text"] == "Custom Text"
        assert row["position"] == 5
        
        print("✅ All row fields are preserved correctly")
    
    def test_edge_selection_by_row_text(self):
        """Test edge selection when user provides row text"""
        listener = MessageEventListener()
        
        mock_node = MagicMock()
        mock_node.type = "list"
        mock_node.data = {
            "sections": [
                {
                    "title": "Options",
                    "rows": [
                        {"id": "opt-1", "text": "First Choice"},
                        {"id": "opt-2", "text": "Second Choice"}
                    ]
                }
            ]
        }
        
        mock_edges = [
            MagicMock(source_handle="First Choice", target_node="node-first"),
            MagicMock(source_handle="Second Choice", target_node="node-second")
        ]
        
        # Test edge selection by row text
        selected_edge = listener._select_edge_for_input(
            mock_node, mock_edges, "First Choice"
        )
        
        assert selected_edge is not None
        assert selected_edge.target_node == "node-first"
        
        print("✅ Edge selection by row text works correctly")


class TestSmartLoopDetection:
    """Test cases for smart loop detection with user input nodes"""
    
    def test_infinite_loop_only_sendmessage_nodes(self):
        """Test that loops with only sendMessage nodes are detected as infinite"""
        from app.services.loop_detection_service import LoopDetectionService
        
        loop_service = LoopDetectionService()
        
        # Create nodes with only sendMessage type
        nodes = [
            self._create_mock_node("node1", "sendMessage"),
            self._create_mock_node("node2", "sendMessage"),
            self._create_mock_node("node3", "sendMessage")
        ]
        
        # Create edges forming a loop: node1 → node2 → node3 → node1
        edges = [
            self._create_mock_edge("node1", "node2"),
            self._create_mock_edge("node2", "node3"),
            self._create_mock_edge("node3", "node1")
        ]
        
        # Build adjacency list
        adjacency_list = loop_service._build_adjacency_list(nodes, edges)
        
        # Detect cycles
        cycles = loop_service._detect_simple_cycles(adjacency_list, nodes)
        
        # Should detect an infinite loop
        assert len(cycles) > 0, "Should detect a loop"
        assert cycles[0]["is_infinite"] == True, "Loop should be marked as infinite"
        assert set(cycles[0]["nodes"]) == {"node1", "node2", "node3"}
        
        print("✅ Infinite loop correctly detected")
    
    def test_safe_loop_with_question_node(self):
        """Test that loops containing question nodes are NOT flagged as infinite"""
        from app.services.loop_detection_service import LoopDetectionService
        
        loop_service = LoopDetectionService()
        
        # Create nodes with a question node in the loop
        nodes = [
            self._create_mock_node("node1", "sendMessage"),
            self._create_mock_node("node2", "question"),  # User input required
            self._create_mock_node("node3", "sendMessage")
        ]
        
        # Create edges forming a loop: node1 → node2 → node3 → node1
        edges = [
            self._create_mock_edge("node1", "node2"),
            self._create_mock_edge("node2", "node3"),
            self._create_mock_edge("node3", "node1")
        ]
        
        # Build adjacency list
        adjacency_list = loop_service._build_adjacency_list(nodes, edges)
        
        # Detect cycles
        cycles = loop_service._detect_simple_cycles(adjacency_list, nodes)
        
        # Should NOT detect as infinite loop (safe loop)
        assert len(cycles) == 0, "Should not detect infinite loop - contains question node"
        
        print("✅ Safe loop with question node not flagged as infinite")
    
    def test_safe_loop_with_buttons_node(self):
        """Test that loops containing buttons nodes are NOT flagged as infinite"""
        from app.services.loop_detection_service import LoopDetectionService
        
        loop_service = LoopDetectionService()
        
        # Create nodes with a buttons node in the loop
        nodes = [
            self._create_mock_node("node1", "sendMessage"),
            self._create_mock_node("node2", "buttons"),  # User input required
            self._create_mock_node("node3", "condition")
        ]
        
        # Create edges forming a loop
        edges = [
            self._create_mock_edge("node1", "node2"),
            self._create_mock_edge("node2", "node3"),
            self._create_mock_edge("node3", "node1")
        ]
        
        # Build adjacency list
        adjacency_list = loop_service._build_adjacency_list(nodes, edges)
        
        # Detect cycles
        cycles = loop_service._detect_simple_cycles(adjacency_list, nodes)
        
        # Should NOT detect as infinite loop
        assert len(cycles) == 0, "Should not detect infinite loop - contains buttons node"
        
        print("✅ Safe loop with buttons node not flagged as infinite")
    
    def test_safe_loop_with_list_node(self):
        """Test that loops containing list nodes are NOT flagged as infinite"""
        from app.services.loop_detection_service import LoopDetectionService
        
        loop_service = LoopDetectionService()
        
        # Create nodes with a list node in the loop
        nodes = [
            self._create_mock_node("node1", "list"),  # User input required
            self._create_mock_node("node2", "sendMessage"),
            self._create_mock_node("node3", "condition")
        ]
        
        # Create edges forming a loop
        edges = [
            self._create_mock_edge("node1", "node2"),
            self._create_mock_edge("node2", "node3"),
            self._create_mock_edge("node3", "node1")
        ]
        
        # Build adjacency list
        adjacency_list = loop_service._build_adjacency_list(nodes, edges)
        
        # Detect cycles
        cycles = loop_service._detect_simple_cycles(adjacency_list, nodes)
        
        # Should NOT detect as infinite loop
        assert len(cycles) == 0, "Should not detect infinite loop - contains list node"
        
        print("✅ Safe loop with list node not flagged as infinite")
    
    def test_is_infinite_loop_method(self):
        """Test _is_infinite_loop method logic"""
        from app.services.loop_detection_service import LoopDetectionService
        
        loop_service = LoopDetectionService()
        
        # Test all sendMessage nodes (infinite)
        node_type_map = {
            "node1": "sendMessage",
            "node2": "sendMessage"
        }
        assert loop_service._is_infinite_loop(["node1", "node2"], node_type_map) == True
        
        # Test with question node (safe)
        node_type_map = {
            "node1": "sendMessage",
            "node2": "question"
        }
        assert loop_service._is_infinite_loop(["node1", "node2"], node_type_map) == False
        
        # Test with buttons node (safe)
        node_type_map = {
            "node1": "condition",
            "node2": "buttons"
        }
        assert loop_service._is_infinite_loop(["node1", "node2"], node_type_map) == False
        
        # Test with list node (safe)
        node_type_map = {
            "node1": "sendMessage",
            "node2": "list"
        }
        assert loop_service._is_infinite_loop(["node1", "node2"], node_type_map) == False
        
        print("✅ _is_infinite_loop method works correctly")
    
    def test_scc_detection_with_user_input_nodes(self):
        """Test SCC detection filters out safe loops with user input"""
        from app.services.loop_detection_service import LoopDetectionService
        
        loop_service = LoopDetectionService()
        
        # Create an SCC with a list node (should be safe)
        nodes = [
            self._create_mock_node("node1", "list"),
            self._create_mock_node("node2", "sendMessage"),
            self._create_mock_node("node3", "condition")
        ]
        
        edges = [
            self._create_mock_edge("node1", "node2"),
            self._create_mock_edge("node2", "node3"),
            self._create_mock_edge("node3", "node1"),
            self._create_mock_edge("node2", "node1")
        ]
        
        adjacency_list = loop_service._build_adjacency_list(nodes, edges)
        scc_loops = loop_service._detect_strongly_connected_components(adjacency_list, nodes)
        
        # Should not detect as infinite (contains list node)
        assert len(scc_loops) == 0, "Should not detect infinite SCC - contains list node"
        
        print("✅ SCC with user input nodes not flagged as infinite")
    
    def test_user_input_node_types_classification(self):
        """Test that user input node types are correctly classified"""
        from app.services.loop_detection_service import LoopDetectionService
        
        loop_service = LoopDetectionService()
        
        assert "question" in loop_service.user_input_node_types
        assert "buttons" in loop_service.user_input_node_types
        assert "list" in loop_service.user_input_node_types
        
        print("✅ User input node types correctly defined")
    
    # Helper methods
    def _create_mock_node(self, node_id: str, node_type: str):
        """Create a mock ChatbotNode"""
        node = MagicMock()
        node.node_id = node_id
        node.type = node_type
        return node
    
    def _create_mock_edge(self, source: str, target: str):
        """Create a mock ChatbotEdge"""
        edge = MagicMock()
        edge.source_node = source
        edge.target_node = target
        return edge


class TestListNodeEntityFieldUpdates:
    """Test cases for list node entity field updates (SD-26938 fix)"""
    
    @pytest.mark.asyncio
    async def test_list_node_entity_field_update_triggered(self):
        """Test that entity field updates are triggered for list nodes"""
        print("🧪 Testing list node entity field updates are triggered...")
        
        listener = MessageEventListener()
        
        # Create mock current node (list node with entity fields)
        current_node = MagicMock()
        current_node.node_id = "list_node_1"
        current_node.type = "list"
        current_node.data = {
            "header": {"format": "text", "text": "Select an option"},
            "body": "Choose your preference",
            "footer": "Thank you",
            "menuButton": "Menu",
            "sections": [
                {
                    "id": "section-0",
                    "title": "Options",
                    "position": 0,
                    "rows": [
                        {"id": "section-0-row-0", "text": "Option A", "position": 0},
                        {"id": "section-0-row-1", "text": "Option B", "position": 1}
                    ]
                }
            ],
            "entityFields": [
                {
                    "entityType": "LEAD",
                    "fieldId": 129273,
                    "name": "firstName",
                    "standard": True,
                    "displayName": "First Name"
                }
            ]
        }
        current_node.entity_fields = []  # DB entity fields empty, will use JSON
        
        # Create conversation state
        state = {
            "conversation_id": str(uuid.uuid4()),
            "chatbot_id": str(uuid.uuid4()),
            "tenant_id": 2048,
            "user_id": 12345,
            "entity_details": [
                {"id": 100, "name": "Test Lead", "entity": "lead"}
            ],
            "answers": [],
            "rule_current_node_id": "list_node_1"
        }
        
        # Mock database and services
        with patch.object(ChatbotService, 'update_entities_after_conversation', new_callable=AsyncMock) as mock_update:
            mock_update.return_value = {
                "successful_updates": [{"entity": "lead", "field": "firstName"}],
                "failed_updates": []
            }
            
            with patch.object(EntityFieldService, 'substitute_node_variables') as mock_substitute:
                mock_substitute.return_value = current_node.data
                
                # Simulate processing list node response
                user_message = "section-0-row-0"  # User selected "Option A"
                
                # Create entity field service
                entity_field_service = EntityFieldService()
                field_values = {}
                
                # This is the key code being tested (from lines 403-540 of message_event_listener.py)
                node_type = current_node.type
                skip_entity_update = False
                
                # The fix: list nodes are now included
                if node_type in ["question", "buttons", "list"]:
                    node_data = current_node.data
                    
                    # Apply variable substitution
                    substituted_data = entity_field_service.substitute_node_variables(node_data, field_values)
                    question_text = substituted_data.get("text") or substituted_data.get("body") or ""
                    selected_value = user_message
                    
                    # Handle list node - extract selected row text
                    if node_type == "list":
                        sections = node_data.get("sections") or []
                        for section in sections:
                            rows = section.get("rows", [])
                            for row in rows:
                                row_id = row.get("id", "")
                                row_text = row.get("text", "")
                                if row_id == user_message:
                                    selected_value = row_text or row_id
                                    break
                    
                    # Build answer entries
                    if not skip_entity_update:
                        new_answers = []
                        json_entity_fields = node_data.get("entityFields") or []
                        
                        for ef in json_entity_fields:
                            field_name = ef.get("name") or ef.get("displayName")
                            entity_type = ef.get("entity_type") or ef.get("entityType")
                            field_id = ef.get("fieldId")
                            is_standard = ef.get("standard", False)
                            
                            answer_entry = {
                                "question_id": current_node.node_id,
                                "question": question_text,
                                "answer": selected_value,
                                "field_name": field_name,
                                "entity_type": entity_type,
                                "field_id": field_id,
                                "standard": is_standard
                            }
                            new_answers.append(answer_entry)
                        
                        # Verify answers were created
                        assert len(new_answers) == 1, "Should create 1 answer entry for list node"
                        assert new_answers[0]["field_name"] == "firstName"
                        assert new_answers[0]["entity_type"] == "LEAD"
                        assert new_answers[0]["answer"] == "Option A"  # Should resolve to row text, not ID
                        assert new_answers[0]["standard"] is True
                        
                        print(f"✅ List node entity field update triggered correctly")
                        print(f"   Answer entry: {new_answers[0]}")
    
    @pytest.mark.asyncio
    async def test_list_node_entity_field_with_row_id_selection(self):
        """Test entity field update when user selects using row ID"""
        print("🧪 Testing list node entity field update with row ID selection...")
        
        # Create list node with entity fields
        node_data = {
            "sections": [
                {
                    "id": "section-0",
                    "title": "Products",
                    "rows": [
                        {"id": "section-0-row-0", "text": "Product X", "position": 0},
                        {"id": "section-0-row-1", "text": "Product Y", "position": 1}
                    ]
                }
            ],
            "entityFields": [
                {
                    "entityType": "LEAD",
                    "fieldId": 886015,
                    "name": "cfProductYouAreInterestedIn",
                    "standard": False,
                    "displayName": "Product you are interested in?"
                }
            ]
        }
        
        # User message is the row ID
        user_message = "section-0-row-0"
        selected_value = user_message
        
        # Extract selected row text (simulating the fix)
        sections = node_data.get("sections") or []
        for section in sections:
            rows = section.get("rows", [])
            for row in rows:
                row_id = row.get("id", "")
                row_text = row.get("text", "")
                if row_id == user_message:
                    selected_value = row_text or row_id
                    break
        
        # Build answer entry
        json_entity_fields = node_data.get("entityFields") or []
        answer_entry = {
            "question_id": "list_node_1",
            "question": "Choose your preference",
            "answer": selected_value,
            "field_name": json_entity_fields[0].get("name"),
            "entity_type": json_entity_fields[0].get("entityType"),
            "field_id": json_entity_fields[0].get("fieldId"),
            "standard": json_entity_fields[0].get("standard", False)
        }
        
        # Verify the answer uses row text, not row ID
        assert answer_entry["answer"] == "Product X", f"Should use row text 'Product X', got '{answer_entry['answer']}'"
        assert answer_entry["field_name"] == "cfProductYouAreInterestedIn"
        assert answer_entry["entity_type"] == "LEAD"
        assert answer_entry["standard"] is False
        
        print(f"✅ List node row ID correctly resolved to text: '{answer_entry['answer']}'")
    
    @pytest.mark.asyncio
    async def test_list_node_entity_field_with_row_text_selection(self):
        """Test entity field update when user selects using row text"""
        print("🧪 Testing list node entity field update with row text selection...")
        
        # Create list node
        node_data = {
            "sections": [
                {
                    "id": "section-0",
                    "title": "Services",
                    "rows": [
                        {"id": "section-0-row-0", "text": "CRM Implementation", "position": 0},
                        {"id": "section-0-row-1", "text": "Lead Management", "position": 1}
                    ]
                }
            ],
            "entityFields": [
                {
                    "entityType": "CONTACT",
                    "fieldId": 889207,
                    "name": "cfProductYouAreInterestedIn",
                    "standard": False,
                    "displayName": "Product you are interested in?"
                }
            ]
        }
        
        # User message is the row text (rare, but possible)
        user_message = "CRM Implementation"
        selected_value = user_message
        
        # Extract selected row text (should remain the same)
        sections = node_data.get("sections") or []
        for section in sections:
            rows = section.get("rows", [])
            for row in rows:
                row_id = row.get("id", "")
                row_text = row.get("text", "")
                if row_text == user_message:
                    selected_value = row_text or row_id
                    break
        
        assert selected_value == "CRM Implementation"
        print(f"✅ List node row text selection handled correctly: '{selected_value}'")
    
    @pytest.mark.asyncio
    async def test_list_node_multiple_entity_fields(self):
        """Test that multiple entity fields are updated for a single list selection"""
        print("🧪 Testing list node with multiple entity fields...")
        
        # Create list node with multiple entity fields (LEAD and CONTACT)
        node_data = {
            "body": "Select your department",
            "sections": [
                {
                    "id": "section-0",
                    "title": "Departments",
                    "rows": [
                        {"id": "section-0-row-0", "text": "Sales", "position": 0},
                        {"id": "section-0-row-1", "text": "Marketing", "position": 1}
                    ]
                }
            ],
            "entityFields": [
                {
                    "entityType": "LEAD",
                    "fieldId": 129313,
                    "name": "designation",
                    "standard": True,
                    "displayName": "Designation"
                },
                {
                    "entityType": "CONTACT",
                    "fieldId": 129381,
                    "name": "designation",
                    "standard": True,
                    "displayName": "Designation"
                }
            ]
        }
        
        user_message = "section-0-row-0"
        selected_value = user_message
        
        # Extract row text
        sections = node_data.get("sections") or []
        for section in sections:
            rows = section.get("rows", [])
            for row in rows:
                if row.get("id") == user_message:
                    selected_value = row.get("text") or row.get("id")
                    break
        
        # Build multiple answer entries (one per entity field)
        new_answers = []
        json_entity_fields = node_data.get("entityFields") or []
        question_text = node_data.get("body", "")
        
        for ef in json_entity_fields:
            answer_entry = {
                "question_id": "list_node_1",
                "question": question_text,
                "answer": selected_value,
                "field_name": ef.get("name"),
                "entity_type": ef.get("entityType"),
                "field_id": ef.get("fieldId"),
                "standard": ef.get("standard", False)
            }
            new_answers.append(answer_entry)
        
        # Verify both LEAD and CONTACT entity fields get answer entries
        assert len(new_answers) == 2, f"Should create 2 answer entries, got {len(new_answers)}"
        
        lead_answer = next((a for a in new_answers if a["entity_type"] == "LEAD"), None)
        contact_answer = next((a for a in new_answers if a["entity_type"] == "CONTACT"), None)
        
        assert lead_answer is not None, "Should have LEAD answer entry"
        assert contact_answer is not None, "Should have CONTACT answer entry"
        assert lead_answer["answer"] == "Sales"
        assert contact_answer["answer"] == "Sales"
        assert lead_answer["field_name"] == "designation"
        assert contact_answer["field_name"] == "designation"
        
        print(f"✅ List node with multiple entity fields creates multiple answer entries")
        print(f"   LEAD answer: {lead_answer}")
        print(f"   CONTACT answer: {contact_answer}")
    
    @pytest.mark.asyncio
    async def test_list_node_without_entity_fields_no_update(self):
        """Test that list nodes without entity fields don't trigger updates"""
        print("🧪 Testing list node without entity fields...")
        
        # Create list node WITHOUT entity fields
        node_data = {
            "body": "Select an option",
            "sections": [
                {
                    "id": "section-0",
                    "title": "Options",
                    "rows": [
                        {"id": "section-0-row-0", "text": "Option 1", "position": 0}
                    ]
                }
            ],
            "entityFields": []  # No entity fields
        }
        
        user_message = "section-0-row-0"
        json_entity_fields = node_data.get("entityFields") or []
        new_answers = []
        
        for ef in json_entity_fields:
            new_answers.append({"field_name": ef.get("name")})
        
        # No answers should be created
        assert len(new_answers) == 0, "Should not create answer entries when no entity fields configured"
        print(f"✅ List node without entity fields correctly skips entity updates")
    
    @pytest.mark.asyncio
    async def test_list_node_entity_field_with_standard_and_custom_fields(self):
        """Test entity field updates with mix of standard and custom fields"""
        print("🧪 Testing list node with standard and custom entity fields...")
        
        node_data = {
            "body": "Select product",
            "sections": [
                {
                    "id": "section-0",
                    "title": "Products",
                    "rows": [
                        {"id": "section-0-row-0", "text": "Premium Plan", "position": 0}
                    ]
                }
            ],
            "entityFields": [
                {
                    "entityType": "LEAD",
                    "fieldId": 129282,
                    "name": "zipcode",
                    "standard": True,  # Standard field
                    "displayName": "Zipcode"
                },
                {
                    "entityType": "LEAD",
                    "fieldId": 886015,
                    "name": "cfProductYouAreInterestedIn",
                    "standard": False,  # Custom field
                    "displayName": "Product you are interested in?"
                }
            ]
        }
        
        user_message = "section-0-row-0"
        selected_value = "Premium Plan"
        
        new_answers = []
        json_entity_fields = node_data.get("entityFields") or []
        
        for ef in json_entity_fields:
            answer_entry = {
                "field_name": ef.get("name"),
                "entity_type": ef.get("entityType"),
                "answer": selected_value,
                "standard": ef.get("standard", False)
            }
            new_answers.append(answer_entry)
        
        # Verify both standard and custom fields are included
        assert len(new_answers) == 2
        
        standard_answer = next((a for a in new_answers if a["standard"]), None)
        custom_answer = next((a for a in new_answers if not a["standard"]), None)
        
        assert standard_answer is not None
        assert custom_answer is not None
        assert standard_answer["field_name"] == "zipcode"
        assert custom_answer["field_name"] == "cfProductYouAreInterestedIn"
        assert standard_answer["answer"] == "Premium Plan"
        assert custom_answer["answer"] == "Premium Plan"
        
        print(f"✅ List node handles both standard and custom fields correctly")
        print(f"   Standard field: {standard_answer['field_name']}")
        print(f"   Custom field: {custom_answer['field_name']}")
    
    @pytest.mark.asyncio
    async def test_list_node_entity_field_before_fix_would_fail(self):
        """
        Test demonstrating the bug that existed before the fix.
        Before: node_type in ["question", "buttons"] - list nodes excluded
        After: node_type in ["question", "buttons", "list"] - list nodes included
        """
        print("🧪 Testing that list nodes are now included in entity field updates...")
        
        node_type = "list"
        
        # Before fix: this would be False
        before_fix = node_type in ["question", "buttons"]
        
        # After fix: this should be True
        after_fix = node_type in ["question", "buttons", "list"]
        
        assert before_fix is False, "Before fix: list nodes were excluded"
        assert after_fix is True, "After fix: list nodes are included"
        
        print(f"✅ Confirmed fix includes list nodes in entity field updates")
        print(f"   Before fix: {before_fix} (list nodes excluded)")
        print(f"   After fix: {after_fix} (list nodes included)")
    
    @pytest.mark.asyncio
    async def test_list_node_entity_field_integration(self):
        """
        Integration test: Full flow of list node selection -> entity field update
        """
        print("🧪 Testing full list node entity field update flow...")
        
        current_node = MagicMock()
        current_node.node_id = "list_test"
        current_node.type = "list"
        current_node.data = {
            "body": "Choose breakfast",
            "sections": [
                {
                    "id": "section-0",
                    "title": "Menu",
                    "rows": [
                        {"id": "section-0-row-0", "text": "Poha", "position": 0},
                        {"id": "section-0-row-1", "text": "Upma", "position": 1}
                    ]
                }
            ],
            "entityFields": [
                {
                    "entityType": "LEAD",
                    "fieldId": 129313,
                    "name": "designation",
                    "standard": True,
                    "displayName": "Designation"
                }
            ]
        }
        current_node.entity_fields = []
        
        # User selects "Poha"
        user_message = "section-0-row-0"
        
        # Simulate the full flow
        node_type = current_node.type
        
        # 1. Check if entity updates should be triggered
        should_process = node_type in ["question", "buttons", "list"]
        assert should_process is True, "List node should be processed for entity updates"
        
        # 2. Extract selected value
        node_data = current_node.data
        selected_value = user_message
        sections = node_data.get("sections") or []
        for section in sections:
            rows = section.get("rows", [])
            for row in rows:
                if row.get("id") == user_message:
                    selected_value = row.get("text") or row.get("id")
                    break
        
        assert selected_value == "Poha", f"Should resolve to 'Poha', got '{selected_value}'"
        
        # 3. Build answer entries
        json_entity_fields = node_data.get("entityFields") or []
        new_answers = []
        
        for ef in json_entity_fields:
            answer_entry = {
                "question_id": current_node.node_id,
                "question": node_data.get("body", ""),
                "answer": selected_value,
                "field_name": ef.get("name"),
                "entity_type": ef.get("entityType"),
                "field_id": ef.get("fieldId"),
                "standard": ef.get("standard", False)
            }
            new_answers.append(answer_entry)
        
        # 4. Verify answer was created correctly
        assert len(new_answers) == 1
        assert new_answers[0]["answer"] == "Poha"
        assert new_answers[0]["field_name"] == "designation"
        assert new_answers[0]["entity_type"] == "LEAD"
        
        print(f"✅ Full integration test passed")
        print(f"   Selected: '{user_message}' -> Resolved to: '{selected_value}'")
        print(f"   Entity field: {new_answers[0]['field_name']} = {new_answers[0]['answer']}")


# Run tests if executed directly
if __name__ == "__main__":
    import asyncio
    
    print("=" * 80)
    print("List Node Entity Field Update Tests (SD-26938)")
    print("=" * 80)
    print()
    
    test_class = TestListNodeEntityFieldUpdates()
    
    async def run_all_tests():
        try:
            await test_class.test_list_node_entity_field_update_triggered()
            print()
            await test_class.test_list_node_entity_field_with_row_id_selection()
            print()
            await test_class.test_list_node_entity_field_with_row_text_selection()
            print()
            await test_class.test_list_node_entity_field_with_standard_and_custom_fields()
            print()
            await test_class.test_list_node_entity_field_before_fix_would_fail()
            print()
            await test_class.test_list_node_entity_field_integration()
            print()
            print("=" * 80)
            print("✅ All list node entity field update tests passed!")
            print("=" * 80)
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
    
    asyncio.run(run_all_tests())


class TestVariableMappingIndependence:
    """Test cases for variable mapping independence between chained nodes (SD-26938 fix)"""
    
    @pytest.mark.asyncio
    async def test_chained_nodes_variable_mapping_independence(self):
        """
        Test that each node's variable mappings are independent.
        When a node has its own variable mappings, it should NOT inherit
        fallback values from the parent node.
        """
        print("🧪 Testing variable mapping independence between chained nodes...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        # Only LEAD entity available (no CONTACT)
        entity_details = [
            {"id": 595245, "name": "Akshay", "entity": "lead"}
        ]
        auth_token = "dummy_token"
        
        # Parent node (list): variable "1" → contact.lastName → fallback "Parent Fallback"
        parent_mappings = [
            {
                "entity": "contact",  # Not available!
                "internalName": "lastName",
                "fallbackValue": "Parent Fallback",
                "variable": "1",
                "componentType": "BODY",
                "fieldType": "TEXT_FIELD"
            }
        ]
        
        # Child node: variable "1" → lead.firstName → fallback "Child Fallback"
        child_mappings = [
            {
                "entity": "lead",  # Available but field might be empty
                "internalName": "firstName",
                "fallbackValue": "Child Fallback",
                "variable": "1",
                "componentType": "BODY",
                "fieldType": "TEXT_FIELD"
            }
        ]
        
        # Get parent field values
        parent_field_values = entity_field_service.get_entity_field_values_from_mappings(
            entity_details, parent_mappings, auth_token
        )
        assert parent_field_values["1"] == "Parent Fallback"
        
        # Simulate _build_node_details with the FIX
        # If child has own mappings, DON'T inherit from parent
        if child_mappings:  # Child has own mappings
            node_field_values = entity_field_service.get_entity_field_values_from_mappings(
                entity_details, child_mappings, auth_token
            )
        else:  # No mappings - inherit
            node_field_values = parent_field_values.copy()
        
        # Verify child uses its own fallback, NOT parent's
        assert node_field_values["1"] == "Child Fallback", f"Expected 'Child Fallback', got '{node_field_values['1']}'"
        assert node_field_values["1"] != "Parent Fallback", "Should NOT use parent's fallback"
        
        print(f"✅ Child node uses own fallback: '{node_field_values['1']}'")
        print(f"   Parent fallback was: 'Parent Fallback'")
        print(f"   Child fallback is: 'Child Fallback'")
    
    @pytest.mark.asyncio
    async def test_node_without_mappings_no_inheritance(self):
        """
        Test that nodes WITHOUT variable mappings do NOT inherit from parent.
        Each node's variable mappings are completely independent.
        """
        print("🧪 Testing that nodes without mappings have no variables...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        entity_details = [
            {"id": 595245, "name": "Akshay", "entity": "lead"}
        ]
        auth_token = "dummy_token"
        
        # Parent node has mappings
        parent_mappings = [
            {
                "entity": "contact",
                "internalName": "lastName",
                "fallbackValue": "Parent Value",
                "variable": "1",
                "componentType": "BODY",
                "fieldType": "TEXT_FIELD"
            }
        ]
        
        # Child node has NO mappings
        child_mappings = []
        
        # Get parent values
        parent_field_values = entity_field_service.get_entity_field_values_from_mappings(
            entity_details, parent_mappings, auth_token
        )
        
        # Simulate _build_node_details with FIX
        if child_mappings:
            node_field_values = entity_field_service.get_entity_field_values_from_mappings(
                entity_details, child_mappings, auth_token
            )
        else:
            # No mappings - DON'T inherit (use empty dict)
            node_field_values = {}
        
        # Verify NO inheritance
        assert node_field_values == {}, f"Should be empty dict, got {node_field_values}"
        assert "1" not in node_field_values, "Should NOT inherit variable '1' from parent"
        
        print(f"✅ Child node does NOT inherit when it has no mappings")
        print(f"   Parent had: {list(parent_field_values.keys())}")
        print(f"   Child has: {list(node_field_values.keys())} (empty)")
    
    @pytest.mark.asyncio
    async def test_list_to_sendmessage_variable_independence(self):
        """
        Specific test for list node → sendMessage node scenario.
        Each node should use its own variable mappings independently.
        """
        print("🧪 Testing list → sendMessage variable mapping independence...")
        
        from app.services.entity_field_service import EntityFieldService
        
        entity_field_service = EntityFieldService()
        
        # Only LEAD entity
        entity_details = [
            {"id": 100, "name": "Test User", "entity": "lead"}
        ]
        auth_token = "dummy_token"
        
        # List node: variable "1" → contact.field1 → fallback "ListDefault"
        # List node: variable "2" → contact.field2 → fallback "ListDefault2"
        list_mappings = [
            {
                "entity": "contact",  # Not available
                "internalName": "field1",
                "fallbackValue": "ListDefault",
                "variable": "1",
                "componentType": "BODY",
                "fieldType": "TEXT_FIELD"
            },
            {
                "entity": "contact",
                "internalName": "field2",
                "fallbackValue": "ListDefault2",
                "variable": "2",
                "componentType": "BODY",
                "fieldType": "TEXT_FIELD"
            }
        ]
        
        # Next sendMessage node: variable "1" → lead.fieldA → fallback "SendMessageDefault"
        # Note: doesn't define variable "2"
        sendmessage_mappings = [
            {
                "entity": "lead",  # Available
                "internalName": "fieldA",
                "fallbackValue": "SendMessageDefault",
                "variable": "1",
                "componentType": "BODY",
                "fieldType": "TEXT_FIELD"
            }
        ]
        
        # Get list node values
        list_field_values = entity_field_service.get_entity_field_values_from_mappings(
            entity_details, list_mappings, auth_token
        )
        
        # Get sendMessage node values (with fix: DON'T inherit from parent)
        sendmessage_field_values = entity_field_service.get_entity_field_values_from_mappings(
            entity_details, sendmessage_mappings, auth_token
        )
        
        # Verify:
        # 1. Variable "1" uses sendMessage's mapping, not list's
        # 2. Variable "2" is NOT present (not inherited from list)
        
        assert sendmessage_field_values.get("1") == "SendMessageDefault"
        assert "2" not in sendmessage_field_values  # Should NOT inherit variable "2"
        
        print(f"✅ SendMessage node uses only its own mappings")
        print(f"   Variable '1': '{sendmessage_field_values.get('1')}' (own mapping)")
        print(f"   Variable '2': {sendmessage_field_values.get('2', 'NOT PRESENT')} (not inherited)")
    
    @pytest.mark.asyncio
    async def test_before_and_after_fix_comparison(self):
        """
        Demonstrate the difference before and after the fix.
        """
        print("🧪 Comparing behavior before and after fix...")
        
        from app.services.entity_field_service import EntityFieldService
        
        service = EntityFieldService()
        
        entity_details = [{"id": 100, "entity": "lead"}]
        auth_token = "dummy"
        
        parent_mappings = [
            {"entity": "contact", "internalName": "f1", "fallbackValue": "ParentValue", "variable": "1", "componentType": "BODY", "fieldType": "TEXT_FIELD"}
        ]
        child_mappings = [
            {"entity": "lead", "internalName": "f2", "fallbackValue": "ChildValue", "variable": "1", "componentType": "BODY", "fieldType": "TEXT_FIELD"}
        ]
        
        parent_values = service.get_entity_field_values_from_mappings(entity_details, parent_mappings, auth_token)
        child_values = service.get_entity_field_values_from_mappings(entity_details, child_mappings, auth_token)
        
        # BEFORE FIX: child_values.copy() then update()
        before_fix = parent_values.copy()
        before_fix.update(child_values)
        
        # AFTER FIX: use ONLY child_values when child has mappings
        after_fix = child_values  # Don't inherit from parent
        
        print(f"  Parent value: variable '1' = '{parent_values['1']}'")
        print(f"  Child value: variable '1' = '{child_values['1']}'")
        print(f"  Before fix: variable '1' = '{before_fix['1']}'")
        print(f"  After fix: variable '1' = '{after_fix['1']}'")
        
        # Both should actually give the same result when both have mappings
        # The real difference is when variables don't overlap
        assert before_fix["1"] == after_fix["1"]  # Both use child's value
        
        print(f"✅ Fix ensures child mappings take precedence")


# Add to main execution
if __name__ == "__main__":
    import asyncio
    
    print("=" * 80)
    print("Variable Mapping Independence Tests")
    print("=" * 80)
    print()
    
    test_class = TestVariableMappingIndependence()
    
    async def run_tests():
        try:
            await test_class.test_chained_nodes_variable_mapping_independence()
            print()
            await test_class.test_node_without_mappings_inherits_parent_values()
            print()
            await test_class.test_list_to_sendmessage_variable_independence()
            print()
            await test_class.test_before_and_after_fix_comparison()
            print()
            print("=" * 80)
            print("✅ All variable mapping independence tests passed!")
            print("=" * 80)
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
    
    asyncio.run(run_tests())
