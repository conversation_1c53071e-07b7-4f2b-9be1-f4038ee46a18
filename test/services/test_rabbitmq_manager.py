#!/usr/bin/env python3
"""
Test script to verify RabbitMQ integration structure and configuration
"""

import os
import sys

def test_dependencies():
    """Test that RabbitMQ dependencies are properly configured"""
    print("Testing RabbitMQ dependencies...")
    
    # Check requirements.txt
    try:
        with open('requirements.txt', 'r') as f:
            requirements = f.read()
        
        if 'aio-pika' in requirements:
            print("✓ aio-pika dependency found in requirements.txt")
            return True
        else:
            print("✗ aio-pika dependency not found in requirements.txt")
            return False
    except FileNotFoundError:
        print("✗ requirements.txt not found")
        return False


def test_environment_configuration():
    """Test RabbitMQ environment configuration"""
    print("\nTesting environment configuration...")
    
    # Check .env file
    try:
        with open('.env', 'r') as f:
            env_content = f.read()
        
        rabbitmq_vars = [
            'RABBITMQ_URL',
            'RABBITMQ_CONNECTION_TIMEOUT',
            'RABBITMQ_HEARTBEAT',
            'SERVICE_NAME'
        ]
        
        found_vars = []
        for var in rabbitmq_vars:
            if var in env_content:
                found_vars.append(var)
                print(f"  ✓ {var} configured")
            else:
                print(f"  ✗ {var} not found")
        
        if len(found_vars) == len(rabbitmq_vars):
            print("✓ All RabbitMQ environment variables configured")
            return True
        else:
            print(f"✗ Missing {len(rabbitmq_vars) - len(found_vars)} environment variables")
            return False
            
    except FileNotFoundError:
        print("✗ .env file not found")
        return False


def test_service_files():
    """Test that RabbitMQ service files exist and have correct structure"""
    print("\nTesting service files...")
    
    files_to_check = [
        'app/services/rabbitmq_service.py',
        'app/services/event_listeners.py'
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"  ✓ {file_path} exists")
        else:
            print(f"  ✗ {file_path} not found")
            all_exist = False
    
    return all_exist


def test_rabbitmq_service_structure():
    """Test RabbitMQ service class structure"""
    print("\nTesting RabbitMQ service structure...")
    
    try:
        with open('app/services/rabbitmq_service.py', 'r') as f:
            content = f.read()
        
        required_methods = [
            'class RabbitMQService',
            'async def connect',
            'async def disconnect',
            'async def declare_exchange',
            'async def declare_queue',
            'async def bind_queue_to_exchange',
            'def register_event_handler',
            'async def message_handler',
            'async def start_consuming',
            'async def setup_scheduler_listener',
            'async def handle_collect_usage_event'
        ]
        
        found_methods = []
        for method in required_methods:
            if method in content:
                found_methods.append(method)
                print(f"  ✓ {method}")
            else:
                print(f"  ✗ {method} not found")
        
        if len(found_methods) == len(required_methods):
            print("✓ RabbitMQ service structure is complete")
            return True
        else:
            print(f"✗ Missing {len(required_methods) - len(found_methods)} required methods")
            return False
            
    except FileNotFoundError:
        print("✗ rabbitmq_service.py not found")
        return False


def test_event_listener_structure():
    """Test event listener structure"""
    print("\nTesting event listener structure...")
    
    try:
        with open('app/services/event_listeners.py', 'r') as f:
            content = f.read()
        
        required_components = [
            'class SchedulerEventListener',
            'class EventListenerManager',
            'async def start',
            'async def stop',
            'async def handle_collect_usage_event',
            'async def start_all',
            'async def stop_all',
            'event_listener_manager'
        ]
        
        found_components = []
        for component in required_components:
            if component in content:
                found_components.append(component)
                print(f"  ✓ {component}")
            else:
                print(f"  ✗ {component} not found")
        
        if len(found_components) == len(required_components):
            print("✓ Event listener structure is complete")
            return True
        else:
            print(f"✗ Missing {len(required_components) - len(found_components)} required components")
            return False
            
    except FileNotFoundError:
        print("✗ event_listeners.py not found")
        return False


def test_fastapi_integration():
    """Test FastAPI integration"""
    print("\nTesting FastAPI integration...")
    
    try:
        with open('app/main.py', 'r') as f:
            content = f.read()
        
        integration_components = [
            'from app.services.event_listeners import event_listener_manager',
            '@app.on_event("startup")',
            '@app.on_event("shutdown")',
            'await event_listener_manager.start_all()',
            'await event_listener_manager.stop_all()'
        ]
        
        found_components = []
        for component in integration_components:
            if component in content:
                found_components.append(component)
                print(f"  ✓ {component}")
            else:
                print(f"  ✗ {component} not found")
        
        if len(found_components) == len(integration_components):
            print("✓ FastAPI integration is complete")
            return True
        else:
            print(f"✗ Missing {len(integration_components) - len(found_components)} integration components")
            return False
            
    except FileNotFoundError:
        print("✗ main.py not found")
        return False


def test_scheduler_event_configuration():
    """Test scheduler event configuration"""
    print("\nTesting scheduler event configuration...")
    
    try:
        with open('app/services/rabbitmq_service.py', 'r') as f:
            content = f.read()
        
        scheduler_config = [
            'ex.scheduler',
            'scheduler.collect.usage',
            'topic',
            'durable=True'
        ]
        
        found_config = []
        for config in scheduler_config:
            if config in content:
                found_config.append(config)
                print(f"  ✓ {config} configured")
            else:
                print(f"  ✗ {config} not found")
        
        if len(found_config) == len(scheduler_config):
            print("✓ Scheduler event configuration is complete")
            return True
        else:
            print(f"✗ Missing {len(scheduler_config) - len(found_config)} configuration items")
            return False
            
    except FileNotFoundError:
        print("✗ rabbitmq_service.py not found")
        return False


def test_error_handling():
    """Test error handling implementation"""
    print("\nTesting error handling...")
    
    try:
        with open('app/services/rabbitmq_service.py', 'r') as f:
            rabbitmq_content = f.read()
        
        with open('app/services/event_listeners.py', 'r') as f:
            listener_content = f.read()
        
        error_handling_patterns = [
            'try:',
            'except Exception as e:',
            'logger.error',
            'await message.ack()',
            'await message.reject(requeue=False)'
        ]
        
        all_content = rabbitmq_content + listener_content
        
        found_patterns = []
        for pattern in error_handling_patterns:
            if pattern in all_content:
                found_patterns.append(pattern)
                print(f"  ✓ {pattern} implemented")
            else:
                print(f"  ✗ {pattern} not found")
        
        if len(found_patterns) >= 4:  # Allow some flexibility
            print("✓ Error handling is properly implemented")
            return True
        else:
            print("✗ Insufficient error handling implementation")
            return False
            
    except FileNotFoundError:
        print("✗ Service files not found")
        return False


def main():
    """Run all structure tests"""
    print("=" * 70)
    print("RabbitMQ Integration Structure Test Suite")
    print("=" * 70)
    
    tests = [
        test_dependencies,
        test_environment_configuration,
        test_service_files,
        test_rabbitmq_service_structure,
        test_event_listener_structure,
        test_fastapi_integration,
        test_scheduler_event_configuration,
        test_error_handling
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed: {str(e)}")
            results.append(False)
    
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 70)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All structure tests passed! RabbitMQ integration is properly configured.")
        print("\nImplementation Summary:")
        print("  • RabbitMQ dependencies added to requirements.txt")
        print("  • Environment configuration added to .env")
        print("  • RabbitMQ service class with connection management")
        print("  • Event listener for scheduler.collect.usage events")
        print("  • FastAPI integration with startup/shutdown handlers")
        print("  • Proper error handling and logging")
        print("  • Exchange: ex.scheduler (topic)")
        print("  • Event: scheduler.collect.usage (empty payload)")
    else:
        print("✗ Some structure tests failed. Please review the implementation.")
    
    print("=" * 70)


if __name__ == "__main__":
    main()
