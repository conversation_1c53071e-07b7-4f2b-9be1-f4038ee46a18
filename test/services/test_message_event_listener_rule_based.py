import pytest
import uuid
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.message_event_listener import message_event_listener, MessageEventListener
from app.services.conversation_event_publisher import conversation_event_publisher


@pytest.mark.asyncio
async def test_rule_based_traversal_question_to_send_message(monkeypatch):
    conversation_id = str(uuid.uuid4())

    # Mock DB session and models
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Start node (question) with two options, edges to next node
    start_node = Node("n1", "question", {
        "text": "What do you want?",
        "options": [
            {"name": "1", "text": "pdf document"},
            {"name": "2", "text": "excel document"},
            {"name": "3", "text": "word document"}
        ]
    })
    next_node = Node("n2", "sendMessage", {
        "options": [
            {"type": "text", "text": "You picked A"}
        ]
    })
    edges = [Edge("n1", "optA", "n2")]

    # Patch helper methods to avoid DB
    message_event_listener._get_start_node = lambda db, state: start_node
    message_event_listener._load_node = lambda db, state, node_id: start_node if node_id == "n1" else next_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: edges if node_id == "n1" else []

    # Mock publisher
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = AsyncMock(return_value=True)

        state = {
            "chatbot_id": "cb-1",
            "tenant_id": 1,
            "chatbotType": "RULE",
            "history": []
        }

        # Numeric selection
        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state, "1", conversation_id
        )

        # Verify sendMessage publishes nodeDetails and message is None
        assert mock_publish.await_count == 1
        args, kwargs = mock_publish.await_args
        assert kwargs["chatbot_conversation_id"] == conversation_id
        assert kwargs["message"] is None
        assert kwargs["completed"] is False
        assert kwargs["extra"].get("chatbotType") == "RULE"
        nd = kwargs["extra"].get("nodeDetails")
        assert nd and nd.get("type") == "sendMessage"
        blocks = nd.get("data") or []
        assert {"type": "text", "text": "You picked A"} in blocks

    # Also test text selection mapping to same edge
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish2:
        mock_publish2.return_value = AsyncMock(return_value=True)

        state2 = {
            "chatbot_id": "cb-1",
            "tenant_id": 1,
            "chatbotType": "RULE",
            "history": []
        }

        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state2, "pdf document", conversation_id
        )

        assert mock_publish2.await_count == 1


@pytest.mark.asyncio
async def test_rule_based_traversal_button_match(monkeypatch):
    conversation_id = str(uuid.uuid4())
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    start_node = Node("n1", "button", {
        "body": "Choose:",
        "buttons": [
            {"name": "yes", "text": "Yes", "position": 1},
            {"name": "no", "text": "No", "position": 2}
        ]
    })
    next_node = Node("n2", "sendMessage", {"options": [{"type": "text", "text": "Confirmed"}]})
    edges = [Edge("n1", "yes", "n2")]

    message_event_listener._get_start_node = lambda db, state: start_node
    message_event_listener._load_node = lambda db, state, node_id: start_node if node_id == "n1" else next_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: edges if node_id == "n1" else []

    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = AsyncMock(return_value=True)

        state = {"chatbot_id": "cb-1", "tenant_id": 1, "chatbotType": "RULE", "history": []}

        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state, "Yes", conversation_id
        )

        assert mock_publish.await_count == 1
        args, kwargs = mock_publish.await_args
        assert kwargs["message"] is None
        assert kwargs["completed"] is False
        nd = kwargs["extra"].get("nodeDetails")
        assert nd and nd.get("type") == "sendMessage"
        blocks = nd.get("data") or []
        assert {"type": "text", "text": "Confirmed"} in blocks


@pytest.mark.asyncio
async def test_rule_based_condition_auto_advance(monkeypatch):
    conversation_id = str(uuid.uuid4())
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    condition_node = Node("n1", "condition", {"conditions": [{"name": "go", "value": "ok"}]})
    target_node = Node("n2", "sendMessage", {"options": [{"type": "text", "text": "Done"}]})
    edges = [Edge("n1", "go", "n2")]

    message_event_listener._get_start_node = lambda db, state: condition_node
    message_event_listener._load_node = lambda db, state, node_id: condition_node if node_id == "n1" else target_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: edges if node_id == "n1" else []

    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = AsyncMock(return_value=True)

        state = {"chatbot_id": "cb-1", "tenant_id": 1, "chatbotType": "RULE", "history": []}

        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state, "ok", conversation_id
        )

        assert mock_publish.await_count == 1
        args, kwargs = mock_publish.await_args
        assert kwargs["message"] is None
        assert kwargs["completed"] is False
        nd = kwargs["extra"].get("nodeDetails")
        assert nd and nd.get("type") == "sendMessage"
        blocks = nd.get("data") or []
        assert {"type": "text", "text": "Done"} in blocks


@pytest.mark.asyncio
async def test_rule_based_send_message_with_media(monkeypatch):
    conversation_id = str(uuid.uuid4())
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, type, data, name="n4"):
            self.node_id = node_id
            self.type = type
            self.data = data
            self.name = name

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    start_node = Node("n1", "question", {"text": "Pick:", "options": [{"name": "h1", "text": "Show"}]}, name="n1")
    media_payload = {
        "fileId": 11,
        "fileName": "myPhonto.png",
        "fileSize": 111111,
        "fileType": "png",
        "fileCaption": "caption to show"
    }
    next_node = Node("n4", "sendMessage", {
        "options": [
            {"type": "text", "text": "Question to be ask {{1}}"},
            {"type": "media", "mediaFile": media_payload}
        ]
    }, name="n4")
    edges = [Edge("n1", "h1", "n4")]

    message_event_listener._get_start_node = lambda db, state: start_node
    message_event_listener._load_node = lambda db, state, node_id: start_node if node_id == "n1" else next_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: edges if node_id == "n1" else []

    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = AsyncMock(return_value=True)

        state = {"chatbot_id": "cb-1", "tenant_id": 1, "chatbotType": "RULE", "history": []}

        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state, "Show", conversation_id
        )

        assert mock_publish.await_count == 1
        args, kwargs = mock_publish.await_args
        assert kwargs["message"] is None
        nd = kwargs["extra"].get("nodeDetails")
        assert nd and nd.get("type") == "sendMessage"
        blocks = nd.get("data") or []
        assert {"type": "text", "text": "Question to be ask {{1}}"} in blocks
        assert {"type": "media", "mediaFile": media_payload} in blocks


@pytest.mark.asyncio
async def test_rule_based_conversation_completion_event():
    """Test that conversation completion events are published when rule-based flow ends"""
    conversation_id = str(uuid.uuid4())
    
    # Mock DB session and models
    mock_db = MagicMock()
    
    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
    
    # Create a node with no outgoing edges (end of flow)
    end_node = Node("end_node", "question", {
        "text": "Thank you for your time!",
        "options": []
    })
    
    # Mock state with entity details
    state = {
        "rule_current_node_id": "end_node",
        "chatbotType": "RULE",
        "tenant_id": 2048,
        "entity_details": [
            {"entityType": "CONTACT", "id": "contact_123"}
        ],
        "node_details": {"id": "end_node", "type": "question"}
    }
    
    # Mock publishers
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_response, \
         patch.object(conversation_event_publisher, 'publish_conversation_completion') as mock_completion:
        
        mock_response.return_value = True
        mock_completion.return_value = True
        
        # Mock Redis and DB operations
        with patch('app.services.redis_service.RedisService') as mock_redis, \
             patch('app.utils.conversation_state_utils.update_conversation_in_db'), \
             patch('app.services.entity_field_service.EntityFieldService') as mock_efs:
            
            mock_redis.return_value.store_conversation_state = MagicMock()
            mock_efs_instance = MagicMock()
            mock_efs_instance.substitute_node_variables.return_value = end_node.data
            mock_efs_instance.get_entity_field_values_from_mappings.return_value = {}
            mock_efs_instance.get_entity_details.return_value = {"ownerId": 12345, "entityName": "John Doe"}
            mock_efs.return_value = mock_efs_instance
            
            # Patch helper methods to simulate end of flow
            message_event_listener._load_node = lambda db, state, node_id: end_node
            message_event_listener._get_outgoing_edges = lambda db, state, node_id: []  # No outgoing edges = end
            message_event_listener._select_edge_for_input = lambda node, edges, input: None  # No edge selection
            message_event_listener._build_message_for_node = lambda node, field_values: "Thank you!"
            
            # Call the rule-based logic
            await message_event_listener._continue_rule_based_logic(
                mock_db, MagicMock(), state, "1", conversation_id
            )
            
            # Verify both events were published
            assert mock_response.called
            assert mock_completion.called
            
            # Verify completion event has correct data
            completion_call = mock_completion.call_args
            assert completion_call[1]['chatbot_conversation_id'] == conversation_id
            assert completion_call[1]['completion_message'] == "Thank you!"
            assert completion_call[1]['chatbot_type'] == "RULE"
            assert completion_call[1]['tenant_id'] == 2048
            assert completion_call[1]['entity_details'] == [{"entityType": "CONTACT", "id": "contact_123"}]
            assert completion_call[1]['node_details'] == {"id": "end_node", "type": "question"}


@pytest.mark.asyncio
async def test_question_node_details_formatting():
    """Test that question nodes have properly formatted nodeDetails with numbered options"""
    conversation_id = str(uuid.uuid4())
    
    # Mock DB session and models
    mock_db = MagicMock()
    
    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
    
    # Create a question node
    question_node = Node("question_node", "question", {
        "text": "What is your problem?",
        "options": [
            {"text": "problem 1", "name": "1", "position": 0},
            {"text": "problem 2", "name": "2", "position": 1},
            {"text": "problem 3", "name": "3", "position": 2}
        ]
    })
    
    # Mock state
    state = {
        "rule_current_node_id": "question_node",
        "chatbotType": "RULE",
        "tenant_id": 2048
    }
    
    # Patch helper methods
    message_event_listener._load_node = lambda db, state, node_id: question_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: []  # End of flow
    message_event_listener._select_edge_for_input = lambda node, edges, input: None  # No edge selection
    
    # Mock publishers
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = True
        
        # Mock Redis and DB operations
        with patch('app.services.redis_service.RedisService') as mock_redis, \
             patch('app.utils.conversation_state_utils.update_conversation_in_db'):
            
            mock_redis.return_value.store_conversation_state = MagicMock()
            
            # Call the rule-based logic
            await message_event_listener._continue_rule_based_logic(
                mock_db, MagicMock(), state, "1", conversation_id
            )
            
            # Verify the published event
            assert mock_publish.called
            
            # Check the nodeDetails format
            call_args = mock_publish.call_args
            extra_data = call_args[1]['extra']
            node_details = extra_data['nodeDetails']
            
            assert node_details is not None
            assert node_details['id'] == "question_node"
            assert node_details['type'] == "question"
            assert node_details['isFirstNode'] == False
            
            # Check formatted text has numbered options
            formatted_text = node_details['data']['text']
            assert "What is your problem?" in formatted_text
            assert "1. problem 1" in formatted_text
            assert "2. problem 2" in formatted_text
            assert "3. problem 3" in formatted_text
            
            # Check options array
            options = node_details['data']['options']
            assert len(options) == 3
            assert options[0]['text'] == "problem 1"
            assert options[0]['name'] == "1"
            assert options[0]['position'] == 0


@pytest.mark.asyncio
async def test_question_node_message_is_none():
    """Test that question nodes publish with message=None and rely on nodeDetails"""
    conversation_id = str(uuid.uuid4())
    
    # Mock DB session and models
    mock_db = MagicMock()
    
    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
    
    # Create a question node
    question_node = Node("question_node", "question", {
        "text": "What is your problem?",
        "options": [
            {"text": "problem 1", "name": "1", "position": 0}
        ]
    })
    
    # Mock state
    state = {
        "rule_current_node_id": "question_node",
        "chatbotType": "RULE",
        "tenant_id": 2048
    }
    
    # Patch helper methods
    message_event_listener._load_node = lambda db, state, node_id: question_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: []  # End of flow
    message_event_listener._select_edge_for_input = lambda node, edges, input: None  # No edge selection
    
    # Mock publishers
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = True
        
        # Mock Redis and DB operations
        with patch('app.services.redis_service.RedisService') as mock_redis, \
             patch('app.utils.conversation_state_utils.update_conversation_in_db'):
            
            mock_redis.return_value.store_conversation_state = MagicMock()
            
            # Call the rule-based logic
            await message_event_listener._continue_rule_based_logic(
                mock_db, MagicMock(), state, "1", conversation_id
            )
            
            # Verify the published event
            assert mock_publish.called
            
            # Check that message is None for question nodes
            call_args = mock_publish.call_args
            assert call_args[1]['message'] is None
            
            # Check that nodeDetails contains the formatted question
            extra_data = call_args[1]['extra']
            node_details = extra_data['nodeDetails']
            assert node_details is not None
            assert "What is your problem?" in node_details['data']['text']


@pytest.mark.asyncio
async def test_entity_field_format_handling():
    """Test that entity field mappings work with both old and new formats"""
    conversation_id = str(uuid.uuid4())
    
    # Mock DB session and models
    mock_db = MagicMock()
    
    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
    
    # Create a question node with entity fields in new format
    question_node = Node("question_node", "question", {
        "text": "What is your problem?",
        "options": [
            {"text": "problem 1", "name": "1", "position": 0}
        ],
        "entityFields": [
            {
                "entityType": "LEAD",
                "fieldId": 129274,
                "displayName": "Patient Full Name"
            },
            {
                "entityType": "CONTACT",
                "fieldId": 129366,
                "displayName": "Patient Full Name"
            }
        ]
    })
    
    # Mock state with entity details
    state = {
        "rule_current_node_id": "question_node",
        "chatbotType": "RULE",
        "tenant_id": 2048,
        "entity_details": [
            {"entityType": "LEAD", "id": "lead_123"},
            {"entityType": "CONTACT", "id": "contact_123"}
        ]
    }
    
    # Patch helper methods
    message_event_listener._load_node = lambda db, state, node_id: question_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: []  # End of flow
    message_event_listener._select_edge_for_input = lambda node, edges, input: None  # No edge selection
    
    # Mock entity field service
    with patch('app.services.entity_field_service.EntityFieldService') as mock_efs:
        mock_efs_instance = MagicMock()
        mock_efs_instance.substitute_node_variables.return_value = question_node.data
        mock_efs_instance.get_entity_field_values_from_mappings.return_value = {"1": "Akshay"}
        mock_efs.return_value = mock_efs_instance
        
        # Mock chatbot service for entity updates
        with patch('app.services.chatbot_service.ChatbotService') as mock_cs:
            mock_cs_instance = MagicMock()
            mock_cs_instance.update_entities_after_conversation = AsyncMock(return_value={"success": True})
            mock_cs.return_value = mock_cs_instance
            
            # Mock publishers
            with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
                mock_publish.return_value = True
                
                # Mock Redis and DB operations
                with patch('app.services.redis_service.RedisService') as mock_redis, \
                     patch('app.utils.conversation_state_utils.update_conversation_in_db'):
                    
                    mock_redis.return_value.store_conversation_state = MagicMock()
                    
                    # Call the rule-based logic
                    await message_event_listener._continue_rule_based_logic(
                        mock_db, MagicMock(), state, "1", conversation_id
                    )
                    
                    # Verify entity update was called
                    assert mock_cs_instance.update_entities_after_conversation.called
                    
                    # Check the entity update call arguments
                    update_call = mock_cs_instance.update_entities_after_conversation.call_args
                    entity_details_arg = update_call[0][0]
                    answers_arg = update_call[0][1]
                    
                    # Verify entity details
                    assert len(entity_details_arg) == 2
                    assert entity_details_arg[0]["entityType"] == "LEAD"
                    assert entity_details_arg[1]["entityType"] == "CONTACT"
                    
                    # Verify answers were captured with correct field mappings
                    assert len(answers_arg) == 2
                    assert answers_arg[0]["field_name"] == "Patient Full Name"
                    assert answers_arg[0]["entity_type"] == "LEAD"
                    assert answers_arg[0]["field_id"] == 129274
                    assert answers_arg[1]["field_name"] == "Patient Full Name"
                    assert answers_arg[1]["entity_type"] == "CONTACT"
                    assert answers_arg[1]["field_id"] == 129366


@pytest.mark.asyncio
async def test_entity_field_old_format_compatibility():
    """Test that entity field mappings work with old format (entity_type/name)"""
    conversation_id = str(uuid.uuid4())
    
    # Mock DB session and models
    mock_db = MagicMock()
    
    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
    
    # Create a question node with entity fields in old format
    question_node = Node("question_node", "question", {
        "text": "What is your problem?",
        "options": [
            {"text": "problem 1", "name": "1", "position": 0}
        ],
        "entityFields": [
            {
                "entity_type": "LEAD",
                "name": "Patient Full Name"
            }
        ]
    })
    
    # Mock state with entity details
    state = {
        "rule_current_node_id": "question_node",
        "chatbotType": "RULE",
        "tenant_id": 2048,
        "entity_details": [
            {"entityType": "LEAD", "id": "lead_123"}
        ]
    }
    
    # Patch helper methods
    message_event_listener._load_node = lambda db, state, node_id: question_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: []  # End of flow
    message_event_listener._select_edge_for_input = lambda node, edges, input: None  # No edge selection
    
    # Mock entity field service
    with patch('app.services.entity_field_service.EntityFieldService') as mock_efs:
        mock_efs_instance = MagicMock()
        mock_efs_instance.substitute_node_variables.return_value = question_node.data
        mock_efs_instance.get_entity_field_values_from_mappings.return_value = {"1": "Akshay"}
        mock_efs.return_value = mock_efs_instance
        
        # Mock chatbot service for entity updates
        with patch('app.services.chatbot_service.ChatbotService') as mock_cs:
            mock_cs_instance = MagicMock()
            mock_cs_instance.update_entities_after_conversation = AsyncMock(return_value={"success": True})
            mock_cs.return_value = mock_cs_instance
            
            # Mock publishers
            with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
                mock_publish.return_value = True
                
                # Mock Redis and DB operations
                with patch('app.services.redis_service.RedisService') as mock_redis, \
                     patch('app.utils.conversation_state_utils.update_conversation_in_db'):
                    
                    mock_redis.return_value.store_conversation_state = MagicMock()
                    
                    # Call the rule-based logic
                    await message_event_listener._continue_rule_based_logic(
                        mock_db, MagicMock(), state, "1", conversation_id
                    )
                    
                    # Verify entity update was called
                    assert mock_cs_instance.update_entities_after_conversation.called
                    
                    # Check the entity update call arguments
                    update_call = mock_cs_instance.update_entities_after_conversation.call_args
                    answers_arg = update_call[0][1]
                    
                    # Verify answers were captured with old format
                    assert len(answers_arg) == 1
                    assert answers_arg[0]["field_name"] == "Patient Full Name"
                    assert answers_arg[0]["entity_type"] == "LEAD"


@pytest.mark.asyncio
async def test_rule_based_last_node_completion():
    """Test that rule-based chatbots publish completed=true for the last node"""
    conversation_id = str(uuid.uuid4())
    
    # Mock DB session and models
    mock_db = MagicMock()
    
    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
    
    class Edge:
        def __init__(self, source_node, target_node):
            self.source_node = source_node
            self.target_node = target_node
    
    # Create a sendMessage node (last node with no outgoing edges)
    last_node = Node("last_node", "sendMessage", {
        "options": [
            {"type": "text", "text": "check your whatsapp"}
        ]
    })
    
    # Mock state
    state = {
        "rule_current_node_id": "current_node",  # Start from current node
        "chatbotType": "RULE",
        "tenant_id": 2048,
        "entity_details": [
            {"entityType": "CONTACT", "id": "contact_123"}
        ]
    }
    
    # Mock publishers
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_response, \
         patch.object(conversation_event_publisher, 'publish_conversation_completion') as mock_completion:
        
        mock_response.return_value = True
        mock_completion.return_value = True
        
        # Mock Redis and DB operations
        with patch('app.services.redis_service.RedisService') as mock_redis, \
             patch('app.utils.conversation_state_utils.update_conversation_in_db'), \
             patch('app.services.entity_field_service.EntityFieldService') as mock_efs:
            
            mock_redis.return_value.store_conversation_state = MagicMock()
            mock_efs_instance = MagicMock()
            mock_efs_instance.substitute_node_variables.return_value = last_node.data
            mock_efs_instance.get_entity_field_values_from_mappings.return_value = {}
            mock_efs_instance.get_entity_details.return_value = {"ownerId": 12345, "entityName": "John Doe"}
            mock_efs.return_value = mock_efs_instance
            
            # Create a current node and edge that leads to the last node
            current_node = Node("current_node", "question", {"text": "What's your choice?"})
            edge_to_last = Edge("current_node", "last_node")
            
            # Patch helper methods to simulate flow to last node
            message_event_listener._load_node = lambda db, state, node_id: last_node if node_id == "last_node" else current_node
            message_event_listener._get_outgoing_edges = lambda db, state, node_id: [edge_to_last] if node_id == "current_node" else []  # Current node has edge, last node has none
            message_event_listener._select_edge_for_input = lambda node, edges, input: edge_to_last  # Select edge to last node
            message_event_listener._build_message_for_node = lambda node, field_values: "check your whatsapp"
            message_event_listener._collect_chained_nodes = AsyncMock(return_value=[])  # No chained nodes
            
            # Call the rule-based logic
            await message_event_listener._continue_rule_based_logic(
                mock_db, MagicMock(), state, "1", conversation_id
            )
            
            # Verify response event was NOT published (since it's the last node)
            assert not mock_response.called
            
            # Verify completion event was published
            assert mock_completion.called
            completion_call = mock_completion.call_args
            assert completion_call[1]['chatbot_conversation_id'] == conversation_id
            assert completion_call[1]['chatbot_type'] == "RULE"
            assert completion_call[1]['completion_message'] is None  # For rule-based, message should be null
            assert completion_call[1]['entity_details'] == [{"entityType": "CONTACT", "id": "contact_123"}]


@pytest.mark.asyncio
async def test_rule_based_middle_node_not_completed():
    """Test that rule-based chatbots publish completed=false for middle nodes"""
    conversation_id = str(uuid.uuid4())
    
    # Mock DB session and models
    mock_db = MagicMock()
    
    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
    
    class Edge:
        def __init__(self, source_node, target_node):
            self.source_node = source_node
            self.target_node = target_node
    
    # Create a sendMessage node (middle node with outgoing edges)
    middle_node = Node("middle_node", "sendMessage", {
        "options": [
            {"type": "text", "text": "middle message"}
        ]
    })
    
    # Mock state
    state = {
        "rule_current_node_id": "middle_node",
        "chatbotType": "RULE",
        "tenant_id": 2048
    }
    
    # Mock publishers
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_response, \
         patch.object(conversation_event_publisher, 'publish_conversation_completion') as mock_completion:
        
        mock_response.return_value = True
        mock_completion.return_value = True
        
        # Mock Redis and DB operations
        with patch('app.services.redis_service.RedisService') as mock_redis, \
             patch('app.utils.conversation_state_utils.update_conversation_in_db'), \
             patch('app.services.entity_field_service.EntityFieldService') as mock_efs:
            
            mock_redis.return_value.store_conversation_state = MagicMock()
            mock_efs_instance = MagicMock()
            mock_efs_instance.substitute_node_variables.return_value = middle_node.data
            mock_efs_instance.get_entity_field_values_from_mappings.return_value = {}
            mock_efs_instance.get_entity_details.return_value = {"ownerId": 12345, "entityName": "John Doe"}
            mock_efs.return_value = mock_efs_instance
            
            # Create a next node and edge for middle node flow
            next_node = Node("next_node", "question", {"text": "Next question?"})
            final_node = Node("final_node", "sendMessage", {"options": [{"type": "text", "text": "Final message"}]})
            edge_to_next = Edge("middle_node", "next_node")
            edge_to_final = Edge("next_node", "final_node")
            
            # Patch helper methods to simulate middle node flow
            message_event_listener._load_node = lambda db, state, node_id: (
                final_node if node_id == "final_node" else 
                next_node if node_id == "next_node" else 
                middle_node
            )
            message_event_listener._get_outgoing_edges = lambda db, state, node_id: (
                [edge_to_next] if node_id == "middle_node" else
                [edge_to_final] if node_id == "next_node" else
                []  # final_node has no edges
            )
            message_event_listener._select_edge_for_input = lambda node, edges, input: edge_to_next  # Select edge to next node
            message_event_listener._build_message_for_node = lambda node, field_values: "middle message"
            message_event_listener._collect_chained_nodes = AsyncMock(return_value=[])  # No chained nodes
            
            # Call the rule-based logic
            await message_event_listener._continue_rule_based_logic(
                mock_db, MagicMock(), state, "1", conversation_id
            )
            
            # Verify response event was published with completed=false
            assert mock_response.called
            response_call = mock_response.call_args
            assert response_call[1]['completed'] is False
            assert response_call[1]['extra']['chatbotType'] == "RULE"
            
            # Verify completion event was NOT published
            assert not mock_completion.called


@pytest.mark.asyncio
async def test_rule_based_last_node_completion_with_null_message():
    """Test that rule-based chatbots can publish completion with null message"""
    conversation_id = str(uuid.uuid4())
    
    # Mock DB session and models
    mock_db = MagicMock()
    
    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
    
    class Edge:
        def __init__(self, source_node, target_node):
            self.source_node = source_node
            self.target_node = target_node
    
    # Create a sendMessage node with null/empty message
    last_node = Node("last_node", "sendMessage", {
        "options": [
            {"type": "text", "text": ""}  # Empty message
        ]
    })
    
    # Mock state
    state = {
        "rule_current_node_id": "current_node",  # Start from current node
        "chatbotType": "RULE",
        "tenant_id": 2048,
        "entity_details": []
    }
    
    # Mock publishers
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_response, \
         patch.object(conversation_event_publisher, 'publish_conversation_completion') as mock_completion:
        
        mock_response.return_value = True
        mock_completion.return_value = True
        
        # Mock Redis and DB operations
        with patch('app.services.redis_service.RedisService') as mock_redis, \
             patch('app.utils.conversation_state_utils.update_conversation_in_db'), \
             patch('app.services.entity_field_service.EntityFieldService') as mock_efs:
            
            mock_redis.return_value.store_conversation_state = MagicMock()
            mock_efs_instance = MagicMock()
            mock_efs_instance.substitute_node_variables.return_value = last_node.data
            mock_efs_instance.get_entity_field_values_from_mappings.return_value = {}
            mock_efs_instance.get_entity_details.return_value = {"ownerId": 12345, "entityName": "John Doe"}
            mock_efs.return_value = mock_efs_instance
            
            # Create a current node and edge that leads to the last node
            current_node = Node("current_node", "question", {"text": "What's your choice?"})
            edge_to_last = Edge("current_node", "last_node")
            
            # Patch helper methods to simulate flow to last node
            message_event_listener._load_node = lambda db, state, node_id: last_node if node_id == "last_node" else current_node
            message_event_listener._get_outgoing_edges = lambda db, state, node_id: [edge_to_last] if node_id == "current_node" else []  # Current node has edge, last node has none
            message_event_listener._select_edge_for_input = lambda node, edges, input: edge_to_last  # Select edge to last node
            message_event_listener._build_message_for_node = lambda node, field_values: None  # Null message
            message_event_listener._collect_chained_nodes = AsyncMock(return_value=[])  # No chained nodes
            
            # Call the rule-based logic
            await message_event_listener._continue_rule_based_logic(
                mock_db, MagicMock(), state, "1", conversation_id
            )
            
            # Verify response event was NOT published (since it's the last node)
            assert not mock_response.called
            
            # Verify completion event was published with null message
            assert mock_completion.called
            completion_call = mock_completion.call_args
            assert completion_call[1]['chatbot_conversation_id'] == conversation_id
            assert completion_call[1]['chatbot_type'] == "RULE"
            assert completion_call[1]['completion_message'] is None  # Should be null
            assert completion_call[1]['entity_details'] == []


@pytest.mark.asyncio
async def test_rule_based_completion_includes_owner_id():
    """Test that rule-based chatbot completion events include owner_id in entity_details"""
    conversation_id = str(uuid.uuid4())
    
    # Mock DB session and models
    mock_db = MagicMock()
    
    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
    
    class Edge:
        def __init__(self, source_node, target_node):
            self.source_node = source_node
            self.target_node = target_node
    
    # Create a sendMessage node (last node with no outgoing edges)
    last_node = Node("last_node", "sendMessage", {
        "options": [
            {"type": "text", "text": "check your whatsapp"}
        ]
    })
    
    # Mock state with entity_details including owner_id
    state = {
        "rule_current_node_id": "current_node",  # Start from current node
        "chatbotType": "RULE",
        "tenant_id": 2048,
        "entity_details": [
            {
                "entityId": 388842,
                "entityType": "contact",
                "ownerId": 12345  # Include owner_id
            }
        ]
    }
    
    # Mock publishers
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_response, \
         patch.object(conversation_event_publisher, 'publish_conversation_completion') as mock_completion:
        
        mock_response.return_value = True
        mock_completion.return_value = True
        
        # Mock Redis and DB operations
        with patch('app.services.redis_service.RedisService') as mock_redis, \
             patch('app.utils.conversation_state_utils.update_conversation_in_db'), \
             patch('app.services.entity_field_service.EntityFieldService') as mock_efs:
            
            mock_redis.return_value.store_conversation_state = MagicMock()
            mock_efs_instance = MagicMock()
            mock_efs_instance.substitute_node_variables.return_value = last_node.data
            mock_efs_instance.get_entity_field_values_from_mappings.return_value = {}
            mock_efs_instance.get_entity_details.return_value = {"ownerId": 12345, "entityName": "John Doe"}
            mock_efs.return_value = mock_efs_instance
            
            # Create a current node and edge that leads to the last node
            current_node = Node("current_node", "question", {"text": "What's your choice?"})
            edge_to_last = Edge("current_node", "last_node")
            
            # Patch helper methods to simulate flow to last node
            message_event_listener._load_node = lambda db, state, node_id: last_node if node_id == "last_node" else current_node
            message_event_listener._get_outgoing_edges = lambda db, state, node_id: [edge_to_last] if node_id == "current_node" else []  # Current node has edge, last node has none
            message_event_listener._select_edge_for_input = lambda node, edges, input: edge_to_last  # Select edge to last node
            message_event_listener._build_message_for_node = lambda node, field_values: "check your whatsapp"
            message_event_listener._collect_chained_nodes = AsyncMock(return_value=[])  # No chained nodes
            
            # Call the rule-based logic
            await message_event_listener._continue_rule_based_logic(
                mock_db, MagicMock(), state, "1", conversation_id
            )
            
            # Verify response event was NOT published (since it's the last node)
            assert not mock_response.called
            
            # Verify completion event was published with owner_id in entity_details
            assert mock_completion.called
            completion_call = mock_completion.call_args
            assert completion_call[1]['chatbot_conversation_id'] == conversation_id
            assert completion_call[1]['chatbot_type'] == "RULE"
            assert completion_call[1]['completion_message'] is None  # For rule-based, message should be null
            
            # Verify entity_details includes owner_id
            entity_details = completion_call[1]['entity_details']
            assert len(entity_details) == 1
            assert entity_details[0]['entityId'] == 388842
            assert entity_details[0]['entityType'] == "contact"
            assert entity_details[0]['ownerId'] == 12345  # Verify owner_id is included


@pytest.mark.asyncio
async def test_rule_based_invalid_option_re_ask_question():
    """Test that when user provides invalid option, the question is re-asked with error message."""
    conversation_id = str(uuid.uuid4())

    # Mock DB session and models
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
            self.variable_mapping = []

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Question node with valid options
    question_node = Node("q1", "question", {
        "text": "Which department would you like to book an appointment in?",
        "options": [
            {"text": "General Physician", "name": "option1"},
            {"text": "Dentist", "name": "option2"},
            {"text": "Cardiologist", "name": "option3"}
        ]
    })
    
    # Setup edges (matching valid options)
    edges = [
        Edge("q1", "option1", "n2"),
        Edge("q1", "option2", "n3"),
        Edge("q1", "option3", "n4")
    ]

    # Patch helper methods
    message_event_listener._load_node = lambda db, state, node_id: question_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: edges

    # Mock publisher
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = None

        # Test invalid option input
        await message_event_listener._continue_rule_based_logic(
            mock_db,
            MagicMock(),
            {
                "rule_current_node_id": "q1",
                "tenant_id": 123,
                "messageConversationId": 456,
                "entity_details": []
            },
            "invalid_option",  # User provides invalid input
            conversation_id
        )

        # Verify that re-ask question was called
        assert mock_publish.called
        call_args = mock_publish.call_args[1]
        
        # Check that the response contains error message and re-asks the question
        assert call_args['chatbot_conversation_id'] == conversation_id
        assert call_args['message'] is None  # Rule-based chatbots use nodeDetails
        assert call_args['completed'] is False
        assert call_args['chatbot_type'] == "RULE"
        assert 'node_details' in call_args
        
        # Verify the error message is included
        node_details = call_args['node_details'][0]
        assert "Please select a correct input" in node_details['data']['text']
        assert "Which department would you like to book an appointment in?" in node_details['data']['text']
        
        # Verify options are still present
        assert len(node_details['data']['options']) == 3
        assert any("General Physician" in opt['text'] for opt in node_details['data']['options'])


@pytest.mark.asyncio
async def test_rule_based_valid_option_proceeds_normally():
    """Test that when user provides valid option, conversation proceeds to next node."""
    conversation_id = str(uuid.uuid4())

    # Mock DB session and models
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data
            self.variable_mapping = []
            self.is_first_node = False

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Question node with valid options
    question_node = Node("q1", "question", {
        "text": "Which department?",
        "options": [
            {"text": "General Physician", "name": "option1"},
            {"text": "Dentist", "name": "option2"}
        ]
    })
    
    # Next node
    next_node = Node("n2", "sendMessage", {
        "options": [{"type": "text", "text": "Great choice!"}]
    })
    
    # Setup edges
    edges = [Edge("q1", "option1", "n2")]

    # Patch helper methods
    message_event_listener._load_node = lambda db, state, node_id: question_node if node_id == "q1" else next_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: edges if node_id == "q1" else []

    # Mock publisher
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = None

        # Test valid option input
        await message_event_listener._continue_rule_based_logic(
            mock_db,
            MagicMock(),
            {
                "rule_current_node_id": "q1",
                "tenant_id": 123,
                "messageConversationId": 456,
                "entity_details": []
            },
            "General Physician",  # User provides valid option
            conversation_id
        )

        # Verify that conversation proceeded normally (not re-asking question)
        assert mock_publish.called
        call_args = mock_publish.call_args[1]
        
        # Should proceed to next node, not re-ask current question
        node_details = call_args.get('node_details', [])
        if node_details:
            # If nodeDetails present, it should be the next node (sendMessage)
            assert node_details[0]['type'] == 'sendMessage'
            assert "Great choice!" in str(node_details[0]['data'])


@pytest.mark.asyncio
async def test_rule_based_traversal_list_node_selection():
    """Test rule-based traversal with list node selection"""
    conversation_id = str(uuid.uuid4())

    # Mock DB session and models
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Create list node
    list_node = Node("list-1", "list", {
        "header": "Choose an option",
        "body": "Please select from the list below",
        "footer": "Thank you for your selection",
        "menuButton": "View All",
        "sections": [
            {
                "title": "Main Options",
                "rows": [
                    {
                        "id": "option1",
                        "title": "First Option",
                        "description": "This is the first option"
                    },
                    {
                        "id": "option2",
                        "title": "Second Option",
                        "description": "This is the second option"
                    }
                ]
            }
        ]
    })

    # Next node
    next_node = Node("next-1", "sendMessage", {
        "options": [{"type": "text", "text": "You selected an option"}]
    })

    # Setup edges
    edges = [
        Edge("list-1", "option1", "next-1"),
        Edge("list-1", "option2", "next-1")
    ]

    # Patch helper methods
    message_event_listener._load_node = lambda db, state, node_id: list_node if node_id == "list-1" else next_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: edges if node_id == "list-1" else []

    # Mock publisher
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = None

        # Test valid list selection by ID
        await message_event_listener._continue_rule_based_logic(
            mock_db,
            MagicMock(),
            {
                "rule_current_node_id": "list-1",
                "tenant_id": 123,
                "messageConversationId": 456,
                "entity_details": []
            },
            "option1",  # User selects by row ID
            conversation_id
        )

        # Verify that conversation proceeded normally
        assert mock_publish.called
        call_args = mock_publish.call_args[1]
        
        # Should proceed to next node
        node_details = call_args.get('node_details', [])
        if node_details:
            assert node_details[0]['type'] == 'sendMessage'
            assert "You selected an option" in str(node_details[0]['data'])


@pytest.mark.asyncio
async def test_rule_based_traversal_list_node_selection_by_title():
    """Test rule-based traversal with list node selection by title"""
    conversation_id = str(uuid.uuid4())

    # Mock DB session and models
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Create list node
    list_node = Node("list-1", "list", {
        "header": "Choose an option",
        "body": "Please select from the list below",
        "footer": "Thank you for your selection",
        "menuButton": "View All",
        "sections": [
            {
                "title": "Main Options",
                "rows": [
                    {
                        "id": "option1",
                        "title": "First Option",
                        "description": "This is the first option"
                    },
                    {
                        "id": "option2",
                        "title": "Second Option",
                        "description": "This is the second option"
                    }
                ]
            }
        ]
    })

    # Next node
    next_node = Node("next-1", "sendMessage", {
        "options": [{"type": "text", "text": "You selected an option"}]
    })

    # Setup edges with title-based handles
    edges = [
        Edge("list-1", "First Option", "next-1"),
        Edge("list-1", "Second Option", "next-1")
    ]

    # Patch helper methods
    message_event_listener._load_node = lambda db, state, node_id: list_node if node_id == "list-1" else next_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: edges if node_id == "list-1" else []

    # Mock publisher
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = None

        # Test valid list selection by title
        await message_event_listener._continue_rule_based_logic(
            mock_db,
            MagicMock(),
            {
                "rule_current_node_id": "list-1",
                "tenant_id": 123,
                "messageConversationId": 456,
                "entity_details": []
            },
            "First Option",  # User selects by row title
            conversation_id
        )

        # Verify that conversation proceeded normally
        assert mock_publish.called
        call_args = mock_publish.call_args[1]
        
        # Should proceed to next node
        node_details = call_args.get('node_details', [])
        if node_details:
            assert node_details[0]['type'] == 'sendMessage'
            assert "You selected an option" in str(node_details[0]['data'])


@pytest.mark.asyncio
async def test_rule_based_traversal_list_node_numeric_selection():
    """Test rule-based traversal with list node numeric selection"""
    conversation_id = str(uuid.uuid4())

    # Mock DB session and models
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Create list node
    list_node = Node("list-1", "list", {
        "header": "Choose an option",
        "body": "Please select from the list below",
        "footer": "Thank you for your selection",
        "menuButton": "View All",
        "sections": [
            {
                "title": "Main Options",
                "rows": [
                    {
                        "id": "option1",
                        "title": "First Option",
                        "description": "This is the first option"
                    },
                    {
                        "id": "option2",
                        "title": "Second Option",
                        "description": "This is the second option"
                    }
                ]
            }
        ]
    })

    # Next node
    next_node = Node("next-1", "sendMessage", {
        "options": [{"type": "text", "text": "You selected an option"}]
    })

    # Setup edges with numeric handles
    edges = [
        Edge("list-1", "list-0", "next-1"),
        Edge("list-1", "list-1", "next-1")
    ]

    # Patch helper methods
    message_event_listener._load_node = lambda db, state, node_id: list_node if node_id == "list-1" else next_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: edges if node_id == "list-1" else []

    # Mock publisher
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = None

        # Test valid list selection by numeric input
        await message_event_listener._continue_rule_based_logic(
            mock_db,
            MagicMock(),
            {
                "rule_current_node_id": "list-1",
                "tenant_id": 123,
                "messageConversationId": 456,
                "entity_details": []
            },
            "1",  # User selects by numeric input
            conversation_id
        )

        # Verify that conversation proceeded normally
        assert mock_publish.called
        call_args = mock_publish.call_args[1]
        
        # Should proceed to next node
        node_details = call_args.get('node_details', [])
        if node_details:
            assert node_details[0]['type'] == 'sendMessage'
            assert "You selected an option" in str(node_details[0]['data'])


@pytest.mark.asyncio
async def test_rule_based_traversal_list_node_invalid_selection():
    """Test rule-based traversal with list node invalid selection"""
    conversation_id = str(uuid.uuid4())

    # Mock DB session and models
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, type, data):
            self.node_id = node_id
            self.type = type
            self.data = data

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Create list node
    list_node = Node("list-1", "list", {
        "header": "Choose an option",
        "body": "Please select from the list below",
        "footer": "Thank you for your selection",
        "menuButton": "View All",
        "sections": [
            {
                "title": "Main Options",
                "rows": [
                    {
                        "id": "option1",
                        "title": "First Option",
                        "description": "This is the first option"
                    }
                ]
            }
        ]
    })

    # Setup edges
    edges = [Edge("list-1", "option1", "next-1")]

    # Patch helper methods
    message_event_listener._load_node = lambda db, state, node_id: list_node
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: edges if node_id == "list-1" else []

    # Mock publisher
    with patch.object(conversation_event_publisher, 'publish_conversation_response') as mock_publish:
        mock_publish.return_value = None

        # Test invalid list selection
        await message_event_listener._continue_rule_based_logic(
            mock_db,
            MagicMock(),
            {
                "rule_current_node_id": "list-1",
                "tenant_id": 123,
                "messageConversationId": 456,
                "entity_details": []
            },
            "invalid_option",  # User provides invalid selection
            conversation_id
        )

        # Verify that conversation re-asked the question
        assert mock_publish.called
        call_args = mock_publish.call_args[1]
        
        # Should re-ask the list question
        node_details = call_args.get('node_details', [])
        if node_details:
            assert node_details[0]['type'] == 'list'
            assert "Choose an option" in str(node_details[0]['data']['header'])



