#!/usr/bin/env python3
"""
Test script for variable mapping approach - fetching only specific fields based on node mappings
"""

import json
from unittest.mock import Mock, patch
from app.services.entity_field_service import EntityFieldService

def test_variable_mapping_approach():
    """Test fetching field values based on variable mappings"""
    
    # Sample entity details from conversation
    entity_details = [
        {"id": "31301725", "entityType": "lead"},
        {"id": "3512242", "entityType": "contact"}
    ]
    
    # Sample variable mappings from a node
    variable_mappings = [
        {
            "entity": "lead",
            "internalName": "firstName",
            "variable": "1",
            "componentType": "body",
            "fieldType": "TEXT_FIELD"
        },
        {
            "entity": "lead", 
            "internalName": "fullName",
            "variable": "userName",
            "componentType": "body",
            "fieldType": "TEXT_FIELD"
        },
        {
            "entity": "contact",
            "internalName": "email",
            "variable": "userEmail", 
            "componentType": "body",
            "fieldType": "EMAIL_FIELD"
        },
        {
            "entity": "lead",
            "internalName": "pipeline",
            "variable": "leadPipeline",
            "componentType": "body", 
            "fieldType": "TEXT_FIELD"
        }
    ]
    
    # Mock API responses
    lead_response = {
        "id": 31301725,
        "firstName": "Akshay",
        "lastName": "Lasy Name",
        "pipeline": {
            "name": "Default Lead Pipeline"
        },
        "metaData": {
            "idNameStore": {
                "ownerId": {
                    "594": "Akshay Gunshetti"
                }
            }
        }
    }
    
    contact_response = {
        "id": 3512242,
        "firstName": "John",
        "lastName": "Doe", 
        "email": "<EMAIL>",
        "phoneNumbers": [
            {
                "id": 39641052,
                "type": "MOBILE",
                "value": "5551234567",
                "primary": True
            }
        ]
    }
    
    # Initialize service
    entity_field_service = EntityFieldService()
    
    # Mock the get_entity_field_value method to return specific values
    def mock_get_entity_field_value(entity_id, entity_type, field_name, token):
        if entity_type == "lead" and entity_id == "31301725":
            if field_name == "firstName":
                return "Akshay"
            elif field_name == "fullName":
                return "Akshay Lasy Name"
            elif field_name == "pipeline":
                return "Default Lead Pipeline"
        elif entity_type == "contact" and entity_id == "3512242":
            if field_name == "email":
                return "<EMAIL>"
        return None
    
    # Patch the method
    with patch.object(entity_field_service, 'get_entity_field_value', side_effect=mock_get_entity_field_value):
        # Test the new approach
        field_values = entity_field_service.get_entity_field_values_from_mappings(
            entity_details, variable_mappings, "test_token"
        )
    
    print("🧪 Testing Variable Mapping Approach")
    print("=" * 50)
    print(f"Entity Details: {json.dumps(entity_details, indent=2)}")
    print(f"Variable Mappings: {json.dumps(variable_mappings, indent=2)}")
    print(f"Retrieved Field Values: {json.dumps(field_values, indent=2)}")
    
    # Expected results
    expected_values = {
        "1": "Akshay",  # lead.firstName -> 1
        "userName": "Akshay Lasy Name",  # lead.fullName -> userName
        "userEmail": "<EMAIL>",  # contact.email -> userEmail
        "leadPipeline": "Default Lead Pipeline"  # lead.pipeline -> leadPipeline
    }
    
    print(f"\nExpected Values: {json.dumps(expected_values, indent=2)}")
    
    # Verify results
    all_correct = True
    for expected_key, expected_value in expected_values.items():
        actual_value = field_values.get(expected_key)
        status = "✅ PASS" if actual_value == expected_value else "❌ FAIL"
        print(f"  {expected_key}: {actual_value} (expected: {expected_value}) {status}")
        if actual_value != expected_value:
            all_correct = False
    
    # Check that we didn't fetch unnecessary fields
    unexpected_fields = set(field_values.keys()) - set(expected_values.keys())
    if unexpected_fields:
        print(f"\n⚠️  Unexpected fields fetched: {unexpected_fields}")
        all_correct = False
    
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if all_correct else '❌ SOME TESTS FAILED'}")
    return all_correct

def test_variable_substitution_with_mappings():
    """Test variable substitution using the mapping approach"""
    
    entity_field_service = EntityFieldService()
    
    # Field values from mapping approach
    field_values = {
        "1": "Akshay",
        "userName": "Akshay Lasy Name", 
        "userEmail": "<EMAIL>",
        "leadPipeline": "Default Lead Pipeline"
    }
    
    # Test text with variables
    test_text = "Hello {{1}}, your {{leadPipeline}} lead is ready. Contact: {{userEmail}}"
    expected_text = "Hello Akshay, your Default Lead Pipeline lead is ready. Contact: <EMAIL>"
    
    result_text = entity_field_service.substitute_variables(test_text, field_values)
    
    print("\n🧪 Testing Variable Substitution with Mappings")
    print("=" * 50)
    print(f"Input: {test_text}")
    print(f"Expected: {expected_text}")
    print(f"Result: {result_text}")
    print(f"Status: {'✅ PASS' if result_text == expected_text else '❌ FAIL'}")
    
    return result_text == expected_text

def test_node_data_substitution_with_mappings():
    """Test node data substitution using the mapping approach"""
    
    entity_field_service = EntityFieldService()
    
    # Field values from mapping approach
    field_values = {
        "1": "Akshay",
        "userName": "Akshay Lasy Name",
        "leadPipeline": "Default Lead Pipeline"
    }
    
    # Test node data
    test_node_data = {
        "text": "Welcome {{1}}!",
        "options": [
            {
                "type": "text",
                "text": "Your {{leadPipeline}} lead is active"
            },
            {
                "type": "media",
                "mediaFile": {
                    "fileId": 14,
                    "fileName": "guide.pdf",
                    "fileCaption": "Guide for {{userName}} - {{leadPipeline}}"
                }
            }
        ]
    }
    
    expected_node_data = {
        "text": "Welcome Akshay!",
        "options": [
            {
                "type": "text", 
                "text": "Your Default Lead Pipeline lead is active"
            },
            {
                "type": "media",
                "mediaFile": {
                    "fileId": 14,
                    "fileName": "guide.pdf",
                    "fileCaption": "Guide for Akshay Lasy Name - Default Lead Pipeline"
                }
            }
        ]
    }
    
    result_node_data = entity_field_service.substitute_node_variables(test_node_data, field_values)
    
    print("\n🧪 Testing Node Data Substitution with Mappings")
    print("=" * 50)
    print(f"Input: {json.dumps(test_node_data, indent=2)}")
    print(f"Expected: {json.dumps(expected_node_data, indent=2)}")
    print(f"Result: {json.dumps(result_node_data, indent=2)}")
    print(f"Status: {'✅ PASS' if result_node_data == expected_node_data else '❌ FAIL'}")
    
    return result_node_data == expected_node_data

if __name__ == "__main__":
    print("🚀 Testing Variable Mapping Approach")
    print("=" * 60)
    
    # Run all tests
    test1 = test_variable_mapping_approach()
    test2 = test_variable_substitution_with_mappings()
    test3 = test_node_data_substitution_with_mappings()
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Variable Mapping Approach: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"Variable Substitution: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"Node Data Substitution: {'✅ PASS' if test3 else '❌ FAIL'}")
    
    all_tests_passed = all([test1, test2, test3])
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if all_tests_passed else '❌ SOME TESTS FAILED'}")
    
    if all_tests_passed:
        print("\n🎉 Variable Mapping Approach is working correctly!")
        print("✅ Only fetches fields specified in variable mappings")
        print("✅ Maps fields to correct variable names")
        print("✅ Supports variable substitution in text and media captions")
    else:
        print("\n⚠️  Please fix failing tests before deployment.")
