"""
Test cases for conversation completion listener
Tests that conversation completion messages are handled correctly
"""

import pytest
from unittest.mock import Mo<PERSON>, patch, AsyncMock, MagicMock
import json


class TestConversationCompletionListener:
    """Test suite for conversation completion listener"""
    
    @pytest.mark.asyncio
    async def test_handle_completion_event_success(self):
        """Test successful handling of completion event"""
        print("\n🧪 Testing successful completion event handling...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        from app.models import ChatbotConversation
        
        listener = ConversationCompletionListener()
        
        # Mock message
        mock_message = AsyncMock()
        mock_message.body = json.dumps({
            "messageConversationId": 101,
            "chatbotConversationId": "abc-123"
        }).encode('utf-8')
        mock_message.routing_key = "message.chatbot.conversation.completed"
        mock_message.exchange = "ex.message"
        
        # Mock conversation
        mock_conversation = Mock(spec=ChatbotConversation)
        mock_conversation.id = "abc-123"
        mock_conversation.completed = False
        
        with patch('app.services.conversation_completion_listener.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_conversation
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch('app.services.conversation_completion_listener.RedisService') as mock_redis:
                mock_redis_instance = Mock()
                mock_redis_instance.get_conversation_state.return_value = {
                    "conversation_id": "abc-123",
                    "chatbot_id": "chatbot-1",
                    "chatbot_name": "Test Chatbot",
                    "tenant_id": 2048,
                    "chatbotType": "RULE"
                }
                mock_redis.return_value = mock_redis_instance
                
                with patch('app.services.conversation_completion_listener.update_conversation_in_db') as mock_update_db:
                    with patch('app.services.conversation_completion_listener.conversation_event_publisher.publish_conversation_response', new_callable=AsyncMock) as mock_publish:
                        
                        # Handle the event
                        payload = {
                            "messageConversationId": 101,
                            "chatbotConversationId": "abc-123"
                        }
                        await listener.handle_completion_event(payload, mock_message)
                        
                        # Verify message was acknowledged
                        mock_message.ack.assert_called_once()
                        
                        # Verify state was updated
                        mock_redis_instance.store_conversation_state.assert_called_once()
                        
                        # Verify database was updated
                        mock_update_db.assert_called_once()
                        
                        # Verify completion event was published
                        mock_publish.assert_called_once()
                        
                        print("✅ Test passed: Completion event handled successfully")
    
    @pytest.mark.asyncio
    async def test_handle_completion_event_missing_conversation_id(self):
        """Test handling of completion event with missing chatbotConversationId"""
        print("\n🧪 Testing completion event with missing conversation ID...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        
        listener = ConversationCompletionListener()
        
        # Mock message without chatbotConversationId
        payload = {
            "messageConversationId": 101
            # Missing chatbotConversationId
        }
        mock_message = AsyncMock()
        mock_message.body = json.dumps(payload).encode('utf-8')
        
        # Handle the event
        await listener.handle_completion_event(payload, mock_message)
        
        # Should still acknowledge the message
        mock_message.ack.assert_called_once()
        
        print("✅ Test passed: Missing conversation ID handled gracefully")
    
    @pytest.mark.asyncio
    async def test_handle_completion_event_conversation_not_found(self):
        """Test handling when conversation doesn't exist in database"""
        print("\n🧪 Testing completion event for non-existent conversation...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        
        listener = ConversationCompletionListener()
        
        # Mock message
        payload = {
            "messageConversationId": 101,
            "chatbotConversationId": "non-existent-id"
        }
        mock_message = AsyncMock()
        mock_message.body = json.dumps(payload).encode('utf-8')
        mock_message.routing_key = "message.chatbot.conversation.completed"
        mock_message.exchange = "ex.message"
        
        with patch('app.services.conversation_completion_listener.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = None  # Conversation not found
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            # Handle the event
            await listener.handle_completion_event(payload, mock_message)
            
            # Should still acknowledge the message
            mock_message.ack.assert_called_once()
            
            print("✅ Test passed: Non-existent conversation handled gracefully")
    
    @pytest.mark.asyncio
    async def test_process_completion_updates_state(self):
        """Test that completion updates both Redis state and database"""
        print("\n🧪 Testing completion updates state...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        from app.models import ChatbotConversation
        
        listener = ConversationCompletionListener()
        
        conversation_id = "abc-123"
        message_conversation_id = 101
        
        # Mock conversation
        mock_conversation = Mock(spec=ChatbotConversation)
        mock_conversation.id = conversation_id
        mock_conversation.completed = False
        
        with patch('app.services.conversation_completion_listener.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_conversation
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch('app.services.conversation_completion_listener.RedisService') as mock_redis:
                mock_redis_instance = Mock()
                mock_redis_instance.get_conversation_state.return_value = {
                    "conversation_id": conversation_id,
                    "completed": False,
                    "ended": False,
                    "chatbot_id": "chatbot-1",
                    "tenant_id": 2048
                }
                mock_redis.return_value = mock_redis_instance
                
                with patch('app.services.conversation_completion_listener.update_conversation_in_db') as mock_update_db:
                    with patch('app.services.conversation_completion_listener.conversation_event_publisher.publish_conversation_response', new_callable=AsyncMock):
                        
                        # Process completion
                        await listener._process_completion(conversation_id, message_conversation_id)
                        
                        # Verify Redis state was updated
                        call_args = mock_redis_instance.store_conversation_state.call_args
                        stored_state = call_args[0][1]
                        
                        assert stored_state["completed"] == True
                        assert stored_state["ended"] == True
                        
                        # Verify database update was called with completed=True
                        mock_update_db.assert_called_once()
                        update_call_kwargs = mock_update_db.call_args.kwargs
                        assert update_call_kwargs["completed"] == True
                        assert update_call_kwargs["ended"] == True
                        
                        print("✅ Test passed: State updated with completed=True and ended=True")
    
    @pytest.mark.asyncio
    async def test_process_completion_without_redis_state(self):
        """Test completion when Redis state doesn't exist"""
        print("\n🧪 Testing completion without Redis state...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        from app.models import ChatbotConversation
        
        listener = ConversationCompletionListener()
        
        conversation_id = "abc-123"
        
        # Mock conversation
        mock_conversation = Mock(spec=ChatbotConversation)
        mock_conversation.id = conversation_id
        mock_conversation.completed = False
        
        with patch('app.services.conversation_completion_listener.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_conversation
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch('app.services.conversation_completion_listener.RedisService') as mock_redis:
                mock_redis_instance = Mock()
                mock_redis_instance.get_conversation_state.return_value = None  # No state in Redis
                mock_redis.return_value = mock_redis_instance
                
                with patch('app.services.conversation_completion_listener.update_conversation_in_db') as mock_update_db:
                    with patch('app.services.conversation_completion_listener.conversation_event_publisher.publish_conversation_response', new_callable=AsyncMock):
                        
                        # Process completion
                        await listener._process_completion(conversation_id)
                        
                        # Should create minimal state
                        call_args = mock_redis_instance.store_conversation_state.call_args
                        stored_state = call_args[0][1]
                        
                        assert stored_state["conversation_id"] == conversation_id
                        assert stored_state["completed"] == True
                        assert stored_state["ended"] == True
                        
                        print("✅ Test passed: Minimal state created when Redis state missing")
    
    @pytest.mark.asyncio
    async def test_publish_completion_event_includes_required_fields(self):
        """Test that completion event includes all required fields"""
        print("\n🧪 Testing completion event payload...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        
        listener = ConversationCompletionListener()
        
        conversation_id = "abc-123"
        state = {
            "conversation_id": conversation_id,
            "chatbot_id": "chatbot-1",
            "chatbot_name": "Test Chatbot",
            "tenant_id": 2048,
            "chatbotType": "RULE",
            "message_conversation_id": 101,
            "entity_details": [
                {"id": 100, "entity": "lead", "name": "John"}
            ],
            "completed": True,
            "ended": True
        }
        
        with patch('app.services.conversation_completion_listener.conversation_event_publisher.publish_conversation_response', new_callable=AsyncMock) as mock_publish:
            
            # Publish completion event
            await listener._publish_completion_event(conversation_id, state)
            
            # Verify publish was called
            mock_publish.assert_called_once()
            
            # Check the call arguments
            call_args = mock_publish.call_args
            
            assert call_args.kwargs["chatbot_conversation_id"] == conversation_id
            assert call_args.kwargs["completed"] == True
            assert call_args.kwargs["message"] == "Conversation completed"
            assert call_args.kwargs["charge"] == 0
            assert call_args.kwargs["tenant_id"] == 2048
            
            # Check extra data
            extra = call_args.kwargs["extra"]
            assert extra["chatbotType"] == "RULE"
            assert extra["ended"] == True
            assert extra["completionReason"] == "external_completion_request"
            
            print("✅ Test passed: Completion event includes all required fields")
    
    @pytest.mark.asyncio
    async def test_listener_start_and_stop(self):
        """Test listener start and stop methods"""
        print("\n🧪 Testing listener start and stop...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        
        listener = ConversationCompletionListener()
        
        with patch('app.services.conversation_completion_listener.rabbitmq_service.connect', new_callable=AsyncMock, return_value=True):
            with patch('app.services.conversation_completion_listener.rabbitmq_service.setup_conversation_completion_listener', new_callable=AsyncMock):
                with patch('app.services.conversation_completion_listener.rabbitmq_service.setup_whatsapp_chatbot_publisher', new_callable=AsyncMock):
                    with patch('app.services.conversation_completion_listener.rabbitmq_service.register_event_handler'):
                        with patch('app.services.conversation_completion_listener.rabbitmq_service.start_consuming', new_callable=AsyncMock) as mock_consume:
                            
                            # Start listener
                            await listener.start()
                            
                            assert listener.is_running == True
                            
                            # Verify start_consuming was called
                            mock_consume.assert_called_once_with("q.message.chatbot.conversation.completed")
                            
                            # Stop listener
                            await listener.stop()
                            
                            assert listener.is_running == False
                            
                            print("✅ Test passed: Listener start, consuming, and stop work correctly")
    
    @pytest.mark.asyncio
    async def test_completion_event_for_ai_chatbot(self):
        """Test completion event for AI-based chatbot"""
        print("\n🧪 Testing completion for AI chatbot...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        from app.models import ChatbotConversation
        
        listener = ConversationCompletionListener()
        
        conversation_id = "ai-conv-123"
        
        # Mock AI conversation
        mock_conversation = Mock(spec=ChatbotConversation)
        mock_conversation.id = conversation_id
        mock_conversation.completed = False
        
        with patch('app.services.conversation_completion_listener.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_conversation
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch('app.services.conversation_completion_listener.RedisService') as mock_redis:
                mock_redis_instance = Mock()
                mock_redis_instance.get_conversation_state.return_value = {
                    "conversation_id": conversation_id,
                    "chatbot_id": "ai-chatbot-1",
                    "chatbotType": "AI",  # AI chatbot
                    "tenant_id": 2048
                }
                mock_redis.return_value = mock_redis_instance
                
                with patch('app.services.conversation_completion_listener.update_conversation_in_db'):
                    with patch('app.services.conversation_completion_listener.conversation_event_publisher.publish_conversation_response', new_callable=AsyncMock) as mock_publish:
                        
                        # Process completion
                        await listener._process_completion(conversation_id)
                        
                        # Verify event published
                        mock_publish.assert_called_once()
                        
                        # Verify chatbotType is AI
                        call_args = mock_publish.call_args
                        assert call_args.kwargs["extra"]["chatbotType"] == "AI"
                        
                        print("✅ Test passed: AI chatbot completion works correctly")
    
    @pytest.mark.asyncio
    async def test_completion_event_for_rule_chatbot(self):
        """Test completion event for RULE-based chatbot"""
        print("\n🧪 Testing completion for RULE chatbot...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        from app.models import ChatbotConversation
        
        listener = ConversationCompletionListener()
        
        conversation_id = "rule-conv-123"
        
        # Mock RULE conversation
        mock_conversation = Mock(spec=ChatbotConversation)
        mock_conversation.id = conversation_id
        mock_conversation.completed = False
        
        with patch('app.services.conversation_completion_listener.get_db') as mock_get_db:
            mock_db = Mock()
            mock_query = Mock()
            mock_filter = Mock()
            mock_filter.first.return_value = mock_conversation
            mock_query.filter.return_value = mock_filter
            mock_db.query.return_value = mock_query
            mock_get_db.return_value = iter([mock_db])
            
            with patch('app.services.conversation_completion_listener.RedisService') as mock_redis:
                mock_redis_instance = Mock()
                mock_redis_instance.get_conversation_state.return_value = {
                    "conversation_id": conversation_id,
                    "chatbot_id": "rule-chatbot-1",
                    "chatbotType": "RULE",  # RULE chatbot
                    "tenant_id": 2048
                }
                mock_redis.return_value = mock_redis_instance
                
                with patch('app.services.conversation_completion_listener.update_conversation_in_db'):
                    with patch('app.services.conversation_completion_listener.conversation_event_publisher.publish_conversation_response', new_callable=AsyncMock) as mock_publish:
                        
                        # Process completion
                        await listener._process_completion(conversation_id)
                        
                        # Verify event published
                        mock_publish.assert_called_once()
                        
                        # Verify chatbotType is RULE
                        call_args = mock_publish.call_args
                        assert call_args.kwargs["extra"]["chatbotType"] == "RULE"
                        
                        print("✅ Test passed: RULE chatbot completion works correctly")
    
    @pytest.mark.asyncio
    async def test_completion_event_includes_entity_details(self):
        """Test that completion event includes entity details if available"""
        print("\n🧪 Testing completion event with entity details...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        
        listener = ConversationCompletionListener()
        
        conversation_id = "abc-123"
        entity_details = [
            {"id": 100, "entity": "lead", "name": "John Doe"},
            {"id": 200, "entity": "contact", "name": "Jane Smith"}
        ]
        
        state = {
            "conversation_id": conversation_id,
            "chatbot_id": "chatbot-1",
            "tenant_id": 2048,
            "chatbotType": "RULE",
            "entity_details": entity_details  # Has entity details
        }
        
        with patch('app.services.conversation_completion_listener.conversation_event_publisher.publish_conversation_response', new_callable=AsyncMock) as mock_publish:
            
            # Publish completion event
            await listener._publish_completion_event(conversation_id, state)
            
            # Verify entity details are included in extra data (implicitly through state)
            mock_publish.assert_called_once()
            
            print("✅ Test passed: Entity details handled correctly")
    
    @pytest.mark.asyncio
    async def test_completion_event_error_handling(self):
        """Test that errors during completion are handled gracefully"""
        print("\n🧪 Testing completion error handling...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        
        listener = ConversationCompletionListener()
        
        # Mock message
        payload = {
            "messageConversationId": 101,
            "chatbotConversationId": "abc-123"
        }
        mock_message = AsyncMock()
        mock_message.body = json.dumps(payload).encode('utf-8')
        mock_message.routing_key = "message.chatbot.conversation.completed"
        mock_message.exchange = "ex.message"
        
        with patch('app.services.conversation_completion_listener.get_db') as mock_get_db:
            # Simulate database error
            mock_get_db.side_effect = Exception("Database connection failed")
            
            # Handle the event (should not raise exception)
            await listener.handle_completion_event(payload, mock_message)
            
            # Should still acknowledge the message
            mock_message.ack.assert_called_once()
            
            print("✅ Test passed: Errors handled gracefully, message acknowledged")
    
    @pytest.mark.asyncio
    async def test_completion_event_includes_entity_details_in_payload(self):
        """Test that entityDetails are explicitly included in the published event payload"""
        print("\n🧪 Testing entityDetails in completion event payload...")
        
        from app.services.conversation_completion_listener import ConversationCompletionListener
        
        listener = ConversationCompletionListener()
        
        conversation_id = "abc-123"
        entity_details = [
            {"id": 100, "entity": "lead", "name": "John Doe"},
            {"id": 200, "entity": "contact", "name": "Jane Smith"}
        ]
        
        state = {
            "conversation_id": conversation_id,
            "chatbot_id": "chatbot-1",
            "chatbot_name": "Test Chatbot",
            "tenant_id": 2048,
            "chatbotType": "RULE",
            "message_conversation_id": 101,
            "entity_details": entity_details
        }
        
        with patch('app.services.conversation_completion_listener.conversation_event_publisher.publish_conversation_response', new_callable=AsyncMock) as mock_publish:
            
            # Publish completion event
            await listener._publish_completion_event(conversation_id, state)
            
            # Verify publish was called
            mock_publish.assert_called_once()
            
            # Verify entityDetails were passed explicitly
            call_kwargs = mock_publish.call_args.kwargs
            assert "entity_details" in call_kwargs
            assert call_kwargs["entity_details"] == entity_details
            assert len(call_kwargs["entity_details"]) == 2
            assert call_kwargs["entity_details"][0]["id"] == 100
            assert call_kwargs["entity_details"][1]["id"] == 200
            
            print("✅ Test passed: entityDetails explicitly included in event payload")


if __name__ == "__main__":
    # Run tests
    import asyncio
    
    async def run_tests():
        test_instance = TestConversationCompletionListener()
        
        await test_instance.test_handle_completion_event_success()
        await test_instance.test_handle_completion_event_missing_conversation_id()
        await test_instance.test_handle_completion_event_conversation_not_found()
        await test_instance.test_process_completion_updates_state()
        await test_instance.test_completion_event_for_ai_chatbot()
        await test_instance.test_completion_event_for_rule_chatbot()
        await test_instance.test_completion_event_includes_entity_details()
        await test_instance.test_completion_event_error_handling()
        await test_instance.test_listener_start_and_stop()
        await test_instance.test_completion_event_includes_entity_details_in_payload()
        
        print("\n" + "=" * 70)
        print("✅ All conversation completion listener tests passed!")
        print("=" * 70)
    
    asyncio.run(run_tests())

