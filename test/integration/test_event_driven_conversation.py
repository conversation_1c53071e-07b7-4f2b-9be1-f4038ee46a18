"""
Test Event-Driven Conversation Flow

This test suite validates the new event-driven conversation system including:
- Conversation event publisher
- Message event listener
- Charge calculation
- Event infrastructure
"""

import pytest
import asyncio
import json
import uuid
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.orm import Session

from app.services.conversation_event_publisher import conversation_event_publisher
from app.services.message_event_listener import message_event_listener
from app.services.charge_calculator import charge_calculator
from app.services.rabbitmq_service import rabbitmq_service


class TestConversationEventPublisher:
    """Test the conversation event publisher"""
    
    @pytest.mark.asyncio
    async def test_publish_conversation_response(self):
        """Test publishing a conversation response event"""
        
        # Mock RabbitMQ service
        with patch.object(rabbitmq_service, 'connection') as mock_connection, \
             patch.object(rabbitmq_service, 'channel') as mock_channel, \
             patch.object(rabbitmq_service, 'publish_message') as mock_publish:
            
            mock_connection.is_closed = False
            mock_channel.is_closed = False
            mock_publish.return_value = None
            
            # Test data
            conversation_id = str(uuid.uuid4())
            message = "What is your name?"
            completed = False
            charge = 1
            
            # Publish event
            result = await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id=conversation_id,
                message=message,
                completed=completed,
                charge=charge
            )
            
            # Verify
            assert result is True
            mock_publish.assert_called_once()
            
            # Check the published message
            call_args = mock_publish.call_args
            assert call_args[1]['exchange'] == 'ex.whatsappChatbot'
            assert call_args[1]['routing_key'] == 'chatbot.conversation.response'
            
            # Parse the message body
            message_body = json.loads(call_args[1]['message'])
            assert message_body['chatbotConversationId'] == conversation_id
            assert message_body['message'] == message
            assert message_body['completed'] == completed
            assert message_body['charge'] == charge
    
    @pytest.mark.asyncio
    async def test_publish_first_question_event(self):
        """Test publishing first question as separate event"""

        with patch.object(rabbitmq_service, 'connection') as mock_connection, \
             patch.object(rabbitmq_service, 'channel') as mock_channel, \
             patch.object(rabbitmq_service, 'publish_message') as mock_publish:

            mock_connection.is_closed = False
            mock_channel.is_closed = False
            mock_publish.return_value = None

            # Test data
            conversation_id = str(uuid.uuid4())
            first_question = "What is your name?"
            charge = 1

            # Publish event
            result = await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id=conversation_id,
                message=first_question,
                completed=False,
                charge=charge
            )

            # Verify
            assert result is True
            mock_publish.assert_called_once()

            # Check the published message
            call_args = mock_publish.call_args
            message_body = json.loads(call_args[1]['message'])
            assert message_body['message'] == first_question
            assert message_body['completed'] is False
            assert message_body['charge'] == charge
    
    @pytest.mark.asyncio
    async def test_publish_conversation_completion(self):
        """Test publishing a conversation completion event"""
        
        with patch.object(rabbitmq_service, 'connection') as mock_connection, \
             patch.object(rabbitmq_service, 'channel') as mock_channel, \
             patch.object(rabbitmq_service, 'publish_message') as mock_publish:
            
            mock_connection.is_closed = False
            mock_channel.is_closed = False
            mock_publish.return_value = None
            
            # Test data
            conversation_id = str(uuid.uuid4())
            completion_message = "Thank you for your responses!"
            charge = 0
            
            # Publish event
            result = await conversation_event_publisher.publish_conversation_completion(
                chatbot_conversation_id=conversation_id,
                completion_message=completion_message,
                charge=charge
            )
            
            # Verify
            assert result is True
            mock_publish.assert_called_once()
            
            # Check the published message
            call_args = mock_publish.call_args
            message_body = json.loads(call_args[1]['message'])
            assert message_body['message'] == completion_message
            assert message_body['completed'] is True
            assert message_body['charge'] == charge


class TestChargeCalculator:
    """Test the charge calculation logic"""
    
    def test_calculate_question_charge_predefined(self):
        """Test charge calculation for predefined questions"""
        
        question = {"question": "What is your name?", "is_predefined": True}
        
        charge = charge_calculator.calculate_question_charge(
            question, is_predefined=True, is_llm_generated=False
        )
        
        assert charge == 1  # Predefined question charge
    
    def test_calculate_question_charge_custom(self):
        """Test charge calculation for custom/LLM generated questions"""
        
        question = {"question": "Tell me more about your preferences", "is_llm_generated": True}
        
        charge = charge_calculator.calculate_question_charge(
            question, is_predefined=False, is_llm_generated=True
        )
        
        assert charge == 2  # Custom question charge
    
    def test_calculate_conversation_charge(self):
        """Test charge calculation for conversation state"""
        
        conversation_state = {
            "questions": [
                {"question": "What is your name?", "is_predefined": True},
                {"question": "Custom question", "is_llm_generated": True}
            ],
            "original_questions": [
                {"question": "What is your name?"}
            ]
        }
        
        # Test predefined question
        charge1 = charge_calculator.calculate_conversation_charge(conversation_state, 0)
        assert charge1 == 1
        
        # Test custom question
        charge2 = charge_calculator.calculate_conversation_charge(conversation_state, 1)
        assert charge2 == 2
        
        # Test completion (beyond questions)
        charge3 = charge_calculator.calculate_conversation_charge(conversation_state, 2)
        assert charge3 == 0
    
    def test_get_charge_summary(self):
        """Test getting charge summary for entire conversation"""
        
        conversation_state = {
            "questions": [
                {"question": "What is your name?", "is_predefined": True},
                {"question": "What is your email?", "is_predefined": True},
                {"question": "Custom question", "is_llm_generated": True}
            ],
            "original_questions": [
                {"question": "What is your name?"},
                {"question": "What is your email?"}
            ]
        }
        
        summary = charge_calculator.get_charge_summary(conversation_state)
        
        assert summary["total_charge"] == 4  # 2 predefined (1 each) + 1 custom (2)
        assert summary["predefined_questions"] == 2
        assert summary["custom_questions"] == 1
        assert summary["predefined_charge"] == 2
        assert summary["custom_charge"] == 2
        assert summary["completion_charge"] == 0


class TestMessageEventListener:
    """Test the message event listener"""
    
    @pytest.mark.asyncio
    async def test_handle_user_message_event(self):
        """Test handling user message events"""
        
        # Mock dependencies
        mock_message = MagicMock()
        mock_message.routing_key = "message.chatbot.user.response"
        
        payload = {
            "message": "John Doe",
            "chatbotConversationId": str(uuid.uuid4()),
            "completed": False
        }
        
        with patch.object(message_event_listener, '_process_conversation_message') as mock_process:
            mock_process.return_value = None
            
            # Handle the event
            await message_event_listener.handle_user_message_event(payload, mock_message)
            
            # Verify processing was called
            mock_process.assert_called_once_with(
                payload["chatbotConversationId"], 
                payload["message"]
            )
    
    @pytest.mark.asyncio
    async def test_handle_new_conversation_message(self):
        """Test handling message for new conversation (no conversation ID)"""
        
        mock_message = MagicMock()
        mock_message.routing_key = "message.chatbot.user.response"
        
        payload = {
            "message": "Hi, I need help!",
            "chatbotConversationId": None,  # New conversation
            "completed": False
        }
        
        with patch.object(message_event_listener, '_process_conversation_message') as mock_process:
            # Handle the event
            await message_event_listener.handle_user_message_event(payload, mock_message)
            
            # Verify processing was NOT called for new conversations
            mock_process.assert_not_called()


class TestEventInfrastructure:
    """Test the event infrastructure setup"""
    
    @pytest.mark.asyncio
    async def test_rabbitmq_setup_whatsapp_chatbot_publisher(self):
        """Test setting up WhatsApp chatbot publisher"""
        
        with patch.object(rabbitmq_service, 'declare_exchange') as mock_declare:
            mock_declare.return_value = MagicMock()
            
            await rabbitmq_service.setup_whatsapp_chatbot_publisher()
            
            mock_declare.assert_called_once_with("ex.whatsappChatbot", "topic")
    
    @pytest.mark.asyncio
    async def test_rabbitmq_setup_message_listener(self):
        """Test setting up message listener"""
        
        with patch.object(rabbitmq_service, 'declare_exchange') as mock_declare_exchange, \
             patch.object(rabbitmq_service, 'declare_queue') as mock_declare_queue, \
             patch.object(rabbitmq_service, 'bind_queue_to_exchange') as mock_bind:
            
            mock_declare_exchange.return_value = MagicMock()
            mock_declare_queue.return_value = MagicMock()
            mock_bind.return_value = None
            
            await rabbitmq_service.setup_message_listener()
            
            # Verify setup calls
            mock_declare_exchange.assert_called_once_with("ex.message", "topic")
            mock_declare_queue.assert_called_once_with("q.message.chatbot.user.response.chatbot", durable=True)
            mock_bind.assert_called_once_with(
                "q.message.chatbot.user.response.chatbot",
                "ex.message",
                "message.chatbot.user.response"
            )


if __name__ == "__main__":
    pytest.main([__file__])
