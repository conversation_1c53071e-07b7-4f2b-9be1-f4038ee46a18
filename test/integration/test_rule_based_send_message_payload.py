import pytest
import uuid
import json
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.message_event_listener import message_event_listener
from app.services.rabbitmq_service import rabbitmq_service


@pytest.mark.asyncio
async def test_rule_based_send_message_exact_json_payload():
    conversation_id = str(uuid.uuid4())
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, name, type, data):
            self.node_id = node_id
            self.name = name
            self.type = type
            self.data = data

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Start question -> sendMessage (with text + media)
    start_node = Node("n1", "n1", "question", {
        "text": "Pick:",
        "options": [{"name": "h1", "text": "Show"}]
    })
    media_payload = {
        "fileId": 11,
        "fileName": "myPhonto.png",
        "fileSize": 111111,
        "fileType": "png",
        "fileCaption": "caption to show"
    }
    # Use numeric-like node_id so id becomes int in nodeDetails
    next_node = Node("123333", "n4", "sendMessage", {
        "options": [
            {"type": "text", "position": 12, "text": "Question to be ask {{1}}"},
            {"type": "media", "position": 13, "mediaFile": media_payload}
        ]
    })
    
    # Add a final node so sendMessage is not terminal
    final_node = Node("n2", "final_node", "question", {
        "text": "Thank you!"
    })
    
    # Add another final node so question is not terminal
    very_final_node = Node("n3", "very_final_node", "sendMessage", {
        "text": "Goodbye!"
    })
    
    edges = [Edge("n1", "h1", "123333")]
    sendmessage_edges = [Edge("123333", "default", "n2")]
    question_edges = [Edge("n2", "default", "n3")]

    # Override helpers to avoid DB
    message_event_listener._get_start_node = lambda db, state: start_node
    message_event_listener._load_node = lambda db, state, node_id: {
        "n1": start_node,
        "123333": next_node,
        "n2": final_node,
        "n3": very_final_node
    }.get(node_id)
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: {
        "n1": edges,
        "123333": sendmessage_edges,
        "n2": question_edges,
        "n3": []
    }.get(node_id, [])

    state = {"chatbot_id": "cb-1", "tenant_id": 1, "chatbotType": "RULE", "history": []}

    with patch.object(rabbitmq_service, 'publish_message') as mock_publish:
        mock_publish.return_value = AsyncMock(return_value=None)

        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state, "Show", conversation_id
        )

        # Ensure publish called once
        assert mock_publish.await_count == 1
        _, kwargs = mock_publish.await_args

        # Exchange and routing key should match
        assert kwargs["exchange"] == "ex.whatsappChatbot"
        assert kwargs["routing_key"] == "chatbot.conversation.response"

        # Parse exact JSON payload
        payload = json.loads(kwargs["message"])
        expected = {
                "chatbotConversationId": conversation_id,
                "message": None,
                "completed": False,
                "charge": 0,
                "chatbotType": "RULE",
                "nodeDetails": [
                    {
                        "id": "123333",
                        "name": "n4",
                        "type": "sendMessage",
                        "isFirstNode": False,
                        "data": [
                            {"type": "text", "text": "Question to be ask {{1}}"},
                            {"type": "media", "mediaFile": media_payload}
                        ]
                    },
                    {
                        "id": "n2",
                        "name": "final_node",
                        "type": "question",
                        "isFirstNode": False,
                        "data": {
                            "text": "Thank you!",
                            "options": []
                        }
                    }
                ]
            }

        assert payload == expected


@pytest.mark.asyncio
async def test_rule_based_sendmessage_chaining():
    """Test that sendMessage -> sendMessage nodes are automatically chained"""
    conversation_id = str(uuid.uuid4())
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, name, type, data, is_first_node=False):
            self.node_id = node_id
            self.name = name
            self.type = type
            self.data = data
            self.is_first_node = is_first_node

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Create a chain: question -> sendMessage -> sendMessage
    start_node = Node("n1", "n1", "question", {
        "text": "Choose option:",
        "options": [{"name": "h1", "text": "Continue"}]
    })
    
    first_sendmessage = Node("n2", "first_msg", "sendMessage", {
        "options": [
            {"type": "text", "text": "First message"}
        ]
    })
    
    second_sendmessage = Node("n3", "second_msg", "sendMessage", {
        "options": [
            {"type": "text", "text": "Second message"}
        ]
    })
    
    # Define edges: n1 -> n2 -> n3
    edges_n1 = [Edge("n1", "h1", "n2")]
    edges_n2 = [Edge("n2", "default", "n3")]
    
    # Override helpers to avoid DB
    message_event_listener._get_start_node = lambda db, state: start_node
    message_event_listener._load_node = lambda db, state, node_id: {
        "n1": start_node,
        "n2": first_sendmessage,
        "n3": second_sendmessage
    }.get(node_id)
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: {
        "n1": edges_n1,
        "n2": edges_n2,
        "n3": []
    }.get(node_id, [])

    state = {"chatbot_id": "cb-1", "tenant_id": 1, "chatbotType": "RULE", "history": []}

    with patch.object(rabbitmq_service, 'publish_message') as mock_publish:
        mock_publish.return_value = AsyncMock(return_value=None)

        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state, "Continue", conversation_id
        )

        # Should be called once with all nodes in a single event
        assert mock_publish.await_count == 1
        
        # Check the single event containing all nodes
        call_args, call_kwargs = mock_publish.await_args_list[0]
        payload = json.loads(call_kwargs["message"])
        
        # Should have nodeDetails as an array with all nodes
        assert isinstance(payload["nodeDetails"], list)
        assert len(payload["nodeDetails"]) == 2
        
        # Check the first sendMessage node
        first_node = payload["nodeDetails"][0]
        assert first_node["id"] == "n2"
        assert first_node["name"] == "first_msg"
        assert first_node["type"] == "sendMessage"
        assert first_node["data"][0]["text"] == "First message"
        
        # Check the chained sendMessage node
        second_node = payload["nodeDetails"][1]
        assert second_node["id"] == "n3"
        assert second_node["name"] == "second_msg"
        assert second_node["type"] == "sendMessage"
        assert second_node["data"][0]["text"] == "Second message"


@pytest.mark.asyncio
async def test_rule_based_sendmessage_to_question_chaining():
    """Test that sendMessage -> question nodes are automatically chained"""
    conversation_id = str(uuid.uuid4())
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, name, type, data, is_first_node=False):
            self.node_id = node_id
            self.name = name
            self.type = type
            self.data = data
            self.is_first_node = is_first_node

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Create a chain: question -> sendMessage -> question
    start_node = Node("n1", "start_question", "question", {
        "text": "Choose option:",
        "options": [{"name": "h1", "text": "Continue"}]
    })
    
    sendmessage_node = Node("n2", "send_msg", "sendMessage", {
        "options": [
            {"type": "text", "text": "Hello from sendMessage"}
        ]
    })
    
    question_node = Node("n3", "question_node", "question", {
        "text": "What is your name?",
        "options": [
            {"text": "John", "name": "1"},
            {"text": "Jane", "name": "2"}
        ]
    })
    
    # Add a final node so question is not terminal
    final_node = Node("n4", "final_node", "sendMessage", {
        "text": "Thank you!"
    })
    
    # Define edges: n1 -> n2 -> n3 -> n4
    edges_n1 = [Edge("n1", "h1", "n2")]
    edges_n2 = [Edge("n2", "default", "n3")]
    edges_n3 = [Edge("n3", "default", "n4")]
    
    # Override helpers to avoid DB
    message_event_listener._get_start_node = lambda db, state: start_node
    message_event_listener._load_node = lambda db, state, node_id: {
        "n1": start_node,
        "n2": sendmessage_node,
        "n3": question_node,
        "n4": final_node
    }.get(node_id)
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: {
        "n1": edges_n1,
        "n2": edges_n2,
        "n3": edges_n3,
        "n4": []
    }.get(node_id, [])

    state = {"chatbot_id": "cb-1", "tenant_id": 1, "chatbotType": "RULE", "history": []}

    with patch.object(rabbitmq_service, 'publish_message') as mock_publish:
        mock_publish.return_value = AsyncMock(return_value=None)

        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state, "Continue", conversation_id
        )

        # Should be called once with all nodes in a single event
        assert mock_publish.await_count == 1
        
        # Check the single event containing all nodes
        call_args, call_kwargs = mock_publish.await_args_list[0]
        payload = json.loads(call_kwargs["message"])
        
        # Should have nodeDetails as an array with all nodes
        assert isinstance(payload["nodeDetails"], list)
        assert len(payload["nodeDetails"]) == 2
        
        # Check the first sendMessage node
        first_node = payload["nodeDetails"][0]
        assert first_node["id"] == "n2"
        assert first_node["name"] == "send_msg"
        assert first_node["type"] == "sendMessage"
        assert first_node["data"][0]["text"] == "Hello from sendMessage"
        
        # Check the chained question node
        second_node = payload["nodeDetails"][1]
        assert second_node["id"] == "n3"
        assert second_node["name"] == "question_node"
        assert second_node["type"] == "question"
        assert second_node["data"]["text"] == "What is your name?\n1. John\n2. Jane"
        assert payload["completed"] == False


