"""
Integration tests for Workflow Integration
"""

import pytest
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from sqlalchemy.orm import Session

from app.services.workflow_event_listener import workflow_event_listener
from app.services.message_event_listener import message_event_listener
from app.services.conversation_event_publisher import conversation_event_publisher
from app.models import Chatbot, ChatbotConversation, ChatbotNode, ChatbotQuestion


class TestWorkflowIntegration:
    """Integration tests for the complete workflow flow"""

    @pytest.fixture
    def workflow_trigger_payload(self):
        """Create a workflow trigger payload"""
        return {
            "metadata": {
                "tenantId": 478,
                "userId": "794",
                "entityType": "WHATSAPP_MESSAGE",
                "workflowId": "WF_314",
                "executedWorkflows": ["WF_302", "WF_314"],
                "entityAction": "CREATED",
                "executeWorkflow": True,
                "entityId": 123,
                "workflowName": "WorkflowNo 14",
                "eventId": 1
            },
            "messageConversationId": 3390,
            "connectedAccount": {
                "id": 1,
                "name": "whatsapp"
            },
            "chatbot": {
                "id": "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                "name": "Test Chatbot"
            },
            "entityDetails": [
                {
                    "id": 1,
                    "name": "Lead 1",
                    "entity": "lead",
                    "email": None
                }
            ]
        }

    @pytest.fixture
    def user_response_payload(self):
        """Create a user response payload"""
        return {
            "message": "My name is John Doe",
            "chatbotConversationId": "test-conversation-id"
        }

    @pytest.fixture
    def mock_chatbot(self):
        """Create a mock chatbot"""
        chatbot = Mock(spec=Chatbot)
        chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
        chatbot.name = "Test Chatbot"
        chatbot.type = "RULE"
        chatbot.welcome_message = "Welcome to Test Chatbot!"
        return chatbot

    @pytest.fixture
    def mock_first_node(self):
        """Create a mock first node"""
        node = Mock(spec=ChatbotNode)
        node.node_id = "node-1"
        node.data = {"text": "What is your name?"}
        return node

    @pytest.mark.asyncio
    async def test_complete_workflow_flow(self, workflow_trigger_payload, user_response_payload, mock_chatbot, mock_first_node):
        """Test the complete workflow from trigger to user response"""
        
        # Mock all external dependencies
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db_workflow, \
             patch('app.services.message_event_listener.get_db') as mock_get_db_message, \
             patch('app.services.workflow_event_listener.RedisService') as mock_redis_service, \
             patch('app.services.message_event_listener.RedisService') as mock_redis_service_msg, \
             patch('app.services.workflow_event_listener.charge_calculator') as mock_charge_calculator, \
             patch('app.services.workflow_event_listener.rabbitmq_service') as mock_rabbitmq_workflow, \
             patch('app.services.message_event_listener.rabbitmq_service') as mock_rabbitmq_message, \
             patch('app.services.conversation_event_publisher.rabbitmq_service') as mock_rabbitmq_publisher:
            
            # Setup database mocks
            mock_db_workflow = Mock(spec=Session)
            mock_db_message = Mock(spec=Session)
            mock_get_db_workflow.return_value = iter([mock_db_workflow])
            mock_get_db_message.return_value = iter([mock_db_message])
            
            # Setup Redis mocks
            mock_redis = Mock()
            mock_redis_service.return_value = mock_redis
            mock_redis_service_msg.return_value = mock_redis
            
            # Mock conversation state
            conversation_state = {
                "conversation_id": "test-conversation-id",
                "chatbot_id": "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                "tenant_id": 478,
                "user_id": "794",
                "message_conversation_id": 3390,
                "connected_account": {"id": 1, "name": "whatsapp"},
                "entity_details": [],
                "workflow_metadata": workflow_trigger_payload["metadata"],
                "current_node": "node-1",
                "conversation_turns": [],
                "current_charge": 0,
                "total_charge": 0,
                "completed": False,
                "started_at": None,
                "last_activity": None
            }
            mock_redis.get_conversation_state.return_value = conversation_state
            mock_redis.store_conversation_state = Mock()
            mock_redis.update_conversation_last_activity = Mock()
            
            # Setup charge calculator mock
            mock_charge_calculator.calculate_question_charge.return_value = 1
            
            # Setup RabbitMQ mocks
            mock_rabbitmq_workflow.connect = AsyncMock(return_value=True)
            mock_rabbitmq_workflow.setup_workflow_listener = AsyncMock()
            mock_rabbitmq_workflow.setup_whatsapp_chatbot_publisher = AsyncMock()
            mock_rabbitmq_workflow.register_event_handler = Mock()
            mock_rabbitmq_workflow.start_consuming = AsyncMock()
            mock_rabbitmq_workflow.stop_consuming = AsyncMock()
            
            mock_rabbitmq_message.connect = AsyncMock(return_value=True)
            mock_rabbitmq_message.setup_message_listener = AsyncMock()
            mock_rabbitmq_message.setup_whatsapp_chatbot_publisher = AsyncMock()
            mock_rabbitmq_message.register_event_handler = Mock()
            mock_rabbitmq_message.start_consuming = AsyncMock()
            mock_rabbitmq_message.stop_consuming = AsyncMock()
            
            mock_rabbitmq_publisher.publish_message = AsyncMock(return_value=True)
            
            # Setup database queries
            # For workflow trigger - no active conversation, chatbot found, first node found
            mock_db_workflow.query.return_value.filter.return_value.first.side_effect = [
                None,  # No active conversation
                mock_chatbot,  # Chatbot found
                mock_first_node  # First node found
            ]
            
            # For user response - conversation found
            mock_conversation = Mock(spec=ChatbotConversation)
            mock_conversation.id = "test-conversation-id"
            mock_conversation.chatbot_id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            mock_conversation.tenant_id = 478
            mock_conversation.user_id = "794"
            mock_conversation.completed = False
            
            mock_db_message.query.return_value.filter.return_value.first.return_value = mock_conversation
            
            # Mock message objects
            workflow_message = Mock()
            workflow_message.routing_key = "workflow.trigger.chatbot"
            workflow_message.exchange = "ex.workflow"
            
            user_message = Mock()
            user_message.routing_key = "message.chatbot.user.response"
            user_message.exchange = "ex.message"
            
            # Start listeners
            await workflow_event_listener.start()
            await message_event_listener.start()
            
            # Step 1: Handle workflow trigger event
            await workflow_event_listener.handle_workflow_trigger_event(
                workflow_trigger_payload, workflow_message
            )
            
            # Verify workflow processing
            assert mock_redis.store_conversation_state.called
            assert mock_rabbitmq_publisher.publish_message.called
            
            # Step 2: Handle user response event
            await message_event_listener.handle_user_message_event(
                user_response_payload, user_message
            )
            
            # Verify user response processing
            assert mock_redis.get_conversation_state.called
            assert mock_redis.update_conversation_last_activity.called
            
            # Stop listeners
            await workflow_event_listener.stop()
            await message_event_listener.stop()

    @pytest.mark.asyncio
    async def test_workflow_trigger_conversation_already_in_progress(self, workflow_trigger_payload, mock_chatbot):
        """Test workflow trigger when conversation is already in progress"""
        
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db:
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock active conversation found
            mock_conversation = Mock(spec=ChatbotConversation)
            mock_conversation.id = "existing-conversation-id"
            mock_conversation.message_conversation_id = 3390
            mock_conversation.tenant_id = 478
            mock_conversation.completed = False
            mock_db.query.return_value.filter.return_value.first.return_value = mock_conversation
            
            workflow_message = Mock()
            workflow_message.routing_key = "workflow.trigger.chatbot"
            workflow_message.exchange = "ex.workflow"
            
            # Execute - should not raise exception but log error
            await workflow_event_listener.handle_workflow_trigger_event(
                workflow_trigger_payload, workflow_message
            )

    @pytest.mark.asyncio
    async def test_workflow_trigger_chatbot_not_found(self, workflow_trigger_payload):
        """Test workflow trigger when chatbot is not found"""
        
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db:
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            
            # Mock no active conversation, but chatbot not found
            mock_db.query.return_value.filter.return_value.first.side_effect = [
                None,  # No active conversation
                None   # Chatbot not found
            ]
            
            workflow_message = Mock()
            workflow_message.routing_key = "workflow.trigger.chatbot"
            workflow_message.exchange = "ex.workflow"
            
            # Execute - should not raise exception but log error
            await workflow_event_listener.handle_workflow_trigger_event(
                workflow_trigger_payload, workflow_message
            )

    @pytest.mark.asyncio
    async def test_event_publishing_format(self, workflow_trigger_payload, mock_chatbot, mock_first_node):
        """Test that the published event has the correct format"""
        
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('app.services.workflow_event_listener.RedisService') as mock_redis_service, \
             patch('app.services.workflow_event_listener.charge_calculator') as mock_charge_calculator, \
             patch('app.services.conversation_event_publisher.rabbitmq_service') as mock_rabbitmq_publisher:
            
            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            mock_redis = Mock()
            mock_redis_service.return_value = mock_redis
            mock_charge_calculator.calculate_question_charge.return_value = 1
            
            # Mock database queries
            mock_db.query.return_value.filter.return_value.first.side_effect = [
                None,  # No active conversation
                mock_chatbot,  # Chatbot found
                mock_first_node  # First node found
            ]
            
            # Mock Redis operations
            mock_redis.store_conversation_state = Mock()
            
            # Capture published message
            published_messages = []
            async def capture_publish(exchange, routing_key, message, **kwargs):
                published_messages.append({
                    'exchange': exchange,
                    'routing_key': routing_key,
                    'message': json.loads(message)
                })
                return True
            
            mock_rabbitmq_publisher.publish_message = AsyncMock(side_effect=capture_publish)
            
            workflow_message = Mock()
            workflow_message.routing_key = "workflow.trigger.chatbot"
            workflow_message.exchange = "ex.workflow"
            
            # Execute
            await workflow_event_listener.handle_workflow_trigger_event(
                workflow_trigger_payload, workflow_message
            )
            
            # Verify published message format
            assert len(published_messages) == 1
            published_msg = published_messages[0]
            
            assert published_msg['exchange'] == 'ex.whatsappChatbot'
            assert published_msg['routing_key'] == 'chatbot.conversation.response'
            
            message_data = published_msg['message']
            assert 'chatbotConversationId' in message_data
            assert 'message' in message_data
            assert 'completed' in message_data
            assert 'charge' in message_data
            assert 'tenantId' in message_data
            assert 'messageConversationId' in message_data
            assert 'chatbot' in message_data
            assert 'connectedAccount' in message_data
            assert 'entityDetails' in message_data
            
            # Verify specific values
            assert message_data['completed'] == False
            assert message_data['charge'] == 1
            assert message_data['tenantId'] == 478
            assert message_data['messageConversationId'] == 3390
            assert message_data['chatbot']['id'] == 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12'
            assert message_data['connectedAccount']['id'] == 1
            assert message_data['connectedAccount']['name'] == 'whatsapp'

    @pytest.mark.asyncio
    async def test_ai_based_chatbot_charge_calculation(self, workflow_trigger_payload, mock_chatbot, mock_first_question):
        """Test that AI-based chatbots use charge calculator for charge calculation"""
        
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('app.services.workflow_event_listener.RedisService') as mock_redis_service, \
             patch('app.services.workflow_event_listener.charge_calculator') as mock_charge_calculator, \
             patch('app.services.conversation_event_publisher.rabbitmq_service') as mock_rabbitmq_publisher:
            
            # Setup mocks
            mock_db = Mock(spec=Session)
            mock_get_db.return_value = iter([mock_db])
            mock_redis = Mock()
            mock_redis_service.return_value = mock_redis
            mock_charge_calculator.calculate_question_charge.return_value = 1  # AI chatbot charge
            
            # Create AI-based chatbot
            ai_chatbot = Mock(spec=Chatbot)
            ai_chatbot.id = "ai-chatbot-id"
            ai_chatbot.name = "AI Chatbot"
            ai_chatbot.type = "AI"
            ai_chatbot.welcome_message = "Welcome to AI Chatbot!"
            
            # Mock database queries
            mock_db.query.return_value.filter.return_value.first.side_effect = [ai_chatbot, mock_first_question]
            
            # Mock Redis operations
            mock_redis.store_conversation_state = Mock()
            
            # Capture published message
            published_messages = []
            async def capture_publish(exchange, routing_key, message, **kwargs):
                published_messages.append({
                    'exchange': exchange,
                    'routing_key': routing_key,
                    'message': json.loads(message)
                })
                return True
            
            mock_rabbitmq_publisher.publish_message = AsyncMock(side_effect=capture_publish)
            
            workflow_message = Mock()
            workflow_message.routing_key = "workflow.trigger.chatbot"
            workflow_message.exchange = "ex.workflow"
            
            # Execute
            await workflow_event_listener.handle_workflow_trigger_event(
                workflow_trigger_payload, workflow_message
            )
            
            # Verify published message format
            assert len(published_messages) == 1
            published_msg = published_messages[0]
            
            message_data = published_msg['message']
            
            # Verify the charge was calculated via charge calculator for AI chatbot
            assert message_data['charge'] == 1  # AI type chatbot charge
            
            # Verify charge calculator was called
            mock_charge_calculator.calculate_question_charge.assert_called_once()
