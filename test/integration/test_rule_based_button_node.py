import pytest
import uuid
import json
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.message_event_listener import message_event_listener
from app.services.rabbitmq_service import rabbitmq_service


@pytest.mark.asyncio
async def test_rule_based_button_node_payload():
    """Test that button nodes are properly handled with correct payload structure"""
    conversation_id = str(uuid.uuid4())
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, name, type, data, is_first_node=False):
            self.node_id = node_id
            self.name = name
            self.type = type
            self.data = data
            self.is_first_node = is_first_node

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Start question -> button node
    start_node = Node("n1", "start_question", "question", {
        "text": "Choose option:",
        "options": [{"name": "h1", "text": "Continue"}]
    })
    
    button_node = Node("node_1758869039023", "node_1758869039023", "buttons", {
        "header": "Header Text {{1}}",
        "body": "Body Here{{1}}",
        "footer": "Footer Here",
        "buttons": [
            {"id": "btn-1", "text": "Button 1", "position": 0},
            {"id": "btn-2", "text": "Button 2", "position": 1}
        ],
        "dataVariable": "xyz",
        "entityFields": [
            {
                "entityType": "LEAD",
                "fieldId": 129273,
                "standard": True,
                "displayName": "First Name",
                "name": "firstName"
            },
            {
                "entityType": "CONTACT",
                "fieldId": 129366,
                "standard": True,
                "displayName": "Patient Full Name ",
                "name": "lastName"
            }
        ]
    }, is_first_node=False)
    
    # Add a next node so button is not terminal
    next_node = Node("n2", "next_question", "question", {
        "text": "What is your name?",
        "options": []
    })
    
    edges = [Edge("n1", "h1", "node_1758869039023")]
    button_edges = [Edge("node_1758869039023", "Button 1", "n2")]

    # Override helpers to avoid DB
    message_event_listener._get_start_node = lambda db, state: start_node
    message_event_listener._load_node = lambda db, state, node_id: {
        "n1": start_node,
        "node_1758869039023": button_node,
        "n2": next_node
    }.get(node_id)
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: {
        "n1": edges,
        "node_1758869039023": button_edges,
        "n2": []
    }.get(node_id, [])

    state = {"chatbot_id": "cb-1", "tenant_id": 1, "chatbotType": "RULE", "history": []}

    with patch.object(rabbitmq_service, 'publish_message') as mock_publish:
        mock_publish.return_value = AsyncMock(return_value=None)

        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state, "Continue", conversation_id
        )

        # Ensure publish called once
        assert mock_publish.await_count == 1
        _, kwargs = mock_publish.await_args

        # Exchange and routing key should match
        assert kwargs["exchange"] == "ex.whatsappChatbot"
        assert kwargs["routing_key"] == "chatbot.conversation.response"

        # Parse exact JSON payload
        payload = json.loads(kwargs["message"])
        expected = {
            "chatbotConversationId": conversation_id,
            "message": None,  # Button nodes use nodeDetails, not message
            "completed": False,
            "charge": 0,
            "chatbotType": "RULE",
            "nodeDetails": [
                    {
                        "id": "node_1758869039023",
                        "name": "node_1758869039023",
                        "type": "buttons",
                        "isFirstNode": False,
                        "data": {
                            "header": "Header Text {{1}}",
                            "body": "Body Here{{1}}",
                            "footer": "Footer Here",
                            "buttons": [
                                {"id": "btn-1", "text": "Button 1", "position": 0},
                                {"id": "btn-2", "text": "Button 2", "position": 1}
                            ]
                        }
                    }
            ]
        }

        assert payload == expected


@pytest.mark.asyncio
async def test_rule_based_button_node_with_variable_substitution():
    """Test that button nodes handle variable substitution correctly"""
    conversation_id = str(uuid.uuid4())
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, name, type, data, is_first_node=False, variable_mapping=None):
            self.node_id = node_id
            self.name = name
            self.type = type
            self.data = data
            self.is_first_node = is_first_node
            self.variable_mapping = variable_mapping

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Start question -> button node with variable mapping
    start_node = Node("n1", "start_question", "question", {
        "text": "Choose option:",
        "options": [{"name": "h1", "text": "Continue"}]
    })
    
    button_node = Node("node_1758869039023", "node_1758869039023", "buttons", {
        "header": "Header Text {{1}}",
        "body": "Body Here{{1}}",
        "footer": "Footer Here",
        "buttons": [
            {"id": "btn-1", "text": "Button 1", "position": 0},
            {"id": "btn-2", "text": "Button 2", "position": 1}
        ]
    }, is_first_node=False, variable_mapping=[
        {
            "entity": "contact",
            "internalName": "salutation",
            "fallbackValue": "none 11",
            "variable": "1",
            "componentType": "HEADER",
            "fieldType": "PICK_LIST"
        },
        {
            "entity": "contact",
            "internalName": "firstName",
            "fallbackValue": "",
            "variable": "1",
            "componentType": "BODY",
            "fieldType": "TEXT_FIELD"
        }
    ])
    
    # Add a next node so button is not terminal
    next_node = Node("n2", "next_question", "question", {
        "text": "What is your name?",
        "options": []
    })
    
    edges = [Edge("n1", "h1", "node_1758869039023")]
    button_edges = [Edge("node_1758869039023", "Button 1", "n2")]

    # Override helpers to avoid DB
    message_event_listener._get_start_node = lambda db, state: start_node
    message_event_listener._load_node = lambda db, state, node_id: {
        "n1": start_node,
        "node_1758869039023": button_node,
        "n2": next_node
    }.get(node_id)
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: {
        "n1": edges,
        "node_1758869039023": button_edges,
        "n2": []
    }.get(node_id, [])

    state = {"chatbot_id": "cb-1", "tenant_id": 1, "chatbotType": "RULE", "history": []}

    with patch.object(rabbitmq_service, 'publish_message') as mock_publish:
        mock_publish.return_value = AsyncMock(return_value=None)

        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state, "Continue", conversation_id
        )

        # Ensure publish called once
        assert mock_publish.await_count == 1
        _, kwargs = mock_publish.await_args

        # Exchange and routing key should match
        assert kwargs["exchange"] == "ex.whatsappChatbot"
        assert kwargs["routing_key"] == "chatbot.conversation.response"

        # Parse exact JSON payload
        payload = json.loads(kwargs["message"])
        
        # Verify the structure includes button node details
        assert payload["chatbotConversationId"] == conversation_id
        assert payload["message"] is None  # Button nodes use nodeDetails
        assert payload["completed"] is False
        assert payload["chatbotType"] == "RULE"
        assert len(payload["nodeDetails"]) == 1
        
        button_node_details = payload["nodeDetails"][0]
        assert button_node_details["id"] == "node_1758869039023"
        assert button_node_details["name"] == "node_1758869039023"
        assert button_node_details["type"] == "buttons"
        assert button_node_details["isFirstNode"] is False
        
        # Verify button data structure
        button_data = button_node_details["data"]
        assert "header" in button_data
        assert "body" in button_data
        assert "footer" in button_data
        assert "buttons" in button_data
        assert len(button_data["buttons"]) == 2
        assert button_data["buttons"][0]["id"] == "btn-1"
        assert button_data["buttons"][0]["text"] == "Button 1"
        assert button_data["buttons"][0]["position"] == 0
        assert button_data["buttons"][1]["id"] == "btn-2"
        assert button_data["buttons"][1]["text"] == "Button 2"
        assert button_data["buttons"][1]["position"] == 1


@pytest.mark.asyncio
async def test_rule_based_button_node_user_selection():
    """Test that button node handles user button selection correctly"""
    conversation_id = str(uuid.uuid4())
    mock_db = MagicMock()

    class Node:
        def __init__(self, node_id, name, type, data, is_first_node=False):
            self.node_id = node_id
            self.name = name
            self.type = type
            self.data = data
            self.is_first_node = is_first_node

    class Edge:
        def __init__(self, source_node, source_handle, target_node):
            self.source_node = source_node
            self.source_handle = source_handle
            self.target_node = target_node

    # Button node -> next question
    button_node = Node("node_1758869039023", "node_1758869039023", "buttons", {
        "header": "Choose an option:",
        "body": "Please select one of the following buttons:",
        "footer": "Footer Here",
        "buttons": [
            {"id": "btn-a", "text": "Option A", "position": 0},
            {"id": "btn-b", "text": "Option B", "position": 1}
        ]
    })
    
    next_node = Node("n2", "next_question", "question", {
        "text": "What is your name?",
        "options": []
    })
    
    # Add a final node so next_node is not terminal
    final_node = Node("n3", "final_node", "sendMessage", {
        "text": "Thank you!"
    })
    
    edges = [
        Edge("node_1758869039023", "btn-a", "n2"),
        Edge("node_1758869039023", "btn-b", "n2")
    ]
    next_edges = [Edge("n2", "default", "n3")]

    # Override helpers to avoid DB
    message_event_listener._get_start_node = lambda db, state: button_node
    message_event_listener._load_node = lambda db, state, node_id: {
        "node_1758869039023": button_node,
        "n2": next_node,
        "n3": final_node
    }.get(node_id)
    message_event_listener._get_outgoing_edges = lambda db, state, node_id: {
        "node_1758869039023": edges,
        "n2": next_edges,
        "n3": []
    }.get(node_id, [])

    state = {"chatbot_id": "cb-1", "tenant_id": 1, "chatbotType": "RULE", "history": []}

    with patch.object(rabbitmq_service, 'publish_message') as mock_publish:
        mock_publish.return_value = AsyncMock(return_value=None)

        # Test user selecting button with ID "btn-a"
        await message_event_listener._continue_rule_based_logic(
            mock_db, MagicMock(), state, "btn-a", conversation_id
        )

        # Ensure publish called once
        assert mock_publish.await_count == 1
        _, kwargs = mock_publish.await_args

        # Exchange and routing key should match
        assert kwargs["exchange"] == "ex.whatsappChatbot"
        assert kwargs["routing_key"] == "chatbot.conversation.response"

        # Parse exact JSON payload
        payload = json.loads(kwargs["message"])
        
        # Verify the structure includes next question node details
        assert payload["chatbotConversationId"] == conversation_id
        assert payload["message"] is None  # Question nodes use nodeDetails
        assert payload["completed"] is False
        assert payload["chatbotType"] == "RULE"
        assert len(payload["nodeDetails"]) == 1
        
        question_node_details = payload["nodeDetails"][0]
        assert question_node_details["id"] == "n2"
        assert question_node_details["name"] == "next_question"
        assert question_node_details["type"] == "question"
        assert question_node_details["isFirstNode"] is False