#!/usr/bin/env python3
"""
Test script to verify authentication fixes
"""

import asyncio
import sys
import os
import pytest
from unittest.mock import MagicMock
from jose import jwt

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

@pytest.mark.asyncio
async def test_auth_with_problematic_token():
    """Test authentication with the problematic token format"""
    print("🔐 Testing Authentication with Problematic Token")
    print("=" * 60)
    
    # Simulate the problematic token from the error
    problematic_payload = {
        "data": {
            "accessToken": "1c77c32f-test-token",
            "userId": 3841,  # int instead of string
            "tenantId": 2048,
            "permissions": [
                {
                    "action": {"read": True}
                    # Missing: id, name, description, limits, units
                }
            ]
            # Missing: refreshToken, username, source, meta
        }
        # Missing: iss
    }
    
    print("Testing with token payload:")
    import json
    print(json.dumps(problematic_payload, indent=2))
    
    try:
        from app.dependencies import get_auth_context
        from fastapi.security import HTTPAuthorizationCredentials
        from unittest.mock import AsyncMock
        
        # Create a test JWT token
        test_token = jwt.encode(problematic_payload, "test-secret", algorithm="HS256")
        print(f"\nGenerated test JWT: {test_token[:50]}...")
        
        # Mock the credentials
        mock_credentials = MagicMock(spec=HTTPAuthorizationCredentials)
        mock_credentials.credentials = test_token
        
        # Mock the request
        mock_request = MagicMock()
        mock_request.state = MagicMock()
        
        # Test the auth context extraction
        auth_context = await get_auth_context(mock_credentials, mock_request)
        
        print("\n✅ Authentication successful!")
        print(f"   User ID: {auth_context.user_id}")
        print(f"   Tenant ID: {auth_context.tenant_id}")
        print(f"   Source Type: {auth_context.source_type}")
        print(f"   Source ID: {auth_context.source_id}")
        print(f"   Source Name: {auth_context.source_name}")
        print(f"   Permissions: {len(auth_context.permissions)} permission(s)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Authentication failed: {e}")
        import traceback
        print(traceback.format_exc())
        return False

@pytest.mark.asyncio
async def test_auth_with_minimal_token():
    """Test authentication with minimal required fields"""
    print("\n" + "=" * 60)
    print("🔐 Testing Authentication with Minimal Token")
    print("=" * 60)
    
    # Minimal token with just the essential fields
    minimal_payload = {
        "iss": "test-issuer",
        "data": {
            "accessToken": "minimal-test-token",
            "userId": "3841",  # string format
            "tenantId": 2048,
            "permissions": []
        }
    }
    
    print("Testing with minimal token payload:")
    import json
    print(json.dumps(minimal_payload, indent=2))
    
    try:
        from app.dependencies import get_auth_context
        from fastapi.security import HTTPAuthorizationCredentials
        from unittest.mock import AsyncMock
        
        # Create a test JWT token
        test_token = jwt.encode(minimal_payload, "test-secret", algorithm="HS256")
        print(f"\nGenerated minimal JWT: {test_token[:50]}...")
        
        # Mock the credentials
        mock_credentials = MagicMock(spec=HTTPAuthorizationCredentials)
        mock_credentials.credentials = test_token
        
        # Mock the request
        mock_request = MagicMock()
        mock_request.state = MagicMock()
        
        # Test the auth context extraction
        auth_context = await get_auth_context(mock_credentials, mock_request)
        
        print("\n✅ Minimal authentication successful!")
        print(f"   User ID: {auth_context.user_id}")
        print(f"   Tenant ID: {auth_context.tenant_id}")
        print(f"   Source Type: {auth_context.source_type}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Minimal authentication failed: {e}")
        return False

@pytest.mark.asyncio
async def test_auth_with_flat_token():
    """Test authentication with flat token structure"""
    print("\n" + "=" * 60)
    print("🔐 Testing Authentication with Flat Token Structure")
    print("=" * 60)
    
    # Flat token structure (no nested data)
    flat_payload = {
        "iss": "test-issuer",
        "accessToken": "flat-test-token",
        "userId": 3841,
        "tenantId": 2048,
        "permissions": [],
        "username": "test-user"
    }
    
    print("Testing with flat token payload:")
    import json
    print(json.dumps(flat_payload, indent=2))
    
    try:
        from app.dependencies import get_auth_context
        from fastapi.security import HTTPAuthorizationCredentials
        
        # Create a test JWT token
        test_token = jwt.encode(flat_payload, "test-secret", algorithm="HS256")
        print(f"\nGenerated flat JWT: {test_token[:50]}...")
        
        # Mock the credentials
        mock_credentials = MagicMock(spec=HTTPAuthorizationCredentials)
        mock_credentials.credentials = test_token
        
        # Mock the request
        mock_request = MagicMock()
        mock_request.state = MagicMock()
        
        # Test the auth context extraction
        auth_context = await get_auth_context(mock_credentials, mock_request)
        
        print("\n✅ Flat authentication successful!")
        print(f"   User ID: {auth_context.user_id}")
        print(f"   Tenant ID: {auth_context.tenant_id}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Flat authentication failed: {e}")
        return False

@pytest.mark.asyncio
async def test_missing_credentials():
    """Test handling of missing credentials"""
    print("\n" + "=" * 60)
    print("🔐 Testing Missing Credentials Handling")
    print("=" * 60)
    
    try:
        from app.dependencies import get_auth_context
        
        # Test with None credentials
        result = await get_auth_context(None, None)
        print("❌ Should have failed with missing credentials")
        return False
        
    except Exception as e:
        if "Authorization header missing" in str(e):
            print("✅ Correctly handled missing credentials")
            print(f"   Error: {e}")
            return True
        else:
            print(f"❌ Unexpected error: {e}")
            return False

async def main():
    """Run all authentication tests"""
    print("🧪 Authentication Fix Test Suite")
    print("=" * 60)
    
    tests = [
        ("Problematic Token", test_auth_with_problematic_token),
        ("Minimal Token", test_auth_with_minimal_token),
        ("Flat Token", test_auth_with_flat_token),
        ("Missing Credentials", test_missing_credentials)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All authentication tests passed!")
        print("\n💡 The JWT authentication should now work with:")
        print("   - Tokens with missing optional fields")
        print("   - Integer userId values (converted to string)")
        print("   - Incomplete permission objects")
        print("   - Both nested and flat token structures")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
        print("   Please check the authentication implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
