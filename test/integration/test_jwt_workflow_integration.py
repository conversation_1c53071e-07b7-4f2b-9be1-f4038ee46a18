"""
Integration tests for JWT and Workflow Event Processing

This module contains comprehensive integration tests that verify the complete
flow from workflow trigger to conversation response with JWT token creation
and metadata propagation.
"""

import pytest
import json
import uuid
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from app.services.workflow_event_listener import WorkflowEventListener
from app.services.message_event_listener import Message<PERSON><PERSON>L<PERSON>ener
from app.services.jwt_service import J<PERSON><PERSON>ervice
from app.models import Cha<PERSON>bot, ChatbotConversation


class TestJWTWorkflowIntegration:
    """Comprehensive integration tests for JWT and workflow processing"""

    @pytest.fixture
    def complete_workflow_payload(self):
        """Complete workflow trigger payload as specified in the requirements"""
        return {
            "metadata": {
                "tenantId": 478,
                "userId": 794,
                "entityType": "WHATSAPP_MESSAGE",
                "workflowId": "WF_314",
                "executedWorkflows": ["WF_302", "WF_314"],
                "entityAction": "CREATED",
                "executeWorkflow": True,
                "entityId": 123,
                "workflowName": "WorkflowNo 14",
                "eventId": 1
            },
            "connectedAccount": {
                "id": 1,
                "name": "whatsapp"
            },
            "chatbot": {
                "id": "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                "name": "chatbot"
            },
            "entityDetails": [
                {
                    "id": 1,
                    "name": "Lead 1",
                    "entity": "lead",
                    "email": None
                },
                {
                    "id": 1,
                    "name": "deal 1",
                    "entity": "deal",
                    "email": None
                },
                {
                    "id": 1,
                    "name": "contact 1",
                    "entity": "contact",
                    "email": None
                }
            ],
            "messageConversationId": 123
        }

    @pytest.fixture
    def mock_iam_user_response(self):
        """Mock IAM service response for user data"""
        return {
            "id": 794,
            "firstName": "John",
            "lastName": "Doe",
            "tenantId": "478",
            "email": "<EMAIL>",
            "permissions": [
                {
                    "id": 1,
                    "name": "user",
                    "description": "has access to user resource",
                    "action": {
                        "read": True, "readAll": True, "write": True,
                        "update": True, "updateAll": True
                    }
                },
                {
                    "id": 2,
                    "name": "task",
                    "description": "has access to task resource",
                    "action": {
                        "read": True, "readAll": True, "write": True,
                        "update": True, "updateAll": True
                    }
                }
            ]
        }

    @pytest.mark.asyncio
    async def test_complete_workflow_trigger_to_conversation_response_flow(
        self, complete_workflow_payload, mock_iam_user_response
    ):
        """Test complete flow from workflow trigger to conversation response with JWT and metadata"""
        
        # Create service instances
        workflow_listener = WorkflowEventListener()
        
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('requests.get') as mock_requests_get, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch.object(workflow_listener.conversation_event_publisher, 'publish_conversation_response') as mock_publish:
            
            # Mock database setup
            mock_db = Mock()
            mock_get_db.return_value = iter([mock_db])
            
            # Mock IAM service response
            mock_response = MagicMock()
            mock_response.json.return_value = mock_iam_user_response
            mock_response.raise_for_status.return_value = None
            mock_requests_get.return_value = mock_response
            
            # Mock chatbot
            mock_chatbot = Mock(spec=Chatbot)
            mock_chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            mock_chatbot.name = "Integration Test Chatbot"
            mock_chatbot.type = "RULE"
            mock_chatbot.welcome_message = "Welcome to our integration test!"
            
            # Mock first question for RULE-based chatbot
            mock_first_question = Mock()
            mock_first_question.id = "first-question"
            mock_first_question.data = {"text": "What can I help you with?"}
            
            # Setup database query mocks
            mock_db.query.return_value.filter.return_value.first.side_effect = [mock_chatbot, mock_first_question]
            
            # Mock Redis store operation
            stored_states = {}
            def store_state(conv_id, state):
                stored_states[conv_id] = state
            mock_redis.store_conversation_state.side_effect = store_state
            
            # Execute the workflow trigger processing
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id=794,
                connected_account=complete_workflow_payload["connectedAccount"],
                entity_details=complete_workflow_payload["entityDetails"],
                metadata=complete_workflow_payload["metadata"],
                reply_headers={"replyToExchange": "test.exchange", "replyToEvent": "test.event", "eventId": 80}
            )
            
            # Verify JWT token was created by checking IAM call
            mock_requests_get.assert_called()
            iam_call_args = mock_requests_get.call_args
            expected_iam_url = "http://sd-iam/v1/tenants/478/creator"
            assert iam_call_args[0][0] == expected_iam_url
            assert "Authorization" in iam_call_args[1]["headers"]
            
            # Verify conversation state was stored with all required fields
            assert len(stored_states) == 1
            conversation_id = list(stored_states.keys())[0]
            stored_state = stored_states[conversation_id]
            
            # Verify JWT and user information
            assert stored_state["jwt_token"] is not None
            assert isinstance(stored_state["jwt_token"], str)
            assert len(stored_state["jwt_token"]) > 0
            assert stored_state["user_id"] == 794  # Should use IAM user ID (long integer)
            assert stored_state["original_user_id"] == 794    # Should preserve original
            
            # Verify workflow metadata
            assert stored_state["workflow_metadata"] == complete_workflow_payload["metadata"]
            assert stored_state["connected_account"] == complete_workflow_payload["connectedAccount"]
            assert stored_state["entity_details"] == complete_workflow_payload["entityDetails"]
            assert stored_state["message_conversation_id"] == 123
            
            # Verify conversation response was published with metadata
            mock_publish.assert_called_once()
            publish_args = mock_publish.call_args
            
            # Check basic response parameters
            assert publish_args[1]["chatbot_conversation_id"] == conversation_id
            assert publish_args[1]["message"] == "Welcome to our integration test!"
            assert publish_args[1]["completed"] == False
            assert publish_args[1]["tenant_id"] == 478
            assert publish_args[1]["message_conversation_id"] == 123
            
            # Check extra data contains all metadata
            extra_data = publish_args[1]["extra"]
            assert extra_data["chatbot"]["id"] == "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            assert extra_data["chatbot"]["name"] == "Integration Test Chatbot"
            assert extra_data["chatbot"]["type"] == "RULE"
            assert extra_data["connectedAccount"] == complete_workflow_payload["connectedAccount"]
            assert extra_data["entityDetails"] == complete_workflow_payload["entityDetails"]
            assert extra_data["metadata"] == complete_workflow_payload["metadata"]
            assert extra_data["jwtToken"] == stored_state["jwt_token"]
            assert extra_data["actualUserId"] == 794

    @pytest.mark.asyncio
    async def test_workflow_to_user_message_response_flow(
        self, complete_workflow_payload, mock_iam_user_response
    ):
        """Test complete flow including user message response with metadata propagation"""
        
        workflow_listener = WorkflowEventListener()
        message_listener = MessageEventListener()
        
        # Step 1: Process workflow trigger
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('requests.get') as mock_requests_get, \
             patch.object(workflow_listener, 'redis_service') as mock_redis, \
             patch.object(workflow_listener.conversation_event_publisher, 'publish_conversation_response') as mock_workflow_publish:
            
            # Mock database and services for workflow processing
            mock_db = Mock()
            mock_get_db.return_value = iter([mock_db])
            
            mock_response = MagicMock()
            mock_response.json.return_value = mock_iam_user_response
            mock_response.raise_for_status.return_value = None
            mock_requests_get.return_value = mock_response
            
            # Mock chatbot and first question
            mock_chatbot = Mock(spec=Chatbot)
            mock_chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            mock_chatbot.name = "Flow Test Chatbot"
            mock_chatbot.type = "AI"  # AI type for different flow
            mock_chatbot.welcome_message = "How can I assist you today?"
            
            mock_first_question = Mock()
            mock_first_question.id = "q1"
            mock_first_question.question = "What's your main concern?"
            
            mock_db.query.return_value.filter.return_value.first.side_effect = [mock_chatbot, mock_first_question]
            
            # Store conversation state for later retrieval
            stored_conversation_state = {}
            def store_state(conv_id, state):
                stored_conversation_state[conv_id] = state
            mock_redis.store_conversation_state.side_effect = store_state
            
            # Execute workflow trigger
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id=794,
                connected_account=complete_workflow_payload["connectedAccount"],
                entity_details=complete_workflow_payload["entityDetails"],
                metadata=complete_workflow_payload["metadata"],
                reply_headers={"replyToExchange": "test.exchange", "replyToEvent": "test.event", "eventId": 80}
            )
            
            # Get the stored conversation ID and state
            conversation_id = list(stored_conversation_state.keys())[0]
            workflow_state = stored_conversation_state[conversation_id]
        
        # Step 2: Process user message response using the stored state
        with patch('app.services.message_event_listener.conversation_event_publisher') as mock_message_publisher, \
             patch('app.services.message_event_listener.ChatbotService') as mock_chatbot_service_class, \
             patch('app.services.message_event_listener.EntityFieldService') as mock_entity_service_class:
            
            # Mock the async publisher
            mock_message_publisher.publish_conversation_response = AsyncMock()
            
            # Mock services for message processing
            mock_chatbot_service = Mock()
            mock_chatbot_service.get_conversation_questions_count.return_value = 1
            mock_chatbot_service_class.return_value = mock_chatbot_service
            
            mock_entity_service = Mock()
            mock_entity_service_class.return_value = mock_entity_service
            
            # Prepare state for message processing (simulating knowledge phase)
            message_state = workflow_state.copy()
            message_state["knowledgebase_id"] = None  # No knowledgebase for generic response
            message_state["ended"] = False
            message_state["chatbotType"] = "AI"
            
            with patch('app.services.message_event_listener.get_db') as mock_message_get_db:
                mock_message_db = Mock()
                mock_message_get_db.return_value = iter([mock_message_db])
                
                mock_es_service = Mock()
                
                # Process user message in knowledge phase
                await message_listener._handle_knowledge_phase(
                    mock_message_db,
                    message_state,
                    "I need help with my account",
                    conversation_id,
                    "478",
                    mock_es_service
                )
                
                # Verify message response includes complete metadata from workflow
                mock_message_publisher.publish_conversation_response.assert_called()
                message_call_args = mock_message_publisher.publish_conversation_response.call_args
                
                # Verify message response parameters
                assert message_call_args[1]["chatbot_conversation_id"] == conversation_id
                assert message_call_args[1]["completed"] == False
                assert message_call_args[1]["charge"] == 0
                
                # Verify complete metadata propagation from workflow to message response
                message_extra_data = message_call_args[1]["extra"]
                assert message_extra_data["chatbotType"] == "AI"
                assert message_extra_data["actualUserId"] == 794
                assert message_extra_data["jwtToken"] == workflow_state["jwt_token"]
                
                # Verify original workflow metadata is preserved
                assert message_extra_data["metadata"] == complete_workflow_payload["metadata"]
                assert message_extra_data["metadata"]["workflowId"] == "WF_314"
                assert message_extra_data["metadata"]["tenantId"] == 478
                assert message_extra_data["metadata"]["userId"] == 794
                
                # Verify entity and account details are preserved
                assert message_extra_data["connectedAccount"] == complete_workflow_payload["connectedAccount"]
                assert message_extra_data["entityDetails"] == complete_workflow_payload["entityDetails"]
                assert len(message_extra_data["entityDetails"]) == 3  # lead, deal, contact

    @pytest.mark.asyncio
    async def test_jwt_failure_does_not_break_metadata_flow(self, complete_workflow_payload):
        """Test that JWT creation failure doesn't break metadata propagation"""
        
        workflow_listener = WorkflowEventListener()
        
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('app.services.workflow_event_listener.jwt_service') as mock_jwt_service, \
             patch('app.services.workflow_event_listener.RedisService') as mock_redis_service, \
             patch.object(workflow_listener.conversation_event_publisher, 'publish_conversation_response') as mock_publish:
            
            # Mock database setup
            mock_db = Mock()
            mock_get_db.return_value = iter([mock_db])
            
            # Mock JWT service to fail
            mock_jwt_service.build_jwt_token_for_analysis.side_effect = Exception("IAM service down")
            
            # Mock Redis service
            mock_redis = Mock()
            mock_redis_service.return_value = mock_redis
            
            # Mock chatbot
            mock_chatbot = Mock(spec=Chatbot)
            mock_chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            mock_chatbot.name = "Resilient Chatbot"
            mock_chatbot.type = "RULE"
            mock_chatbot.welcome_message = "Welcome despite JWT failure!"
            
            mock_first_question = Mock()
            mock_first_question.id = "first-q"
            mock_first_question.data = {"text": "How can I help?"}
            
            mock_db.query.return_value.filter.return_value.first.side_effect = [mock_chatbot, mock_first_question]
            
            stored_states = {}
            def store_state(conv_id, state):
                stored_states[conv_id] = state
            mock_redis.store_conversation_state.side_effect = store_state
            
            # Execute workflow trigger - should not fail despite JWT error
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id=794,
                connected_account=complete_workflow_payload["connectedAccount"],
                entity_details=complete_workflow_payload["entityDetails"],
                metadata=complete_workflow_payload["metadata"]
            )
            
            # Verify workflow completed despite JWT failure
            assert len(stored_states) == 1
            conversation_id = list(stored_states.keys())[0]
            stored_state = stored_states[conversation_id]
            
            # Verify JWT is None but workflow metadata is preserved
            assert stored_state["jwt_token"] is None
            assert stored_state["user_id"] == 794  # Uses original user ID
            assert stored_state["workflow_metadata"] == complete_workflow_payload["metadata"]
            assert stored_state["connected_account"] == complete_workflow_payload["connectedAccount"]
            assert stored_state["entity_details"] == complete_workflow_payload["entityDetails"]
            
            # Verify conversation response was still published
            mock_publish.assert_called_once()
            publish_args = mock_publish.call_args
            
            # Check extra data still contains metadata (but no JWT)
            extra_data = publish_args[1]["extra"]
            assert extra_data["metadata"] == complete_workflow_payload["metadata"]
            assert extra_data["connectedAccount"] == complete_workflow_payload["connectedAccount"]
            assert extra_data["entityDetails"] == complete_workflow_payload["entityDetails"]
            assert extra_data["jwtToken"] is None  # JWT should be None due to failure
            assert extra_data["actualUserId"] == 794  # Should fallback to original user ID

    @pytest.mark.asyncio
    async def test_conversation_database_record_integrity(self, complete_workflow_payload, mock_iam_user_response):
        """Test that database conversation record maintains integrity with JWT and metadata"""
        
        workflow_listener = WorkflowEventListener()
        
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('requests.get') as mock_requests_get, \
             patch('app.services.workflow_event_listener.RedisService') as mock_redis_service, \
             patch.object(workflow_listener.conversation_event_publisher, 'publish_conversation_response'):
            
            # Mock database setup
            mock_db = Mock()
            mock_get_db.return_value = iter([mock_db])
            
            # Mock IAM service
            mock_response = MagicMock()
            mock_response.json.return_value = mock_iam_user_response
            mock_response.raise_for_status.return_value = None
            mock_requests_get.return_value = mock_response
            
            # Mock Redis service
            mock_redis = Mock()
            mock_redis_service.return_value = mock_redis
            
            # Mock chatbot
            mock_chatbot = Mock(spec=Chatbot)
            mock_chatbot.id = "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            mock_chatbot.name = "DB Test Chatbot"
            mock_chatbot.type = "RULE"
            mock_chatbot.welcome_message = "Testing database integrity"
            
            mock_first_question = Mock()
            mock_first_question.id = "db-test-q"
            mock_first_question.data = {"text": "Database test question"}
            
            mock_db.query.return_value.filter.return_value.first.side_effect = [mock_chatbot, mock_first_question]
            
            # Execute workflow trigger
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id="a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12",
                tenant_id=478,
                user_id=794,
                connected_account=complete_workflow_payload["connectedAccount"],
                entity_details=complete_workflow_payload["entityDetails"],
                metadata=complete_workflow_payload["metadata"]
            )
            
            # Verify database conversation record was created
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            
            # Get the conversation record that was added
            conversation_record = mock_db.add.call_args[0][0]
            
            # Verify conversation record fields
            assert conversation_record.chatbot_id == "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12"
            assert conversation_record.tenant_id == 478
            assert conversation_record.user_id == "794"  # Should use IAM user ID (converted to string for DB)
            assert conversation_record.completed == False
            assert conversation_record.message_conversation_id == 123
            assert conversation_record.connected_account_id == 1
            assert conversation_record.connected_account_name == "whatsapp"
            assert conversation_record.entity_details == complete_workflow_payload["entityDetails"]
            
            # Verify conversation data includes metadata but NOT JWT tokens (security best practice)
            conversation_data = json.loads(conversation_record.conversation_data)
            # JWT tokens should NOT be stored in database for security reasons
            assert "jwt_token" not in conversation_data
            assert "auth_token" not in conversation_data
            assert conversation_data["user_id"] == 794
            assert conversation_data["original_user_id"] == 794
            assert conversation_data["workflow_metadata"] == complete_workflow_payload["metadata"]
            assert conversation_data["connected_account"] == complete_workflow_payload["connectedAccount"]
            assert conversation_data["entity_details"] == complete_workflow_payload["entityDetails"]


class TestJWTWorkflowErrorScenarios:
    """Test error scenarios and edge cases in JWT workflow integration"""

    @pytest.mark.asyncio
    async def test_invalid_workflow_payload_structure(self):
        """Test handling of invalid workflow payload structure"""
        workflow_listener = WorkflowEventListener()
        
        # Invalid payload missing required fields
        invalid_payload = {
            "metadata": {
                "tenantId": 478
                # Missing userId and other required fields
            },
            "messageConversationId": 123
            # Missing chatbot, connectedAccount, entityDetails
        }
        
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value = iter([mock_db])
            
            # Should handle invalid payload gracefully
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id=None,  # Invalid chatbot ID
                tenant_id=478,
                user_id=None,    # Missing user ID
                connected_account={},
                entity_details=[],
                metadata=invalid_payload["metadata"]
            )
            
            # Should not create conversation record with invalid data
            mock_db.add.assert_not_called()

    @pytest.mark.asyncio
    async def test_chatbot_not_found_scenario(self):
        """Test scenario where chatbot is not found in database"""
        workflow_listener = WorkflowEventListener()
        
        with patch('app.services.workflow_event_listener.get_db') as mock_get_db, \
             patch('app.services.workflow_event_listener.jwt_service') as mock_jwt_service:
            
            mock_db = Mock()
            mock_get_db.return_value = iter([mock_db])
            
            # Mock JWT service
            mock_jwt_service.build_jwt_token_for_analysis.return_value = ("token", "user")
            
            # Mock chatbot query to return None (not found)
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            # Should handle missing chatbot gracefully
            await workflow_listener._process_workflow_trigger(
                message_conversation_id=123,
                chatbot_id="non-existent-chatbot",
                tenant_id=478,
                user_id=794,
                connected_account={"id": 1, "name": "whatsapp"},
                entity_details=[],
                metadata={"tenantId": 478, "userId": 794}
            )
            
            # Should not proceed with conversation creation
            mock_db.add.assert_not_called()
            mock_db.commit.assert_not_called()

    def test_jwt_service_configuration_edge_cases(self):
        """Test JWT service with various configuration edge cases"""
        
        # Test with missing environment variables
        with patch.dict('os.environ', {}, clear=True):
            jwt_service = JWTService()
            assert jwt_service.secret == "test"  # Default value
            assert jwt_service.iam_base_path == "http://sd-iam"  # Default value
        
        # Test with empty environment variables
        with patch.dict('os.environ', {"SECRET_KEY": "", "IAM_BASE_PATH": ""}):
            jwt_service = JWTService()
            assert jwt_service.secret == ""
            assert jwt_service.iam_base_path == ""
        
        # Test with valid environment variables
        with patch.dict('os.environ', {"SECRET_KEY": "prod-secret", "IAM_BASE_PATH": "https://iam.prod.com"}):
            jwt_service = JWTService()
            assert jwt_service.secret == "prod-secret"
            assert jwt_service.iam_base_path == "https://iam.prod.com"
