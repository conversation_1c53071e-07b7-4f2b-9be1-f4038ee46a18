#!/usr/bin/env python3
"""
Test script for the improved conversation flow:
1. Ask each question only once (even if it maps to multiple entities)
2. After predefined questions, allow custom questions from knowledgebase only
"""

def test_question_deduplication():
    """Test that questions are not duplicated for multiple entities"""
    print("🔍 Testing Question Deduplication...")
    
    # Mock questions from database
    mock_questions = [
        {
            "id": "q1",
            "question": "What is your name?",
            "entity_fields": [
                {"entity_type": "LEAD", "field_id": 123, "name": "firstName", "display_name": "Full Name", "standard": False},
                {"entity_type": "CONTACT", "field_id": 456, "name": "contactName", "display_name": "Full Name", "standard": False}
            ]
        },
        {
            "id": "q2",
            "question": "What is your email?",
            "entity_fields": [
                {"entity_type": "LEAD", "field_id": 124, "name": "email", "display_name": "Email", "standard": True}
            ]
        }
    ]
    
    # Mock entity types in conversation
    entity_types = ["LEAD", "CONTACT"]
    
    print(f"   Input: {len(mock_questions)} questions, {len(entity_types)} entity types")
    
    # Apply NEW logic: each question asked only once
    relevant_questions = []
    for q in mock_questions:
        matching_entity_fields = [ef for ef in q["entity_fields"] if ef["entity_type"] in entity_types]
        
        if matching_entity_fields:
            # Create ONE question entry that maps to ALL matching entity fields
            relevant_question = {
                "id": q["id"],  # Use original question ID (no entity suffix)
                "question_id": q["id"],
                "question": q["question"],
                "display_name": matching_entity_fields[0]["display_name"],
                "standard": matching_entity_fields[0]["standard"],
                "entity_fields": matching_entity_fields  # Store ALL matching entity fields
            }
            relevant_questions.append(relevant_question)
    
    print(f"   Output: {len(relevant_questions)} unique questions to ask")
    
    for i, rq in enumerate(relevant_questions):
        print(f"     {i+1}. '{rq['question']}' maps to {len(rq['entity_fields'])} entity fields:")
        for ef in rq['entity_fields']:
            print(f"        - {ef['entity_type']}: {ef['name']} (fieldId: {ef['field_id']})")
    
    # Verify: Should have 2 questions, not 3 (name question not duplicated)
    expected_count = 2
    actual_count = len(relevant_questions)
    
    if actual_count == expected_count:
        print(f"   ✅ SUCCESS: {actual_count} questions (no duplication)")
        return True
    else:
        print(f"   ❌ FAILED: Expected {expected_count} questions, got {actual_count}")
        return False

def test_answer_mapping():
    """Test that one answer maps to multiple entity fields"""
    print("\n🔍 Testing Answer Mapping to Multiple Entities...")
    
    # Mock current question with multiple entity fields
    current_question = {
        "id": "q1",
        "question": "What is your name?",
        "entity_fields": [
            {"entity_type": "LEAD", "name": "firstName"},
            {"entity_type": "CONTACT", "name": "contactName"}
        ]
    }
    
    user_answer = "John Doe"
    
    print(f"   Question: '{current_question['question']}'")
    print(f"   User Answer: '{user_answer}'")
    print(f"   Entity Fields: {len(current_question['entity_fields'])}")
    
    # Apply NEW logic: one answer creates multiple answer entries
    answers = []
    for entity_field in current_question["entity_fields"]:
        answers.append({
            "question_id": current_question["id"],
            "question": current_question["question"],
            "answer": user_answer,
            "field_name": entity_field["name"],
            "entity_type": entity_field["entity_type"]
        })
    
    print(f"   Generated {len(answers)} answer mappings:")
    for answer in answers:
        print(f"     - {answer['entity_type']}.{answer['field_name']} = '{answer['answer']}'")
    
    # Verify: Should have 2 answer mappings for 1 user response
    expected_count = 2
    actual_count = len(answers)
    
    if actual_count == expected_count:
        print(f"   ✅ SUCCESS: 1 answer mapped to {actual_count} entity fields")
        return True
    else:
        print(f"   ❌ FAILED: Expected {expected_count} mappings, got {actual_count}")
        return False

def test_conversation_flow():
    """Test the complete conversation flow"""
    print("\n🔍 Testing Complete Conversation Flow...")
    
    # Mock conversation state
    conversation_state = {
        "remaining_questions": [
            {
                "id": "q1",
                "question": "What is your name?",
                "entity_fields": [
                    {"entity_type": "LEAD", "name": "firstName"},
                    {"entity_type": "CONTACT", "name": "contactName"}
                ]
            },
            {
                "id": "q2",
                "question": "What is your email?",
                "entity_fields": [
                    {"entity_type": "LEAD", "name": "email"}
                ]
            }
        ],
        "asked_questions": [],
        "answers": []
    }
    
    print(f"   Starting with {len(conversation_state['remaining_questions'])} questions")
    
    # Simulate answering questions
    steps = []
    
    # Step 1: Ask first question
    if conversation_state["remaining_questions"]:
        current_q = conversation_state["remaining_questions"][0]
        steps.append(f"Ask: '{current_q['question']}'")
        
        # User answers
        user_answer = "John Doe"
        steps.append(f"User: '{user_answer}'")
        
        # Process answer (maps to multiple entities)
        for ef in current_q["entity_fields"]:
            conversation_state["answers"].append({
                "question_id": current_q["id"],
                "answer": user_answer,
                "field_name": ef["name"],
                "entity_type": ef["entity_type"]
            })
        
        # Move question from remaining to asked
        conversation_state["asked_questions"].append(conversation_state["remaining_questions"].pop(0))
        steps.append(f"Mapped answer to {len(current_q['entity_fields'])} entity fields")
    
    # Step 2: Ask second question
    if conversation_state["remaining_questions"]:
        current_q = conversation_state["remaining_questions"][0]
        steps.append(f"Ask: '{current_q['question']}'")
        
        # User answers
        user_answer = "<EMAIL>"
        steps.append(f"User: '{user_answer}'")
        
        # Process answer
        for ef in current_q["entity_fields"]:
            conversation_state["answers"].append({
                "question_id": current_q["id"],
                "answer": user_answer,
                "field_name": ef["name"],
                "entity_type": ef["entity_type"]
            })
        
        # Move question from remaining to asked
        conversation_state["asked_questions"].append(conversation_state["remaining_questions"].pop(0))
        steps.append(f"Mapped answer to {len(current_q['entity_fields'])} entity fields")
    
    # Step 3: All predefined questions completed
    if not conversation_state["remaining_questions"]:
        steps.append("All predefined questions completed")
        steps.append("Transition: 'Thank you! Feel free to ask any questions from our knowledge base.'")
        steps.append("Mode: Custom questions (knowledgebase only, no OpenAI)")
    
    print("   Conversation Flow:")
    for i, step in enumerate(steps, 1):
        print(f"     {i}. {step}")
    
    # Verify final state
    total_answers = len(conversation_state["answers"])
    asked_questions = len(conversation_state["asked_questions"])
    remaining_questions = len(conversation_state["remaining_questions"])
    
    print(f"\n   Final State:")
    print(f"     - Asked questions: {asked_questions}")
    print(f"     - Remaining questions: {remaining_questions}")
    print(f"     - Total answer mappings: {total_answers}")
    
    # Should have 0 remaining, 2 asked, 3 total answers (2 for name, 1 for email)
    if remaining_questions == 0 and asked_questions == 2 and total_answers == 3:
        print(f"   ✅ SUCCESS: Conversation flow completed correctly")
        return True
    else:
        print(f"   ❌ FAILED: Unexpected final state")
        return False

def test_knowledgebase_only_responses():
    """Test that custom questions use only knowledgebase (no OpenAI)"""
    print("\n🔍 Testing Knowledgebase-Only Responses...")
    
    # Mock knowledgebase search results
    mock_search_results = [
        {"content": "Our company offers 24/7 customer support through multiple channels.", "score": 0.95},
        {"content": "You can reach us via phone, email, or live chat on our website.", "score": 0.87},
        {"content": "Support hours are Monday to Friday 9 AM to 6 PM for phone support.", "score": 0.82}
    ]
    
    user_question = "What are your support hours?"
    
    print(f"   User Question: '{user_question}'")
    print(f"   Knowledgebase Results: {len(mock_search_results)} found")
    
    # Apply NEW logic: format knowledgebase results directly (no OpenAI)
    response_parts = []
    for i, result in enumerate(mock_search_results[:3], 1):
        response_parts.append(f"• {result['content'][:200]}...")
    
    kb_response = "Based on our knowledge base, here's what I found:\n\n" + "\n".join(response_parts)
    follow_up = "Is there anything else I can help you with?"
    complete_response = f"{kb_response}\n\n{follow_up}"
    
    print(f"   Response Method: Direct knowledgebase formatting (no AI)")
    print(f"   Response Length: {len(complete_response)} characters")
    print(f"   Contains AI Processing: {'No' if 'Based on our knowledge base' in complete_response else 'Yes'}")
    
    # Verify: Response should be direct knowledgebase content, no AI processing
    if "Based on our knowledge base" in complete_response and len(response_parts) == 3:
        print(f"   ✅ SUCCESS: Direct knowledgebase response (no OpenAI)")
        return True
    else:
        print(f"   ❌ FAILED: Response doesn't match expected format")
        return False

def main():
    """Run all tests for improved conversation flow"""
    print("🚀 Testing Improved Conversation Flow\n")
    
    tests = [
        ("Question Deduplication", test_question_deduplication),
        ("Answer Mapping to Multiple Entities", test_answer_mapping),
        ("Complete Conversation Flow", test_conversation_flow),
        ("Knowledgebase-Only Responses", test_knowledgebase_only_responses)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"Running: {test_name}")
            if test_func():
                passed += 1
                print("✅ PASSED\n")
            else:
                print("❌ FAILED\n")
        except Exception as e:
            print(f"❌ FAILED with exception: {str(e)}\n")
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Improved conversation flow is working correctly:")
        print("   ✅ Questions asked only once (no duplication)")
        print("   ✅ Answers map to multiple entities correctly")
        print("   ✅ Smooth transition to custom questions")
        print("   ✅ Knowledgebase-only responses (no OpenAI for custom questions)")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
