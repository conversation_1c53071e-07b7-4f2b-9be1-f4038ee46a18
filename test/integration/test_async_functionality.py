"""
Test to verify async functionality works correctly with pytest-asyncio
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, Mock


@pytest.mark.asyncio
async def test_basic_async_functionality():
    """Test that basic async functionality works"""
    
    async def async_function():
        await asyncio.sleep(0.01)  # Small delay
        return "async_result"
    
    result = await async_function()
    assert result == "async_result"


@pytest.mark.asyncio
async def test_async_mock_functionality():
    """Test that AsyncMock works correctly"""
    
    mock_service = AsyncMock()
    mock_service.async_method.return_value = "mocked_result"
    
    result = await mock_service.async_method()
    assert result == "mocked_result"
    mock_service.async_method.assert_called_once()


@pytest.mark.asyncio
async def test_async_exception_handling():
    """Test async exception handling"""
    
    async def failing_async_function():
        await asyncio.sleep(0.01)
        raise ValueError("Test error")
    
    with pytest.raises(ValueError, match="Test error"):
        await failing_async_function()


@pytest.mark.asyncio
async def test_multiple_async_operations():
    """Test multiple async operations"""
    
    async def async_operation(value):
        await asyncio.sleep(0.01)
        return value * 2
    
    # Test concurrent operations
    tasks = [async_operation(i) for i in range(3)]
    results = await asyncio.gather(*tasks)
    
    assert results == [0, 2, 4]


class TestAsyncClass:
    """Test class with async methods"""
    
    @pytest.mark.asyncio
    async def test_async_method_in_class(self):
        """Test async method within a test class"""
        
        async def class_async_method():
            await asyncio.sleep(0.01)
            return "class_async_result"
        
        result = await class_async_method()
        assert result == "class_async_result"
    
    @pytest.mark.asyncio
    async def test_async_context_manager(self):
        """Test async context manager"""
        
        class AsyncContextManager:
            async def __aenter__(self):
                await asyncio.sleep(0.01)
                return "context_value"
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                await asyncio.sleep(0.01)
        
        async with AsyncContextManager() as value:
            assert value == "context_value"


@pytest.mark.asyncio
async def test_async_generator():
    """Test async generator functionality"""
    
    async def async_generator():
        for i in range(3):
            await asyncio.sleep(0.01)
            yield i
    
    results = []
    async for value in async_generator():
        results.append(value)
    
    assert results == [0, 1, 2]


@pytest.mark.asyncio
async def test_async_with_fixtures(mock_database):
    """Test async function with pytest fixtures"""
    
    # Mock an async database operation
    async def async_db_operation():
        await asyncio.sleep(0.01)
        return mock_database.query.return_value
    
    # Configure mock
    mock_database.query.return_value = "db_result"
    
    result = await async_db_operation()
    assert result == "db_result"


def test_sync_function_still_works():
    """Test that synchronous functions still work normally"""
    
    def sync_function():
        return "sync_result"
    
    result = sync_function()
    assert result == "sync_result"


@pytest.mark.unit
@pytest.mark.asyncio
async def test_async_with_markers():
    """Test async function with multiple pytest markers"""
    
    async def marked_async_function():
        await asyncio.sleep(0.01)
        return "marked_result"
    
    result = await marked_async_function()
    assert result == "marked_result"
