import os
import logging
import pathlib
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv(verbose=True)

# Check AWS credentials
aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
aws_region = os.getenv("AWS_REGION", "us-east-1")
bucket_name = os.getenv("AWS_S3_BUCKET_NAME")

logger.info("AWS Environment Variables:")
logger.info(f"AWS_ACCESS_KEY_ID present: {aws_access_key_id is not None}")
logger.info(f"AWS_SECRET_ACCESS_KEY present: {aws_secret_access_key is not None}")
logger.info(f"AWS_REGION: {aws_region}")
logger.info(f"AWS_S3_BUCKET_NAME: {bucket_name}")

# Check if any required variables are missing
missing_vars = []
if not aws_access_key_id:
    missing_vars.append("AWS_ACCESS_KEY_ID")
if not aws_secret_access_key:
    missing_vars.append("AWS_SECRET_ACCESS_KEY")
if not bucket_name:
    missing_vars.append("AWS_S3_BUCKET_NAME")

if missing_vars:
    logger.info(f"Missing required environment variables: {', '.join(missing_vars)}")
else:
    logger.info("All required AWS environment variables are set.")

# Print the .env file path
env_path = pathlib.Path('.') / '.env'
logger.info(f".env file path: {env_path.absolute()}")
logger.info(f".env file exists: {env_path.exists()}")

if env_path.exists():
    logger.info("Contents of .env file (without showing secret values):")
    with open(env_path, 'r') as f:
        for line in f:
            if line.strip() and not line.strip().startswith('#'):
                key = line.split('=')[0].strip()
                logger.info(f"{key}: {'[HIDDEN]' if 'SECRET' in key or 'KEY' in key else line.split('=')[1].strip()}")