"""merge_question_length_branch

Revision ID: l2m3n4o5p6q7
Revises: j1k2l3m4n5o6, k1l2m3n4o5p6
Create Date: 2025-01-31 15:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'l2m3n4o5p6q7'
down_revision = ('j1k2l3m4n5o6', 'k1l2m3n4o5p6')
branch_labels = None
depends_on = None


def upgrade() -> None:
    # This is a merge migration - no schema changes needed
    # The actual schema changes are already applied by the individual migrations
    pass


def downgrade() -> None:
    # This is a merge migration - no schema changes needed
    pass 