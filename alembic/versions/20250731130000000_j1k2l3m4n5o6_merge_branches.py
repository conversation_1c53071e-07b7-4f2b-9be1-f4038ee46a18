"""merge_branches

Revision ID: j1k2l3m4n5o6
Revises: h9i0j1k2l3m4, i0j1k2l3m4n5
Create Date: 2025-07-31 13:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'j1k2l3m4n5o6'
down_revision = ('h9i0j1k2l3m4', 'i0j1k2l3m4n5')
branch_labels = None
depends_on = None


def upgrade() -> None:
    # This is a merge migration - no schema changes needed
    pass


def downgrade() -> None:
    # This is a merge migration - no schema changes needed
    pass
