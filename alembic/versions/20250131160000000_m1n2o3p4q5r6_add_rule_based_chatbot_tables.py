"""Add rule-based chatbot tables with proper node_id constraints

Revision ID: m1n2o3p4q5r6
Revises: l2m3n4o5p6q7
Create Date: 2025-07-31 16:00:00.000000

Creates tables for rule-based chatbots with:
- node_id unique per chatbot (not globally)
- 255 character limit on node_id columns
- Proper foreign key constraints

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'm1n2o3p4q5r6'
down_revision = 'l2m3n4o5p6q7'
branch_labels = None
depends_on = None


def upgrade():
    # Create chatbot_nodes table
    op.create_table('chatbot_nodes',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('chatbot_id', sa.String(), nullable=False),
        sa.Column('tenant_id', sa.BigInteger(), nullable=False),
        sa.Column('node_id', sa.String(255), nullable=False),  # Added 255 character limit
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('type', sa.String(), nullable=False),
        sa.Column('position_x', sa.Integer(), nullable=False),
        sa.Column('position_y', sa.Integer(), nullable=False),
        sa.Column('data', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('variable_mapping', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['chatbot_id'], ['chatbots.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chatbot_nodes_chatbot_id'), 'chatbot_nodes', ['chatbot_id'], unique=False)
    op.create_index(op.f('ix_chatbot_nodes_tenant_id'), 'chatbot_nodes', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_chatbot_nodes_node_id'), 'chatbot_nodes', ['node_id'], unique=False)  # Added index for performance
    # Add composite unique constraint: node_id should be unique per chatbot (not globally)
    op.create_unique_constraint('uq_chatbot_node_id', 'chatbot_nodes', ['chatbot_id', 'node_id'])

    # Create chatbot_node_entity_fields table
    op.create_table('chatbot_node_entity_fields',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('node_id', sa.String(255), nullable=False),  # This references the node_id field for logical relationship
        sa.Column('entity_type', sa.String(), nullable=False),
        sa.Column('field_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('display_name', sa.String(), nullable=False),
        sa.Column('standard', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        # Note: No foreign key constraint here since node_id is not unique alone
        # The relationship is maintained at the application level
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chatbot_node_entity_fields_node_id'), 'chatbot_node_entity_fields', ['node_id'], unique=False)

    # Create chatbot_edges table
    op.create_table('chatbot_edges',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('chatbot_id', sa.String(), nullable=False),
        sa.Column('tenant_id', sa.BigInteger(), nullable=False),
        sa.Column('edge_id', sa.String(), nullable=False),
        sa.Column('source_node', sa.String(255), nullable=False),  # References node_id for logical relationship
        sa.Column('source_handle', sa.String(), nullable=True),
        sa.Column('target_node', sa.String(255), nullable=False),  # References node_id for logical relationship
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['chatbot_id'], ['chatbots.id'], ),
        # Note: No foreign key constraints for source_node and target_node since node_id is not unique alone
        # The relationships are maintained at the application level
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chatbot_edges_chatbot_id'), 'chatbot_edges', ['chatbot_id'], unique=False)
    op.create_index(op.f('ix_chatbot_edges_tenant_id'), 'chatbot_edges', ['tenant_id'], unique=False)


def downgrade():
    # Drop tables in reverse order
    op.drop_index(op.f('ix_chatbot_edges_tenant_id'), table_name='chatbot_edges')
    op.drop_index(op.f('ix_chatbot_edges_chatbot_id'), table_name='chatbot_edges')
    op.drop_table('chatbot_edges')
    
    op.drop_index(op.f('ix_chatbot_node_entity_fields_node_id'), table_name='chatbot_node_entity_fields')
    op.drop_table('chatbot_node_entity_fields')
    
    # Drop unique constraint first (updated constraint name)
    op.drop_constraint('uq_chatbot_node_id', 'chatbot_nodes', type_='unique')
    op.drop_index(op.f('ix_chatbot_nodes_node_id'), table_name='chatbot_nodes')  # Added index drop
    op.drop_index(op.f('ix_chatbot_nodes_tenant_id'), table_name='chatbot_nodes')
    op.drop_index(op.f('ix_chatbot_nodes_chatbot_id'), table_name='chatbot_nodes')
    op.drop_table('chatbot_nodes') 