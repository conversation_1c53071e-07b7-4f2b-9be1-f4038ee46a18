"""add_document_details_to_knowledgebase

Revision ID: h9i0j1k2l3m4
Revises: g8h9i0j1k2l3
Create Date: 2025-01-30 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'h9i0j1k2l3m4'
down_revision = 'g8h9i0j1k2l3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add document metadata fields to chatbot_knowledgebases table"""
    
    # Add new columns to chatbot_knowledgebases table
    op.add_column('chatbot_knowledgebases', sa.Column('document_name', sa.String(), nullable=True))
    op.add_column('chatbot_knowledgebases', sa.Column('document_size', sa.BigInteger(), nullable=True))
    op.add_column('chatbot_knowledgebases', sa.Column('document_type', sa.String(), nullable=True))


def downgrade() -> None:
    """Remove document metadata fields from chatbot_knowledgebases table"""
    
    # Remove the added columns
    op.drop_column('chatbot_knowledgebases', 'document_type')
    op.drop_column('chatbot_knowledgebases', 'document_size')
    op.drop_column('chatbot_knowledgebases', 'document_name')
