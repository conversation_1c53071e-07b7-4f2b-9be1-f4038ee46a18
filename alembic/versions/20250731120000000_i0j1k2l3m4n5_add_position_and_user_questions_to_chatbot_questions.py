"""add_position_and_user_questions_to_chatbot_questions

Revision ID: i0j1k2l3m4n5
Revises: g8h9i0j1k2l3
Create Date: 2025-07-31 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'i0j1k2l3m4n5'
down_revision = 'g8h9i0j1k2l3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add position and user_questions fields to chatbot_questions table"""
    
    # Add position column with default value of 1
    op.add_column('chatbot_questions', 
                  sa.Column('position', sa.Integer(), nullable=False, server_default='1'))
    
    # Add user_questions column for storing JSON array of user questions
    op.add_column('chatbot_questions', 
                  sa.Column('user_questions', sa.Text(), nullable=True))
    
    # Update existing questions to have sequential positions
    # This ensures existing questions get proper position values
    connection = op.get_bind()
    
    # Get all existing questions grouped by chatbot_id and tenant_id
    result = connection.execute(sa.text("""
        SELECT id, chatbot_id, tenant_id, 
               ROW_NUMBER() OVER (PARTITION BY chatbot_id, tenant_id ORDER BY created_at) as row_num
        FROM chatbot_questions 
        ORDER BY chatbot_id, tenant_id, created_at
    """))
    
    # Update each question with its sequential position
    for row in result:
        connection.execute(sa.text("""
            UPDATE chatbot_questions 
            SET position = :position 
            WHERE id = :question_id
        """), {
            'position': row.row_num,
            'question_id': row.id
        })
    
    # Remove the server default after updating existing data
    op.alter_column('chatbot_questions', 'position', server_default=None)


def downgrade() -> None:
    """Remove position and user_questions fields from chatbot_questions table"""
    
    # Drop the added columns
    op.drop_column('chatbot_questions', 'user_questions')
    op.drop_column('chatbot_questions', 'position')
