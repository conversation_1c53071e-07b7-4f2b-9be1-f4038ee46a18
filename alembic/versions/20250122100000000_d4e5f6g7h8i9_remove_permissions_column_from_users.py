"""Remove permissions column from users table

Revision ID: d4e5f6g7h8i9
Revises: c3d4e5f6g7h8
Create Date: 2025-01-22 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'd4e5f6g7h8i9'
down_revision: Union[str, None] = 'c3d4e5f6g7h8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Remove permissions column from users table
    
    The permissions column is being removed as permissions are now handled
    entirely through JWT tokens from the IAM system.
    """
    # Check if the column exists before trying to drop it
    # This prevents errors if the migration is run multiple times
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    columns = [col['name'] for col in inspector.get_columns('users')]
    
    if 'permissions' in columns:
        op.drop_column('users', 'permissions')


def downgrade() -> None:
    """Add back permissions column to users table
    
    This restores the permissions column as a JSON field for rollback purposes.
    Note: The data will be empty and would need to be repopulated from IAM.
    """
    # Add back the permissions column as JSON
    op.add_column('users', sa.Column('permissions', postgresql.JSON(astext_type=sa.Text()), nullable=True))
