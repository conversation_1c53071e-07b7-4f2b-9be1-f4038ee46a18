"""add_entities_to_chatbots

Revision ID: p0q1r2s3t4u5
Revises: o9p0q1r2s3t4
Create Date: 2025-10-11 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'p0q1r2s3t4u5'
down_revision = 'o9p0q1r2s3t4'  # Latest migration
branch_labels = None
depends_on = None


def upgrade():
    """Add entities column to chatbots table"""
    # Add entities column as JSON type
    op.add_column('chatbots', sa.Column('entities', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    
    print("✅ Added entities column to chatbots table")


def downgrade():
    """Remove entities column from chatbots table"""
    op.drop_column('chatbots', 'entities')
    
    print("✅ Removed entities column from chatbots table")

