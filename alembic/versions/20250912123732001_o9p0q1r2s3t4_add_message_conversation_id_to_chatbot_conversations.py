"""Add messageConversationId to chatbot_conversations

Revision ID: o9p0q1r2s3t4
Revises: o8p9q0r1s2t3
Create Date: 2025-09-12 12:37:32.000001

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'o9p0q1r2s3t4'
down_revision = 'o8p9q0r1s2t3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add messageConversationId column to chatbot_conversations table
    op.add_column('chatbot_conversations', sa.Column('message_conversation_id', sa.Integer(), nullable=True))
    
    # Create index for better query performance
    op.create_index('ix_chatbot_conversations_message_conversation_id', 'chatbot_conversations', ['message_conversation_id'])
    
    # Create composite index for tenant_id, message_conversation_id, and completed for efficient querying
    op.create_index('ix_chatbot_conversations_tenant_message_completed', 'chatbot_conversations', 
                   ['tenant_id', 'message_conversation_id', 'completed'])


def downgrade() -> None:
    # Drop the composite index
    op.drop_index('ix_chatbot_conversations_tenant_message_completed', table_name='chatbot_conversations')
    
    # Drop the message_conversation_id index
    op.drop_index('ix_chatbot_conversations_message_conversation_id', table_name='chatbot_conversations')
    
    # Drop the message_conversation_id column
    op.drop_column('chatbot_conversations', 'message_conversation_id')
