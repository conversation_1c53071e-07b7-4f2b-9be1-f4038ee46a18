"""update position columns to float

Revision ID: n7o8p9q0r1s2
Revises: m1n2o3p4q5r6
Create Date: 2025-09-12 12:37:32.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'n7o8p9q0r1s2'
down_revision = 'm1n2o3p4q5r6'
branch_labels = None
depends_on = None


def upgrade():
    # Alter position_x and position_y columns from INTEGER to FLOAT
    # This allows storing decimal values like 100.12, 221.67
    op.alter_column('chatbot_nodes', 'position_x',
                    existing_type=sa.Integer(),
                    type_=sa.Float(),
                    existing_nullable=False)
    
    op.alter_column('chatbot_nodes', 'position_y',
                    existing_type=sa.Integer(),
                    type_=sa.Float(),
                    existing_nullable=False)


def downgrade():
    # Revert position_x and position_y columns back to INTEGER
    # Note: This will truncate any decimal values to integers
    op.alter_column('chatbot_nodes', 'position_x',
                    existing_type=sa.Float(),
                    type_=sa.Integer(),
                    existing_nullable=False)
    
    op.alter_column('chatbot_nodes', 'position_y',
                    existing_type=sa.Float(),
                    type_=sa.Integer(),
                    existing_nullable=False)
