"""populate_default_entities_for_existing_chatbots

Revision ID: q1r2s3t4u5v6
Revises: p0q1r2s3t4u5
Create Date: 2025-10-11 14:00:00.000000

This migration populates the entities field for existing chatbots
with default values [LEAD, CONTACT] if entities is NULL or empty.
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision = 'q1r2s3t4u5v6'
down_revision = 'p0q1r2s3t4u5'
branch_labels = None
depends_on = None


def upgrade():
    """
    Update existing chatbots with default entities [LEAD, CONTACT]
    if entities is NULL or empty array
    """
    # Get database connection
    connection = op.get_bind()
    
    # Define default entities
    default_entities = [
        {"entity": "LEAD"},
        {"entity": "CONTACT"}
    ]
    
    # Convert to JSON string for SQL
    import json
    default_entities_json = json.dumps(default_entities)
    
    # Update chatbots where entities is NULL
    # Note: entities column is JSON type, so we cast the parameter to json
    result_null = connection.execute(
        text("""
            UPDATE chatbots 
            SET entities = CAST(:default_entities AS json)
            WHERE entities IS NULL
        """),
        {"default_entities": default_entities_json}
    )
    
    # Update chatbots where entities is empty array []
    # Cast both sides to text for comparison since json/jsonb comparison isn't direct
    result_empty = connection.execute(
        text("""
            UPDATE chatbots 
            SET entities = CAST(:default_entities AS json)
            WHERE CAST(entities AS text) = '[]'
        """),
        {"default_entities": default_entities_json}
    )
    
    # Get total count
    result = connection.execute(
        text("SELECT COUNT(*) FROM chatbots WHERE entities IS NOT NULL")
    )
    count = result.scalar()
    
    print(f"✅ Populated default entities [LEAD, CONTACT]:")
    print(f"   - Updated {result_null.rowcount if hasattr(result_null, 'rowcount') else 'N/A'} chatbots with NULL entities")
    print(f"   - Updated {result_empty.rowcount if hasattr(result_empty, 'rowcount') else 'N/A'} chatbots with empty array entities")
    print(f"   - Total chatbots with entities: {count}")


def downgrade():
    """
    Revert entities back to NULL for chatbots that have default entities
    """
    # Get database connection
    connection = op.get_bind()
    
    # Define default entities (for comparison)
    default_entities = [
        {"entity": "LEAD"},
        {"entity": "CONTACT"}
    ]
    
    import json
    default_entities_json = json.dumps(default_entities)
    
    # Revert chatbots that have the default entities back to NULL
    # Cast both to text for comparison since json comparison isn't direct
    result = connection.execute(
        text("""
            UPDATE chatbots 
            SET entities = NULL
            WHERE CAST(entities AS text) = :default_entities
        """),
        {"default_entities": default_entities_json}
    )
    
    print(f"✅ Reverted {result.rowcount if hasattr(result, 'rowcount') else 'N/A'} chatbots with default entities back to NULL")

