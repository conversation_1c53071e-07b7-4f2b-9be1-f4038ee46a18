"""Add entity details to chatbot_conversations table

Revision ID: e5f6g7h8i9j0
Revises: d4e5f6g7h8i9
Create Date: 2025-01-22 11:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e5f6g7h8i9j0'
down_revision: Union[str, None] = 'd4e5f6g7h8i9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add entity details columns to chatbot_conversations table
    
    These columns will store information about which entities are involved
    in each conversation for better tracking and auditing.
    """
    # Add entity_details column to store array of entity information
    op.add_column('chatbot_conversations', 
                  sa.Column('entity_details', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    
    # Add connected_account_id for easier querying and indexing
    op.add_column('chatbot_conversations', 
                  sa.Column('connected_account_id', sa.Integer(), nullable=True))
    
    # Add connected_account_name for display purposes
    op.add_column('chatbot_conversations', 
                  sa.Column('connected_account_name', sa.String(), nullable=True))
    
    # Add entity_update_status to track which entities were successfully updated
    op.add_column('chatbot_conversations', 
                  sa.Column('entity_update_status', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    
    # Create index on connected_account_id for efficient querying
    op.create_index('ix_chatbot_conversations_connected_account_id', 
                    'chatbot_conversations', ['connected_account_id'])


def downgrade() -> None:
    """Remove entity details columns from chatbot_conversations table"""
    # Drop the index first
    op.drop_index('ix_chatbot_conversations_connected_account_id', 
                  table_name='chatbot_conversations')
    
    # Drop the columns
    op.drop_column('chatbot_conversations', 'entity_update_status')
    op.drop_column('chatbot_conversations', 'connected_account_name')
    op.drop_column('chatbot_conversations', 'connected_account_id')
    op.drop_column('chatbot_conversations', 'entity_details')
