"""add is_first_node to chatbot_nodes

Revision ID: o8p9q0r1s2t3
Revises: n7o8p9q0r1s2
Create Date: 2025-01-15 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = 'o8p9q0r1s2t3'
down_revision = 'n7o8p9q0r1s2'
branch_labels = None
depends_on = None


def upgrade():
    """Add is_first_node column to chatbot_nodes table"""
    # Add the new column as nullable first
    op.add_column('chatbot_nodes', sa.Column('is_first_node', sa.<PERSON>(), nullable=True))
    
    # Populate the is_first_node field for existing nodes
    # A node is a first node if it's not a target of any edge
    connection = op.get_bind()
    
    # Update is_first_node to True for nodes that have no incoming edges
    connection.execute(text("""
        UPDATE chatbot_nodes 
        SET is_first_node = TRUE 
        WHERE node_id NOT IN (
            SELECT DISTINCT target_node 
            FROM chatbot_edges 
            WHERE chatbot_edges.chatbot_id = chatbot_nodes.chatbot_id 
            AND target_node IS NOT NULL
        )
    """))
    
    # Set default value for any remaining NULL values
    connection.execute(text("""
        UPDATE chatbot_nodes 
        SET is_first_node = FALSE 
        WHERE is_first_node IS NULL
    """))
    
    # Now make the column NOT NULL
    op.alter_column('chatbot_nodes', 'is_first_node', nullable=False)
    
    # Create index on the new column for performance
    op.create_index('ix_chatbot_nodes_is_first_node', 'chatbot_nodes', ['is_first_node'])
    
    print("✅ Successfully added is_first_node column and populated existing data")


def downgrade():
    """Remove is_first_node column from chatbot_nodes table"""
    # Drop the index first
    op.drop_index('ix_chatbot_nodes_is_first_node', table_name='chatbot_nodes')
    
    # Drop the column
    op.drop_column('chatbot_nodes', 'is_first_node')
    
    print("✅ Successfully removed is_first_node column")
