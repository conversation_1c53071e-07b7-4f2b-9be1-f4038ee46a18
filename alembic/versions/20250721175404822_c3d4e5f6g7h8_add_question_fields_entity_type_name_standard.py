"""Add question fields entity_type, name, standard

Revision ID: c3d4e5f6g7h8
Revises: b2c3d4e5f6g7
Create Date: 2025-07-21 17:54:04.822000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c3d4e5f6g7h8'
down_revision: Union[str, None] = 'b2c3d4e5f6g7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add entity_type, name, and standard fields to chatbot_questions table"""
    
    # Add new columns to chatbot_questions table
    op.add_column('chatbot_questions', sa.Column('entity_type', sa.String(), nullable=False, server_default=''))
    op.add_column('chatbot_questions', sa.Column('name', sa.String(), nullable=False, server_default=''))
    op.add_column('chatbot_questions', sa.Column('standard', sa.<PERSON>(), nullable=False, server_default=sa.text('false')))
    
    # Remove server defaults after adding columns (they were only needed for existing rows)
    op.alter_column('chatbot_questions', 'entity_type', server_default=None)
    op.alter_column('chatbot_questions', 'name', server_default=None)
    op.alter_column('chatbot_questions', 'standard', server_default=None)


def downgrade() -> None:
    """Remove entity_type, name, and standard fields from chatbot_questions table"""
    
    # Remove the added columns
    op.drop_column('chatbot_questions', 'standard')
    op.drop_column('chatbot_questions', 'name')
    op.drop_column('chatbot_questions', 'entity_type')
