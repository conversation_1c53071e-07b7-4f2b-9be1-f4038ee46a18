"""Increase question field length in chatbot_questions table

Revision ID: k1l2m3n4o5p6
Revises: i0j1k2l3m4n5
Create Date: 2025-01-31 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'k1l2m3n4o5p6'
down_revision = 'i0j1k2l3m4n5'
branch_labels = None
depends_on = None


def upgrade():
    """Increase question field length from 100 to 255 characters"""
    # Increase the question field length from 100 to 255 characters
    op.alter_column('chatbot_questions', 'question',
                    existing_type=sa.String(length=100),
                    type_=sa.String(length=255),
                    existing_nullable=False)


def downgrade():
    """Revert question field length back to 100 characters"""
    # Revert the question field length back to 100 characters
    op.alter_column('chatbot_questions', 'question',
                    existing_type=sa.String(length=255),
                    type_=sa.String(length=100),
                    existing_nullable=False) 