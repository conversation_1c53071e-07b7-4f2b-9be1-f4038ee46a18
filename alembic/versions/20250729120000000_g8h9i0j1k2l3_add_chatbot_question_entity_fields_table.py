"""add_chatbot_question_entity_fields_table

Revision ID: g8h9i0j1k2l3
Revises: f6g7h8i9j0k1
Create Date: 2025-07-29 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'g8h9i0j1k2l3'
down_revision = 'f6g7h8i9j0k1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add chatbot_question_entity_fields table and migrate existing data"""
    
    # Create the new chatbot_question_entity_fields table
    op.create_table('chatbot_question_entity_fields',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('question_id', sa.String(), nullable=False),
        sa.Column('entity_type', sa.String(), nullable=False),
        sa.Column('field_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('display_name', sa.String(), nullable=False),
        sa.Column('standard', sa.<PERSON>(), nullable=False, server_default=sa.text('false')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add indexes
    op.create_index(op.f('ix_chatbot_question_entity_fields_question_id'), 'chatbot_question_entity_fields', ['question_id'], unique=False)
    
    # Add foreign key constraint
    op.create_foreign_key(None, 'chatbot_question_entity_fields', 'chatbot_questions', ['question_id'], ['id'])
    
    # Migrate existing data from chatbot_questions to chatbot_question_entity_fields
    # This will create entity field records for each existing question
    op.execute("""
        INSERT INTO chatbot_question_entity_fields (id, question_id, entity_type, field_id, name, display_name, standard, created_at, updated_at)
        SELECT 
            gen_random_uuid()::text as id,
            id as question_id,
            entity_type,
            field_id,
            name,
            display_name,
            standard,
            created_at,
            updated_at
        FROM chatbot_questions
        WHERE entity_type IS NOT NULL AND field_id IS NOT NULL AND name IS NOT NULL
    """)
    
    # Remove the old columns from chatbot_questions table
    op.drop_column('chatbot_questions', 'entity_type')
    op.drop_column('chatbot_questions', 'field_id')
    op.drop_column('chatbot_questions', 'name')
    op.drop_column('chatbot_questions', 'display_name')
    op.drop_column('chatbot_questions', 'standard')


def downgrade() -> None:
    """Revert the changes - add back old columns and migrate data"""
    
    # Add back the old columns to chatbot_questions
    op.add_column('chatbot_questions', sa.Column('entity_type', sa.String(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('field_id', sa.Integer(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('name', sa.String(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('display_name', sa.String(), nullable=True))
    op.add_column('chatbot_questions', sa.Column('standard', sa.Boolean(), nullable=True))
    
    # Migrate data back from chatbot_question_entity_fields to chatbot_questions
    # Note: This will only migrate the first entity field mapping per question
    op.execute("""
        UPDATE chatbot_questions 
        SET 
            entity_type = ef.entity_type,
            field_id = ef.field_id,
            name = ef.name,
            display_name = ef.display_name,
            standard = ef.standard
        FROM (
            SELECT DISTINCT ON (question_id) 
                question_id, entity_type, field_id, name, display_name, standard
            FROM chatbot_question_entity_fields
            ORDER BY question_id, created_at
        ) ef
        WHERE chatbot_questions.id = ef.question_id
    """)
    
    # Make the columns non-nullable after migration
    op.alter_column('chatbot_questions', 'entity_type', nullable=False)
    op.alter_column('chatbot_questions', 'field_id', nullable=False)
    op.alter_column('chatbot_questions', 'name', nullable=False)
    op.alter_column('chatbot_questions', 'display_name', nullable=False)
    op.alter_column('chatbot_questions', 'standard', nullable=False)
    
    # Drop the new table
    op.drop_table('chatbot_question_entity_fields')
