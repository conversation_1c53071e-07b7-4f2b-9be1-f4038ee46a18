"""Remove entity_type column from chatbots table (keep trigger)

Revision ID: f6g7h8i9j0k1
Revises: e5f6g7h8i9j0
Create Date: 2025-01-22 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'f6g7h8i9j0k1'
down_revision: Union[str, None] = 'e5f6g7h8i9j0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Remove entity_type column from chatbots table

    The entity_type column is no longer needed as we've moved to an account-based
    chatbot model where one chatbot serves all entities for a connected account.
    The trigger column is kept as it's still useful for chatbot behavior.
    """
    # Check if the column exists before trying to drop it
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    columns = [col['name'] for col in inspector.get_columns('chatbots')]

    if 'entity_type' in columns:
        op.drop_column('chatbots', 'entity_type')


def downgrade() -> None:
    """Add back entity_type column to chatbots table

    This restores the column for rollback purposes.
    Note: The data will be empty and would need to be repopulated.
    """
    # Add back the entity_type column
    op.add_column('chatbots', sa.Column('entity_type', sa.String(), nullable=True))
