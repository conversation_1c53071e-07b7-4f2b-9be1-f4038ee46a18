#!/usr/bin/env python3
"""
Server startup script for the Knowledge Base Chatbot API

This script uses the factory=True approach with uvicorn to prevent duplicate
RabbitMQ consumers when running multiple workers. Each worker gets its own
app instance, ensuring consumers are not duplicated across worker processes.

Key Benefits:
- Multiple workers for HTTP performance
- No duplicate RabbitMQ consumers
- Proper resource isolation per worker
- Environment-based configuration
"""

# Standard library imports
import os
import logging

# Third-party imports
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration constants
DEFAULT_WORKERS = 1
DEFAULT_HOST = "0.0.0.0"
DEFAULT_PORT = 8000
DEFAULT_LOG_LEVEL = "info"

def create_app():
    """Create and configure the FastAPI app."""
    from app.main import app
    return app

def main():
    """Start the FastAPI server"""
    
    # Get configuration from environment
    host = os.getenv("HOST", DEFAULT_HOST)
    port = int(os.getenv("PORT", DEFAULT_PORT))
    log_level = os.getenv("LOG_LEVEL", DEFAULT_LOG_LEVEL).lower()
    reload = os.getenv("RELOAD", "false").lower() == "true"
    workers = int(os.getenv("WORKERS", DEFAULT_WORKERS))
    
    logger.info(f"Starting server on {host}:{port}")
    logger.info(f"Log level: {log_level}")
    logger.info(f"Reload: {reload}")
    logger.info(f"Workers: {workers}")
    logger.info("Using factory=True to prevent duplicate RabbitMQ consumers across workers")
    
    # Start the server with factory=True to prevent duplicate consumers
    uvicorn.run(
        "start_server:create_app",
        host=host,
        port=port,
        workers=workers,
        factory=True,
        log_level=log_level,
        reload=reload,
        access_log=True
    )

if __name__ == "__main__":
    main()