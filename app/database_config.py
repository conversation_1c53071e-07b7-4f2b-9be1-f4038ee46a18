"""
Database configuration for Digital Ocean deployment
Optimized connection pooling settings for production environments

IMPORTANT: This configuration is optimized for Digital Ocean databases with 
limited connection capacity (10 total connections). The pool size is set to 8
to leave 2 connections free for system operations and admin access.

Key Settings:
- pool_size: 8 (leaves 2 connections free)
- max_overflow: 0 (no overflow - respect 10-connection limit)
- pool_recycle: 1800 seconds (30 minutes - more frequent for limited pool)
- pool_timeout: 15 seconds (reduced for limited pool)
- high_utilization: 70% (alert at 7+ connections)
- critical_utilization: 90% (critical at 9+ connections)

For optimal performance with limited connections:
1. Keep database operations short
2. Return connections to pool quickly
3. Monitor pool utilization closely
4. Use background tasks for long operations
"""

import os
from typing import Dict, Any

# Digital Ocean specific database configuration
DIGITAL_OCEAN_DB_CONFIG = {
    "pool_size": 8,  # Base pool size (leaves 2 connections free for system use)
    "max_overflow": 0,  # No overflow - total capacity is only 10 connections
    "pool_pre_ping": True,  # Validate connections before use
    "pool_recycle": 1800,  # Recycle connections every 30 minutes (more frequent for limited pool)
    "pool_timeout": 60,  # Wait up to 60 seconds for a connection (as set by user)
    "echo": False,  # Disable SQL logging in production
    "echo_pool": False,  # Disable pool logging in production
}

# Connection arguments optimized for Digital Ocean
DIGITAL_OCEAN_CONNECT_ARGS = {
    "connect_timeout": 10,  # Connection timeout in seconds
}

# Environment-specific overrides
def get_db_config() -> Dict[str, Any]:
    """Get database configuration based on environment"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        # Production settings for Digital Ocean
        return {
            **DIGITAL_OCEAN_DB_CONFIG,
            "pool_size": int(os.getenv("DB_POOL_SIZE", "8")),  # 8 connections (leaves 2 free)
            "max_overflow": int(os.getenv("DB_MAX_OVERFLOW", "0")),  # No overflow for DO 10-connection limit
            "pool_recycle": int(os.getenv("DB_POOL_RECYCLE", "1800")),  # 30 minutes for limited pool
            "pool_timeout": int(os.getenv("DB_POOL_TIMEOUT", "60")),  # 60 seconds as set by user
        }
    elif env == "staging":
        # Staging settings
        return {
            **DIGITAL_OCEAN_DB_CONFIG,
            "pool_size": int(os.getenv("DB_POOL_SIZE", "5")),
            "max_overflow": int(os.getenv("DB_MAX_OVERFLOW", "10")),
            "echo": os.getenv("DB_ECHO", "false").lower() == "true",
        }
    else:
        # Development settings
        return {
            **DIGITAL_OCEAN_DB_CONFIG,
            "pool_size": int(os.getenv("DB_POOL_SIZE", "5")),
            "max_overflow": int(os.getenv("DB_MAX_OVERFLOW", "10")),
            "echo": os.getenv("DB_ECHO", "false").lower() == "true",
            "echo_pool": os.getenv("DB_ECHO_POOL", "false").lower() == "true",
        }

def get_connect_args(pool_name: str = "whatsapp_chatbot") -> Dict[str, Any]:
    """Get connection arguments based on environment"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    base_args = {
        **DIGITAL_OCEAN_CONNECT_ARGS,
        "application_name": f"whatsapp_chatbot_{pool_name}",  # Dynamic application name
    }
    
    if env == "production":
        # Production settings for Digital Ocean
        return {
            **base_args,
            "connect_timeout": int(os.getenv("DB_CONNECT_TIMEOUT", "10")),
        }
    else:
        # Development/Staging settings
        return base_args

# Pool monitoring thresholds
POOL_MONITORING_THRESHOLDS = {
    "high_utilization": 70,  # Alert when pool utilization > 70% (7+ connections) for limited pool
    "connection_timeout": 3,  # Alert when waiting > 3 seconds for connection (reduced for limited pool)
    "invalid_connections": 2,  # Alert when > 2 invalid connections (reduced for limited pool)
}

# Limited connection pool specific settings
LIMITED_POOL_CONFIG = {
    "max_connections": 10,  # Total database connection limit
    "recommended_pool_size": 8,  # Recommended to leave 2 connections free
    "critical_utilization": 90,  # Critical alert at 90% (9+ connections)
    "connection_wait_warning": 2,  # Warning if waiting > 2 seconds
}

def get_limited_pool_config() -> Dict[str, Any]:
    """Get configuration specifically for limited connection pools"""
    return {
        **LIMITED_POOL_CONFIG,
        "max_connections": int(os.getenv("DB_MAX_CONNECTIONS", "10")),
        "recommended_pool_size": int(os.getenv("DB_RECOMMENDED_POOL_SIZE", "8")),
        "critical_utilization": int(os.getenv("POOL_CRITICAL_UTILIZATION", "90")),
        "connection_wait_warning": int(os.getenv("POOL_CONNECTION_WAIT_WARNING", "2")),
    }

def get_optimized_db_config_for_limited_pool() -> Dict[str, Any]:
    """Get optimized database configuration for limited connection pools (10 total connections)"""
    return {
        "pool_size": 8,  # Leave 2 connections free for system/admin use
        "max_overflow": 0,  # No overflow - respect the 10-connection limit
        "pool_pre_ping": True,  # Validate connections before use
        "pool_recycle": 1800,  # Recycle every 30 minutes (more frequent for limited pool)
        "pool_timeout": 10,  # Wait up to 10 seconds for connection
        "echo": False,  # Disable SQL logging in production
        "echo_pool": False,  # Disable pool logging in production
    }

def get_pool_monitoring_config() -> Dict[str, Any]:
    """Get pool monitoring configuration"""
    return {
        **POOL_MONITORING_THRESHOLDS,
        "high_utilization": int(os.getenv("POOL_HIGH_UTILIZATION_THRESHOLD", "80")),
        "connection_timeout": int(os.getenv("POOL_CONNECTION_TIMEOUT_THRESHOLD", "5")),
        "invalid_connections": int(os.getenv("POOL_INVALID_CONNECTIONS_THRESHOLD", "3")),
    } 