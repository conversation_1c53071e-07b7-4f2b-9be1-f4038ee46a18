"""
Advanced connection pooling configuration for PostgreSQL with Digital Ocean optimization
"""
import os
import time
import threading
from contextlib import contextmanager
from typing import Optional, Dict, Any
from sqlalchemy import create_engine, event, text
from sqlalchemy.engine import Engine
from sqlalchemy.pool import QueuePool, NullPool
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.exc import OperationalError, DisconnectionError
from dotenv import load_dotenv
import logging
import psycopg2
from psycopg2.pool import SimpleConnectionPool, ThreadedConnectionPool
from psycopg2.extras import RealDictCursor

# Load environment variables from .env file
load_dotenv()

# Import database configuration
from .database_config import get_db_config, get_connect_args

# Get logger
logger = logging.getLogger(__name__)

class ConnectionPoolManager:
    """Manages database connection pools with advanced features for Digital Ocean"""
    
    def __init__(self):
        self._pools: Dict[str, Any] = {}
        self._lock = threading.Lock()
        self._stats = {
            'total_connections': 0,
            'active_connections': 0,
            'idle_connections': 0,
            'failed_connections': 0,
            'pool_utilization': 0.0
        }
    
    def get_sqlalchemy_pool(self, database_url: str, pool_name: str = "whatsapp_chatbot") -> Engine:
        """Get or create SQLAlchemy engine with optimized connection pool"""
        if pool_name not in self._pools:
            with self._lock:
                if pool_name not in self._pools:
                    self._pools[pool_name] = self._create_sqlalchemy_engine(database_url, pool_name)
        
        return self._pools[pool_name]
    
    def _create_sqlalchemy_engine(self, database_url: str, pool_name: str) -> Engine:
        """Create SQLAlchemy engine with advanced connection pooling for Digital Ocean"""
        
        # Parse connection parameters
        pool_config = self._get_pool_config()
        
        # Filter out None values and use only valid parameters
        engine_kwargs = {
            'poolclass': QueuePool,
            'pool_size': pool_config.get('pool_size', 8),
            'max_overflow': pool_config.get('max_overflow', 0),
            'pool_timeout': pool_config.get('pool_timeout', 60),
            'pool_recycle': pool_config.get('pool_recycle', 1800),
            'pool_pre_ping': pool_config.get('pool_pre_ping', True),
            'echo': pool_config.get('echo', False),
            'echo_pool': pool_config.get('echo_pool', False),
        }
        
        # Remove None values
        engine_kwargs = {k: v for k, v in engine_kwargs.items() if v is not None}
        
        # Create engine with connection pooling
        engine = create_engine(
            database_url,
            **engine_kwargs,
            # Connection-level settings optimized for Digital Ocean
            connect_args=get_connect_args(pool_name)
        )
        
        # Add event listeners for connection management
        self._setup_engine_events(engine, pool_name)
        
        logger.info(f"Created SQLAlchemy engine '{pool_name}' with pool size {pool_config['pool_size']} for Digital Ocean")
        return engine
    
    def _get_pool_config(self) -> Dict[str, Any]:
        """Get connection pool configuration from database_config.py"""
        return get_db_config()
    
    def _setup_engine_events(self, engine: Engine, pool_name: str):
        """Setup SQLAlchemy engine event listeners for monitoring and optimization"""
        
        @event.listens_for(engine, "connect")
        def receive_connect(dbapi_connection, connection_record):
            """Handle new connection creation"""
            self._stats['total_connections'] += 1
            self._stats['active_connections'] += 1
            logger.debug(f"New connection created for pool '{pool_name}'")
            
            # Set PostgreSQL session variables for Digital Ocean optimization
            try:
                with dbapi_connection.cursor() as cursor:
                    cursor.execute("SET SESSION statement_timeout = '180s'")  # 3 minutes
                    cursor.execute("SET SESSION idle_in_transaction_session_timeout = '60s'")  # 1 minute
                    cursor.execute("SET SESSION lock_timeout = '10s'")  # 10 seconds
                logger.debug(f"Session variables set successfully for pool '{pool_name}'")
            except Exception as e:
                logger.warning(f"Failed to set session variables for pool '{pool_name}': {e}")
                # Continue with connection creation even if session variables fail
        
        @event.listens_for(engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """Handle connection checkout from pool"""
            self._stats['idle_connections'] = max(0, self._stats['idle_connections'] - 1)
            self._stats['active_connections'] += 1
            self._update_pool_utilization()
            logger.debug(f"Connection checked out from pool '{pool_name}'")
        
        @event.listens_for(engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """Handle connection return to pool"""
            self._stats['active_connections'] = max(0, self._stats['active_connections'] - 1)
            self._stats['idle_connections'] += 1
            self._update_pool_utilization()
            logger.debug(f"Connection returned to pool '{pool_name}'")
        
        @event.listens_for(engine, "close")
        def receive_close(dbapi_connection, connection_record):
            """Handle connection close"""
            self._stats['active_connections'] = max(0, self._stats['active_connections'] - 1)
            self._update_pool_utilization()
            logger.debug(f"Connection closed for pool '{pool_name}'")
        
        @event.listens_for(engine, "handle_error")
        def receive_handle_error(exception):
            """Handle connection errors"""
            self._stats['failed_connections'] += 1
            logger.error(f"Database connection error in pool '{pool_name}': {exception}")
    
    def _update_pool_utilization(self):
        """Update pool utilization percentage"""
        total_connections = self._stats['active_connections'] + self._stats['idle_connections']
        if total_connections > 0:
            self._stats['pool_utilization'] = (self._stats['active_connections'] / total_connections) * 100
        else:
            self._stats['pool_utilization'] = 0.0
    
    def get_psycopg2_pool(self, database_url: str, pool_name: str = "psycopg2") -> SimpleConnectionPool:
        """Get or create psycopg2 connection pool for direct database access"""
        if pool_name not in self._pools:
            with self._lock:
                if pool_name not in self._pools:
                    self._pools[pool_name] = self._create_psycopg2_pool(database_url, pool_name)
        
        return self._pools[pool_name]
    
    def _create_psycopg2_pool(self, database_url: str, pool_name: str) -> SimpleConnectionPool:
        """Create psycopg2 connection pool"""
        # Parse connection string
        conn_params = self._parse_connection_string(database_url)
        
        pool_config = self._get_pool_config()
        
        try:
            pool = SimpleConnectionPool(
                minconn=1,
                maxconn=pool_config['pool_size'] + pool_config['max_overflow'],
                **conn_params,
                cursor_factory=RealDictCursor
            )
            logger.info(f"Created psycopg2 pool '{pool_name}' with max connections {pool_config['pool_size'] + pool_config['max_overflow']}")
            return pool
        except Exception as e:
            logger.error(f"Failed to create psycopg2 pool: {e}")
            raise
    
    def _parse_connection_string(self, database_url: str) -> Dict[str, str]:
        """Parse PostgreSQL connection string into parameters"""
        # Simple parsing - in production, use urllib.parse
        if database_url.startswith('postgresql://'):
            url = database_url.replace('postgresql://', '')
            if '@' in url:
                auth, rest = url.split('@', 1)
                if ':' in auth:
                    user, password = auth.split(':', 1)
                else:
                    user, password = auth, ''
                
                if ':' in rest:
                    host_port, database = rest.split('/', 1)
                    if ':' in host_port:
                        host, port = host_port.split(':', 1)
                    else:
                        host, port = host_port, '5432'
                else:
                    host, port, database = rest, '5432', 'postgres'
                
                return {
                    'host': host,
                    'port': port,
                    'database': database,
                    'user': user,
                    'password': password
                }
        
        # Fallback to default values
        return {
            'host': 'localhost',
            'port': '5432',
            'database': 'whatsapp_chatbot',
            'user': 'user',
            'password': 'password'
        }
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Get current pool statistics"""
        stats = self._stats.copy()
        
        # Add pool-specific information for SQLAlchemy pools
        for pool_name, pool in self._pools.items():
            if hasattr(pool, 'pool'):
                pool_obj = pool.pool
                if hasattr(pool_obj, 'size'):
                    stats[f'{pool_name}_pool_size'] = pool_obj.size()
                    stats[f'{pool_name}_checked_in'] = pool_obj.checkedin()
                    stats[f'{pool_name}_checked_out'] = pool_obj.checkedout()
                    stats[f'{pool_name}_overflow'] = pool_obj.overflow()
                    if hasattr(pool_obj, 'invalid'):
                        stats[f'{pool_name}_invalid'] = pool_obj.invalid()
        
        return stats
    
    def close_all_pools(self):
        """Close all connection pools"""
        with self._lock:
            for pool_name, pool in self._pools.items():
                try:
                    if hasattr(pool, 'dispose'):
                        pool.dispose()
                    elif hasattr(pool, 'closeall'):
                        pool.closeall()
                    logger.info(f"Closed pool '{pool_name}'")
                except Exception as e:
                    logger.error(f"Error closing pool '{pool_name}': {e}")
            
            self._pools.clear()
            self._stats = {
                'total_connections': 0,
                'active_connections': 0,
                'idle_connections': 0,
                'failed_connections': 0,
                'pool_utilization': 0.0
            }

# Global connection pool manager instance
pool_manager = ConnectionPoolManager()

# Get database connection parameters
DB_USER = os.getenv("POSTGRES_USER", "user")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "test")
DB_HOST = os.getenv("POSTGRES_HOST", "postgresql-postgresql")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "whatsapp_chatbot")

# Use DATABASE_URL from Helm if available, otherwise construct from individual variables
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

logger.info(f"Connecting to database", extra={"db_host": DB_HOST, "db_name": DB_NAME, "db_user": DB_USER})

def get_database_url():
    """Get the database URL for use in migrations and other contexts"""
    return DATABASE_URL

# Print the database URL (with password masked for security)
masked_url = f"postgresql://{DB_USER}:****@{DB_HOST}:{DB_PORT}/{DB_NAME}"
logger.info(f"Connecting to database", extra={"db_host": DB_HOST, "db_name": DB_NAME, "db_user": DB_USER})

# Get the main SQLAlchemy engine from the pool manager
engine = pool_manager.get_sqlalchemy_pool(DATABASE_URL, "whatsapp_chatbot")

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

def get_db():
    """Get database session with retry logic using the connection pool manager"""
    max_retries = 3
    retry_delay = 1  # seconds
    
    for attempt in range(max_retries):
        db = None
        try:
            db = SessionLocal()
            # Test the connection
            db.execute(text("SELECT 1"))
            yield db
            break
        except (OperationalError, DisconnectionError) as e:
            logger.warning(f"Database connection attempt {attempt + 1} failed: {str(e)}")
            if db:
                db.close()
            
            if attempt < max_retries - 1:
                logger.info(f"Retrying database connection in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                logger.error("All database connection attempts failed")
                raise
        except Exception as e:
            logger.error(f"Unexpected database error: {str(e)}")
            if db:
                db.close()
            raise
        finally:
            if db:
                db.close()

@contextmanager
def get_db_session():
    """Context manager for database sessions with automatic cleanup"""
    db = None
    try:
        db = SessionLocal()
        # Test the connection
        db.execute(text("SELECT 1"))
        yield db
    except (OperationalError, DisconnectionError) as e:
        logger.error(f"Database connection error: {str(e)}")
        if db:
            db.rollback()
        raise
    except Exception as e:
        logger.error(f"Database error: {str(e)}")
        if db:
            db.rollback()
        raise
    finally:
        if db:
            db.close()

def test_database_connection():
    """Test database connection and log pool status"""
    try:
        with get_db_session() as db:
            result = db.execute(text("SELECT 1")).scalar()
            logger.info("Database connection test successful")
            
            # Log pool status from the pool manager
            pool_stats = pool_manager.get_pool_stats()
            logger.info(f"Database pool status: {pool_stats}")
            
            return True
    except Exception as e:
        logger.error(f"Database connection test failed: {str(e)}")
        return False

def close_database_connections():
    """Close all database connections"""
    try:
        pool_manager.close_all_pools()
        logger.info("Database connections closed successfully")
    except Exception as e:
        logger.error(f"Error closing database connections: {str(e)}")

# Health check function for monitoring
def get_database_health():
    """Get database health status using the pool manager"""
    try:
        with get_db_session() as db:
            # Test basic connectivity
            db.execute(text("SELECT 1"))
            
            # Test a simple query
            result = db.execute(text("SELECT COUNT(*) FROM chatbots LIMIT 1")).scalar()
            
            # Get comprehensive pool statistics
            pool_stats = pool_manager.get_pool_stats()
            
            return {
                "status": "healthy",
                "pool_info": pool_stats,
                "chatbot_count": result
            }
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

def get_pool_status():
    """Get detailed connection pool status from the pool manager"""
    try:
        pool_stats = pool_manager.get_pool_stats()
        
        # Calculate additional metrics
        total_connections = pool_stats.get('active_connections', 0) + pool_stats.get('idle_connections', 0)
        utilization_percentage = pool_stats.get('pool_utilization', 0.0)
        
        return {
            "pool_type": "QueuePool",
            "pool_size": int(os.getenv('DB_POOL_SIZE', '10')),
            "max_overflow": int(os.getenv('DB_MAX_OVERFLOW', '20')),
            "checked_in": pool_stats.get('idle_connections', 0),
            "checked_out": pool_stats.get('active_connections', 0),
            "overflow": pool_stats.get('whatsapp_chatbot_overflow', 0),
            "invalid": pool_stats.get('whatsapp_chatbot_invalid', 0),
            "total_connections": total_connections,
            "utilization_percentage": round(utilization_percentage, 2),
            "failed_connections": pool_stats.get('failed_connections', 0),
            "total_created": pool_stats.get('total_connections', 0)
        }
    except Exception as e:
        logger.error(f"Error getting pool status: {str(e)}")
        return {"error": str(e)}

def reset_pool():
    """Reset the connection pool (useful for maintenance)"""
    try:
        pool_manager.close_all_pools()
        # Recreate the main engine
        global engine
        engine = pool_manager.get_sqlalchemy_pool(DATABASE_URL, "whatsapp_chatbot")
        logger.info("Database connection pool reset successfully")
        return {"status": "success", "message": "Pool reset successfully"}
    except Exception as e:
        logger.error(f"Error resetting pool: {str(e)}")
        return {"status": "error", "error": str(e)}

# Context manager for database sessions with pool management
@contextmanager
def get_db_session_advanced(pool_name: str = "whatsapp_chatbot"):
    """Advanced context manager for database sessions with pool management"""
    session = None
    try:
        engine = pool_manager.get_sqlalchemy_pool(DATABASE_URL, pool_name)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        session = SessionLocal()
        yield session
    except Exception as e:
        if session:
            session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        if session:
            session.close()

# Health check function using pool manager
def check_database_health_advanced() -> Dict[str, Any]:
    """Advanced database health check using the pool manager"""
    try:
        with get_db_session_advanced() as session:
            session.execute(text("SELECT 1"))
            return {
                'status': 'healthy',
                'message': 'Database connection is working',
                'pool_stats': pool_manager.get_pool_stats()
            }
    except Exception as e:
        return {
            'status': 'unhealthy',
            'message': f'Database connection failed: {e}',
            'pool_stats': pool_manager.get_pool_stats()
        }

# Cleanup function
def cleanup_database():
    """Cleanup database connections using the pool manager"""
    pool_manager.close_all_pools()