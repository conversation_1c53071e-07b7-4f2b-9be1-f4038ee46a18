from prometheus_client import Counter, Histogram, Gauge, Summary
import time

# Define metrics
# Counter for total requests
REQUEST_COUNT = Counter(
    'app_request_count', 
    'Total number of requests',
    ['method', 'endpoint', 'status_code']
)

# Histogram for request latency
REQUEST_LATENCY = Histogram(
    'app_request_latency_seconds', 
    'Request latency in seconds',
    ['method', 'endpoint']
)

# Counter for authentication failures
AUTH_FAILURE_COUNT = Counter(
    'app_auth_failure_count', 
    'Total number of authentication failures',
    ['reason']
)

# Counter for database operations
DB_OPERATION_COUNT = Counter(
    'app_db_operation_count', 
    'Total number of database operations',
    ['operation', 'table']
)

# Gauge for active connections
ACTIVE_CONNECTIONS = Gauge(
    'app_active_connections', 
    'Number of active connections'
)

# Summary for elasticsearch query time
ES_QUERY_TIME = Summary(
    'app_elasticsearch_query_time_seconds', 
    'Elasticsearch query time in seconds',
    ['query_type']
)

# Counter for chatbot conversations
CHATBOT_CONVERSATION_COUNT = Counter(
    'app_chatbot_conversation_count', 
    'Total number of chatbot conversations',
    ['status']
)

# Counter for OpenAI API calls
OPENAI_API_CALL_COUNT = Counter(
    'app_openai_api_call_count', 
    'Total number of OpenAI API calls',
    ['model']
)

# Context manager for measuring request latency
class LatencyTimer:
    def __init__(self, method, endpoint):
        self.method = method
        self.endpoint = endpoint
        
    def __enter__(self):
        self.start_time = time.time()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        latency = time.time() - self.start_time
        REQUEST_LATENCY.labels(method=self.method, endpoint=self.endpoint).observe(latency)