from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Request, Form, BackgroundTasks, status, Response, Security
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.utils.response_formatter import format_response, format_chatbot_response, format_questions_response

# Security scheme
security = HTTPBearer()
from app.models import (
    Document,
    Chatbot,
    ChatbotQuestion,
    ChatbotCreate,
    ChatbotUpdate,
    ConversationMessage,
    ConversationRequest,
    ConversationResponse,
    QuestionCreate,
    QuestionUpdate,
    QuestionCreateLegacy,
    QuestionUpdateLegacy,
    ChatbotKnowledgebase,
    ChatbotConversation,
    ConversationTokenUsage,
    ChatbotCreditUsage,
    CreditUsageResponse,
    KnowledgebaseDocument,
    KnowledgebaseUpdateRequest,
    BackgroundTaskResponse,
    ChatbotNode,
    ChatbotNodeEntityField,
    <PERSON><PERSON>botEdge,
    ChatbotNodeResponse,
    ChatbotEdgeResponse,
    RuleBasedChatbotFlow,
    RuleBasedChatbotFlowResponse
)
from typing import List, Dict, Optional, Any
from app.dependencies import get_auth_context, AuthContext
from app.services.elasticsearch_service import ElasticsearchService
from app.services.s3_service import S3Service
from app.services.redis_service import RedisService
from app.services.chatbot_service import ChatbotService
from app.services.rule_based_chatbot_service import RuleBasedChatbotService
from app.exceptions import (
    ChatbotNotFoundException,
    RuleBasedFlowValidationError,
    RuleBasedFlowCreationError
)
from app.services.charge_calculator import charge_calculator
from app.utils.conversation_state_utils import (
    clean_conversation_state,
    update_conversation_in_db,
    sanitize_state_for_db,
    store_conversation_turn,
    track_credit_usage
)
from app.services.conversation_event_publisher import conversation_event_publisher
import PyPDF2
import uuid
from io import BytesIO
import logging
import json
import re
from datetime import datetime, timezone, date

router = APIRouter(
    prefix="/v1/chatbot",
    tags=["chatbot"]
)

logger = logging.getLogger(__name__)





# List all chatbots endpoint
@router.get("")
async def list_chatbots(
    request: Request,
    include_draft: bool = True
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to list chatbots
    chatbot_service = ChatbotService()
    return chatbot_service.list_chatbots(int(tenant_id), include_draft)

# Create chatbot endpoint
@router.post("")
async def create_chatbot(
    request: Request,
    chatbot_data: ChatbotCreate
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    logger.info(f"Creating chatbot with data: {auth_context}")
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    # Use ChatbotService to create the chatbot
    chatbot_service = ChatbotService()
    return await chatbot_service.create_chatbot(chatbot_data, tenant_id, user_id, token, auth_context)

# Update chatbot endpoint
@router.put("/{chatbot_id}")
async def update_chatbot(
    request: Request,
    chatbot_id: str,
    chatbot_data: ChatbotUpdate
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    # Use ChatbotService to update the chatbot
    chatbot_service = ChatbotService()
    return await chatbot_service.update_chatbot(chatbot_id, chatbot_data, tenant_id, user_id, token, auth_context)

# Delete chatbot endpoint
@router.delete("/{chatbot_id}")
async def delete_chatbot(
    request: Request,
    chatbot_id: str
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to delete the chatbot
    chatbot_service = ChatbotService()
    return chatbot_service.delete_chatbot(chatbot_id, int(tenant_id))

# Activate chatbot endpoint
@router.post("/{chatbot_id}/activate")
async def activate_chatbot(
    request: Request,
    chatbot_id: str
):
    """
    Activate a chatbot (set status to ACTIVE)
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    # Use ChatbotService to activate the chatbot
    chatbot_service = ChatbotService()
    return await chatbot_service.update_chatbot_status(
        chatbot_id, "ACTIVE", tenant_id, user_id, token
    )

# Deactivate chatbot endpoint
@router.post("/{chatbot_id}/deactivate")
async def deactivate_chatbot(
    request: Request,
    chatbot_id: str
):
    """
    Deactivate a chatbot (set status to INACTIVE)
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    # Use ChatbotService to deactivate the chatbot
    chatbot_service = ChatbotService()
    return await chatbot_service.update_chatbot_status(
        chatbot_id, "INACTIVE", tenant_id, user_id, token
    )

# Generic status update endpoint (kept for flexibility)
@router.patch("/{chatbot_id}/status")
async def update_chatbot_status(
    request: Request,
    chatbot_id: str,
    status_data: dict
):
    """
    Update chatbot status (DRAFT -> ACTIVE, ACTIVE -> INACTIVE, etc.)
    This endpoint is kept for advanced use cases or setting to DRAFT
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    # Validate status
    new_status = status_data.get("status")
    if not new_status or new_status not in ["DRAFT", "ACTIVE", "INACTIVE"]:
        raise HTTPException(
            status_code=400,
            detail="Status must be one of: DRAFT, ACTIVE, INACTIVE"
        )

    # Use ChatbotService to update the chatbot status
    chatbot_service = ChatbotService()
    return await chatbot_service.update_chatbot_status(
        chatbot_id, new_status, tenant_id, user_id, token
    )

# Delete question endpoint
@router.delete("/{chatbot_id}/questions/{question_id}")
async def delete_question(
    request: Request,
    chatbot_id: str,
    question_id: str
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to delete the question
    chatbot_service = ChatbotService()
    return chatbot_service.delete_question(chatbot_id, question_id, int(tenant_id))

# Conversation endpoints
@router.post("/conversations")
async def start_conversation(
    request: Request,
    conversation_request: ConversationRequest,
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Security(security)
):
    import logging
    logger = logging.getLogger(__name__)
    
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    
    logger.info("🚀 STARTING CONVERSATION - Request received")
    logger.info(f"   Tenant ID: {tenant_id}")
    logger.info(f"   User ID: {auth_context.user_id}")
    logger.info(f"   Message: {conversation_request.message}")
    logger.info(f"   Connected Account: {conversation_request.connectedAccount.name} (ID: {conversation_request.connectedAccount.id})")
    try:
        received_payload_log = {
            "message": conversation_request.message,
            "entityDetails": [
                {"id": e.id, "entityType": e.entityType} for e in conversation_request.entityDetails
            ],
            "connectedAccount": {
                "id": conversation_request.connectedAccount.id,
                "name": conversation_request.connectedAccount.name
            },
            "messageConversationId": getattr(conversation_request, "messageConversationId", None)
        }
        logger.info(f"📥 START CONVERSATION - Received payload: {json.dumps(received_payload_log, default=str)}")
    except Exception as e:
        logger.warning(f"Could not serialize received payload for logging: {str(e)}")
    
    # Log entity details
    logger.info(f"📋 ENTITY DETAILS - Count: {len(conversation_request.entityDetails)}")
    for i, entity in enumerate(conversation_request.entityDetails, 1):
        logger.info(f"   Entity {i}: {entity.entityType} (ID: {entity.id})")
    
    # Validate entity details
    if not conversation_request.entityDetails or len(conversation_request.entityDetails) == 0:
        logger.error("❌ VALIDATION FAILED - No entity details provided")
        raise HTTPException(status_code=400, detail="At least one entity detail is required")

    # Extract connected account details
    connected_account_id = conversation_request.connectedAccount.id
    connected_account_name = conversation_request.connectedAccount.name

    # Use ChatbotService to find chatbot by connected account
    # Note: Multiple active chatbots per account are now supported
    # This endpoint returns the first active chatbot for backward compatibility
    # For workflow-triggered conversations, use the workflow event listener which receives specific chatbot_id
    chatbot_service = ChatbotService()
    logger.info(f"🔍 SEARCHING CHATBOT - Connected Account ID: {connected_account_id}")

    # Find first active chatbot by connected account ID
    chatbot = chatbot_service.find_chatbot_by_account(
        connected_account_id,
        int(tenant_id)  # Convert string to int for database query
    )
    if not chatbot:
        logger.error(f"❌ CHATBOT NOT FOUND - Connected Account ID: {connected_account_id}")
        raise HTTPException(status_code=404, detail=f"No active chatbot found for connected account ID {connected_account_id}")
    
    logger.info(f"✅ CHATBOT FOUND - ID: {chatbot.id}, Name: {chatbot.name}, Status: {chatbot.status}")

    # RULE-based branch: skip question flow and publish nodeDetails for start node
    if (getattr(chatbot, "type", "AI") or "AI").upper() == "RULE":
        logger.info("🤖 RULE-BASED CHATBOT - Skipping question flow and publishing start node details")

        # Generate conversation ID
        conversation_id = str(uuid.uuid4())
        logger.info(f"🆔 CONVERSATION ID GENERATED: {conversation_id}")

        # Build minimal conversation state
        # Initialize rule_current_node_id as None - it will be set by the service after processing the first node
        conversation_state = {
            "chatbot_id": chatbot.id,
            "chatbot_name": chatbot.name,  # Add chatbot name to state
            "tenant_id": tenant_id,
            "user_id": auth_context.user_id,
            "chatbotType": chatbot.type.upper(),  # Use actual chatbot type from database
            "entity_details": [{"entityId": e.id, "entityType": e.entityType} for e in conversation_request.entityDetails],
            "connected_account": {"id": conversation_request.connectedAccount.id, "name": conversation_request.connectedAccount.name},
            "auth_token": credentials.credentials,  # Store auth token for variable substitution
            "rule_current_node_id": None,  # Initialize for RULE-based chatbots - will be set after first node processing
            "history": [
                {"role": "user", "content": conversation_request.message}
            ]
        }

        # Do not precompute start node here to avoid duplicate DB reads; publisher will include nodeDetails

        # Store in Redis and DB
        redis_service = RedisService()
        redis_service.store_conversation_state(conversation_id, conversation_state)

        # Sanitize state before storing in database (remove JWT tokens)
        sanitized_state = sanitize_state_for_db(conversation_state)
        conversation = ChatbotConversation(
            id=conversation_id,
            chatbot_id=chatbot.id,
            tenant_id=tenant_id,
            conversation_data=json.dumps(sanitized_state),
            completed=False,
            user_id=auth_context.user_id,
            entity_details=[{"entityId": e.id, "entityType": e.entityType, "ownerId": getattr(e, 'ownerId', None)} for e in conversation_request.entityDetails],
            connected_account_id=conversation_request.connectedAccount.id,
            connected_account_name=conversation_request.connectedAccount.name,
            entity_update_status=None
        )
        db.add(conversation)
        db.commit()

        welcome_message = chatbot.welcome_message or f"Welcome to {chatbot.name}!"
        charge = 0
        message_conversation_id = getattr(conversation_request, "messageConversationId", None)

        # Log outgoing preview
        try:
            outgoing_event_preview = {
                "chatbotConversationId": conversation_id,
                "message": None,
                "completed": False,
                "charge": 0,
                "welcomeMessage": welcome_message,
                "chatbotType": chatbot.type.upper(),
                "nodeDetails": "computed_in_service_if_RULE",
                "messageConversationId": message_conversation_id
            }
            logger.info(f"📤 START CONVERSATION ({chatbot.type.upper()}) - Outgoing event preview: {json.dumps(outgoing_event_preview, default=str)}")
        except Exception as e:
            logger.warning(f"Could not serialize outgoing event preview: {str(e)}")

        # Publish start event with nodeDetails (built in service if RULE-based)
        await chatbot_service.publish_start_conversation_event(
            chatbot=chatbot,
            tenant_id=int(tenant_id),
            conversation_id=conversation_id,
            welcome_message=welcome_message,
            first_question={"question": None},
            charge=charge,
            message_conversation_id=message_conversation_id,
            db=db,
            precomputed_node_details=None,
            entity_details=[{"entityId": e.id, "entityType": e.entityType, "ownerId": getattr(e, 'ownerId', None)} for e in conversation_request.entityDetails],
            auth_token=credentials.credentials
        )

        logger.info("🎉 RULE-BASED CONVERSATION STARTED - Event published")
        return Response(status_code=200)

    # Get chatbot questions (AI-based only)
    logger.info(f"🔍 FETCHING QUESTIONS - Chatbot ID: {chatbot.id}")
    questions = chatbot_service.get_chatbot_questions_for_conversation(chatbot.id, int(tenant_id))
    logger.info(f"📝 QUESTIONS FOUND - Count: {len(questions)}")
    
    for i, q in enumerate(questions, 1):
        logger.info(f"   Question {i}: '{q['question']}' (ID: {q['id']})")
        logger.info(f"     Entity Fields: {len(q['entity_fields'])} fields")
        for j, ef in enumerate(q['entity_fields'], 1):
            logger.info(f"       Field {j}: {ef['entity_type']} - {ef['name']} (ID: {ef['field_id']}, Standard: {ef['standard']})")

    if not questions:
        logger.error("❌ NO QUESTIONS FOUND - Chatbot has no configured questions")
        raise HTTPException(status_code=404, detail="No questions found for this chatbot")

    # Generate conversation ID
    conversation_id = str(uuid.uuid4())
    logger.info(f"🆔 CONVERSATION ID GENERATED: {conversation_id}")

    # Get entity types from the conversation request
    entity_types = [e.entityType for e in conversation_request.entityDetails]
    logger.info(f"🎯 ENTITY TYPES IN CONVERSATION: {entity_types}")

    # Filter questions based on entity types present in the conversation
    # Each question should be asked only ONCE, even if it maps to multiple entities
    relevant_questions = []
    logger.info("🔍 FILTERING QUESTIONS BY ENTITY TYPES")

    for q in questions:
        # For each question, find entity fields that match the entity types in this conversation
        # Use case-insensitive matching to handle different entity type formats
        matching_entity_fields = []
        for ef in q["entity_fields"]:
            # Check if this entity field matches any of the entity types in the conversation
            for entity_type in entity_types:
                if ef["entity_type"].lower() == entity_type.lower():
                    matching_entity_fields.append(ef)
                    logger.info(f"   ✅ MATCH FOUND - Question: '{q['question']}', Entity: {ef['entity_type']}, Field: {ef['name']}")
                    break  # Found a match, no need to check other entity types
        
        if not matching_entity_fields:
            logger.info(f"   ❌ NO MATCH - Question: '{q['question']}' (no matching entity types: {[ef['entity_type'] for ef in q['entity_fields']]})")

        if matching_entity_fields:
            # Create ONE question entry that maps to ALL matching entity fields
            # This ensures the question is asked only once, but the answer applies to all relevant entities
            relevant_question = {
                "id": q["id"],  # Use original question ID (no entity suffix)
                "question_id": q["id"],
                "question": q["question"],
                "display_name": matching_entity_fields[0]["display_name"],  # Use first field's display name
                "standard": matching_entity_fields[0]["standard"],  # Use first field's standard flag
                "entity_fields": matching_entity_fields  # Store ALL matching entity fields
            }
            relevant_questions.append(relevant_question)
            logger.info(f"   ✅ QUESTION ADDED - '{q['question']}' with {len(matching_entity_fields)} entity field mappings")

    logger.info(f"📊 FILTERING COMPLETE - Relevant questions: {len(relevant_questions)} out of {len(questions)} total")

    if not relevant_questions:
        logger.error(f"❌ NO RELEVANT QUESTIONS - Entity types: {entity_types}")
        raise HTTPException(status_code=404, detail=f"No questions found for entity types: {entity_types}")

    # Determine chatbot type contract (AI or RULE)
    chatbot_type_contract = "RULE" if (getattr(chatbot, "type", "AI") or "AI").upper() == "RULE" else "AI"

    # Initialize conversation state with new structure including entity details
    conversation_state = {
        "chatbot_id": chatbot.id,
        "chatbot_name": chatbot.name,  # Add chatbot name to state
        "tenant_id": tenant_id,
        "user_id": auth_context.user_id,
        "chatbotType": chatbot_type_contract,
        "entity_details": [{"id": e.id, "entityType": e.entityType} for e in conversation_request.entityDetails],
        "connected_account": {"id": conversation_request.connectedAccount.id, "name": conversation_request.connectedAccount.name},
        "all_questions": relevant_questions,
        "remaining_questions": relevant_questions.copy(),
        "asked_questions": [],
        "answers": [],
        "history": [
            {"role": "system", "content": f"You are a helpful assistant for {chatbot.name}. Your job is to collect information from users by asking questions one by one."},
            {"role": "user", "content": conversation_request.message}
        ]
    }
    
    logger.info("💾 CONVERSATION STATE CREATED")
    logger.info(f"   Chatbot ID: {conversation_state['chatbot_id']}")
    logger.info(f"   Entity Details: {len(conversation_state['entity_details'])} entities")
    for i, entity in enumerate(conversation_state['entity_details'], 1):
        logger.info(f"     Entity {i}: {entity['entityType']} (ID: {entity['id']})")
    logger.info(f"   Questions: {len(conversation_state['all_questions'])} questions")
    for i, q in enumerate(conversation_state['all_questions'], 1):
        logger.info(f"     Question {i}: '{q['question']}' with {len(q['entity_fields'])} entity fields")
    
    # Store in Redis
    logger.info("💾 STORING CONVERSATION STATE IN REDIS")
    redis_service = RedisService()
    redis_service.store_conversation_state(conversation_id, conversation_state)
    logger.info(f"   ✅ Conversation state stored in Redis with ID: {conversation_id}")
    
    # Create initial conversation record in database with entity details
    logger.info("💾 CREATING CONVERSATION RECORD IN DATABASE")
    # Sanitize state before storing in database (remove JWT tokens)
    sanitized_state = sanitize_state_for_db(conversation_state)
    conversation = ChatbotConversation(
        id=conversation_id,
        chatbot_id=chatbot.id,
        tenant_id=tenant_id,
        conversation_data=json.dumps(sanitized_state),
        completed=False,
        user_id=auth_context.user_id,
        # Store entity details for tracking and auditing
        entity_details=[{"entityId": e.id, "entityType": e.entityType} for e in conversation_request.entityDetails],
        connected_account_id=conversation_request.connectedAccount.id,
        connected_account_name=conversation_request.connectedAccount.name,
        entity_update_status=None  # Will be populated after conversation completion
    )
    db.add(conversation)
    logger.info(f"   ✅ Conversation record added to database")
    
    # Initialize ElasticsearchService for token counting
    logger.info("🔍 INITIALIZING ELASTICSEARCH SERVICE")
    es_service = ElasticsearchService()

    db.commit()
    logger.info("💾 DATABASE COMMITTED - Conversation record saved")

    # Use LLM to select the first question based on user's initial message
    logger.info(f"🤖 SELECTING FIRST QUESTION - Remaining questions: {len(conversation_state['remaining_questions'])}")

    # Try to select first question using LLM
    first_question, input_tokens, output_tokens, model = es_service.select_next_question(
        conversation_state["history"],
        conversation_state["remaining_questions"],
        conversation_state["answers"]
    )

    # Fallback: if LLM doesn't select a question, use the first available question
    if not first_question and conversation_state["remaining_questions"]:
        first_question = conversation_state["remaining_questions"][0]
        input_tokens, output_tokens, model = 0, 0, "fallback"
        logger.info(f"   ⚠️  LLM didn't select a question, using fallback: '{first_question['question']}'")

    if first_question:
        logger.info(f"   ✅ FIRST QUESTION SELECTED: '{first_question['question']}' (ID: {first_question['id']})")
        logger.info(f"   📊 TOKENS - Input: {input_tokens}, Output: {output_tokens}, Model: {model}")
    else:
        logger.warning("   ⚠️  NO FIRST QUESTION SELECTED - No questions available")

    if first_question:
        # Move the selected question from remaining to asked
        conversation_state["remaining_questions"] = [q for q in conversation_state["remaining_questions"] if q["id"] != first_question["id"]]
        conversation_state["asked_questions"].append(first_question)

        # Store conversation turn for question selection
        # Reconstruct the LLM prompt that was used for question selection
        question_selection_prompt = [
            {
                "role": "system",
                "content": """You are a helpful assistant that selects the most appropriate next question to ask based on the conversation context.

                Your task is to:
                1. Analyze the conversation history and previously answered questions
                2. Select the most logical next question from the remaining questions
                3. Consider the natural flow of conversation and what would make sense to ask next
                4. Respond with ONLY the number (1, 2, 3, etc.) of the question you want to select

                Do not provide any explanation, just the number."""
            },
            {
                "role": "user",
                "content": f"""Conversation context:
{conversation_state["history"][-6:]}

Remaining questions to choose from:
{[f"{i+1}. {q.get('question', '')}" for i, q in enumerate(conversation_state["remaining_questions"])]}

Select the most appropriate next question by responding with its number (1, 2, 3, etc.)."""
            }
        ]

        logger.info("💾 STORING CONVERSATION TURN")
        store_conversation_turn(
            db, conversation_id, tenant_id,
            llm_prompt=question_selection_prompt,
            llm_response=f"Selected question: {first_question['question']}",
            input_tokens=input_tokens,
            output_tokens=output_tokens
        )
        logger.info("   ✅ Conversation turn stored")

        # Update state in Redis
        logger.info("💾 UPDATING CONVERSATION STATE IN REDIS")
        redis_service.store_conversation_state(conversation_id, conversation_state)
        logger.info("   ✅ Conversation state updated in Redis")

        # Use chatbot's welcome message
        welcome_message = chatbot.welcome_message or f"Welcome to {chatbot.name}!"
        logger.info(f"💬 WELCOME MESSAGE: {welcome_message}")

        # Calculate charge for the first question (predefined = 1)
        charge = charge_calculator.calculate_question_charge(
            first_question, is_predefined=True, is_llm_generated=False
        )
        logger.info(f"💰 CHARGE CALCULATED: {charge}")

        # Store the charge in conversation state for tracking
        conversation_state["current_charge"] = charge
        conversation_state["total_charge"] = charge

        # Update state in Redis with charge information
        redis_service.store_conversation_state(conversation_id, conversation_state)

        # Publish event via service layer (no repo logic in controller)
        message_conversation_id = getattr(conversation_request, "messageConversationId", None)
        try:
            chatbot_type_contract = "RULE" if (getattr(chatbot, "type", "AI") or "AI").upper() == "RULE" else "AI"
            event_message_preview = (first_question or {}).get("question") if chatbot_type_contract == "AI" else None
            event_charge_preview = 0 if chatbot_type_contract == "RULE" else charge
            outgoing_event_preview = {
                "chatbotConversationId": conversation_id,
                "message": event_message_preview,
                "completed": False,
                "charge": event_charge_preview,
                "welcomeMessage": welcome_message,
                "chatbotType": chatbot_type_contract,
                "nodeDetails": "computed_in_service_if_RULE",
                "messageConversationId": message_conversation_id
            }
            logger.info(f"📤 START CONVERSATION - Outgoing event preview: {json.dumps(outgoing_event_preview, default=str)}")
        except Exception as e:
            logger.warning(f"Could not serialize outgoing event preview for logging: {str(e)}")
        await chatbot_service.publish_start_conversation_event(
            chatbot=chatbot,
            tenant_id=int(tenant_id),
            conversation_id=conversation_id,
            welcome_message=welcome_message,
            first_question=first_question,
            charge=charge,
            message_conversation_id=message_conversation_id,
            db=db
        )

        logger.info("🎉 CONVERSATION STARTED SUCCESSFULLY - Event published")
        return Response(status_code=200)
    else:
        # Fallback if no question could be selected - use chatbot's welcome message
        logger.warning("⚠️  FALLBACK MODE - No question could be selected")
        welcome_message = chatbot.welcome_message or f"Welcome to {chatbot.name}!"
        fallback_question = "How can I help you today?"

        # Store fallback state
        conversation_state["current_charge"] = 0
        conversation_state["total_charge"] = 0
        conversation_state["fallback_mode"] = True

        # Update state in Redis
        redis_service.store_conversation_state(conversation_id, conversation_state)

        # Publish event for fallback as well via service
        message_conversation_id = getattr(conversation_request, "messageConversationId", None)
        try:
            chatbot_type_contract = "RULE" if (getattr(chatbot, "type", "AI") or "AI").upper() == "RULE" else "AI"
            event_message_preview = fallback_question if chatbot_type_contract == "AI" else None
            outgoing_event_preview = {
                "chatbotConversationId": conversation_id,
                "message": event_message_preview,
                "completed": False,
                "charge": 0,
                "welcomeMessage": welcome_message,
                "chatbotType": chatbot_type_contract,
                "nodeDetails": "computed_in_service_if_RULE",
                "messageConversationId": message_conversation_id
            }
            logger.info(f"📤 START CONVERSATION (Fallback) - Outgoing event preview: {json.dumps(outgoing_event_preview, default=str)}")
        except Exception as e:
            logger.warning(f"Could not serialize fallback outgoing event preview for logging: {str(e)}")
        await chatbot_service.publish_start_conversation_event(
            chatbot=chatbot,
            tenant_id=int(tenant_id),
            conversation_id=conversation_id,
            welcome_message=welcome_message,
            first_question={"question": fallback_question},
            charge=0,
            message_conversation_id=message_conversation_id,
            db=db
        )

        logger.warning("🎉 CONVERSATION STARTED IN FALLBACK MODE - Event published")
        return Response(status_code=200)

@router.post("/conversations/{conversation_id}/complete")
async def complete_conversation(
    conversation_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Complete conversation and update all entities with collected answers
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    try:
        # Get conversation state from Redis
        redis_service = RedisService()
        conversation_state = redis_service.get_conversation_state(conversation_id)

        if not conversation_state:
            raise HTTPException(status_code=404, detail="Conversation not found")

        # Extract entity details and collected answers
        entity_details = conversation_state.get("entity_details", [])
        collected_answers = conversation_state.get("answers", [])

        if not entity_details:
            raise HTTPException(status_code=400, detail="No entities found in conversation")

        if not collected_answers:
            raise HTTPException(status_code=400, detail="No answers collected in conversation")

        # Use ChatbotService to update all entities
        chatbot_service = ChatbotService()
        update_results = await chatbot_service.update_entities_after_conversation(
            entity_details,
            collected_answers,
            tenant_id,
            user_id,
            conversation_state.get("chatbot_id"),
            conversation_state  # Pass conversation state
        )

        # Mark conversation as completed in database and store entity update results
        conversation = db.query(ChatbotConversation).filter(
            ChatbotConversation.id == conversation_id,
            ChatbotConversation.tenant_id == tenant_id
        ).first()

        if conversation:
            conversation.completed = True
            conversation.entity_update_status = update_results  # Store update results in dedicated column
            # Sanitize state before storing in database (remove JWT tokens)
            completion_state = {
                **conversation_state,
                "completion_timestamp": datetime.now(timezone.utc).isoformat(),
                "entity_update_results": update_results
            }
            sanitized_state = sanitize_state_for_db(completion_state)
            conversation.conversation_data = json.dumps(sanitized_state)
            db.commit()

        # Prepare and publish conversation completion event with new payload format
        try:
            from app.services.conversation_event_publisher import ConversationEventPublisher
            
            # Get auth token from request
            token = request.headers.get("Authorization", "").replace("Bearer ", "")
            
            # Prepare the new payload format
            completion_payload = await chatbot_service.prepare_conversation_completion_payload(
                conversation_id=conversation_id,
                entity_details=entity_details,
                conversation_state=conversation_state,
                token=token,
                completion_message="Conversation completed successfully",
                charge=0
            )
            
            # Publish the completion event
            event_publisher = ConversationEventPublisher()
            await event_publisher.publish_conversation_completion(
                chatbot_conversation_id=conversation_id,
                completion_message=completion_payload.get("message"),
                charge=completion_payload.get("charge", 0),
                chatbot_type=completion_payload.get("chatbotType", "RULE"),
                entity_details=completion_payload.get("entityDetails", []),
                node_details=completion_payload.get("nodeDetails")
            )
            
            logger.info(f"Published conversation completion event for {conversation_id}")
            
            # Note: Workflow completion events are published automatically in MessageEventListener
            # when conversations complete naturally, not through this manual completion API
            
        except Exception as e:
            logger.error(f"Error publishing conversation completion event: {str(e)}")
            # Don't fail the completion if event publishing fails

        # Clear conversation state from Redis
        redis_service.clear_conversation_state(conversation_id)

        return {
            "message": "Conversation completed successfully",
            "conversation_id": conversation_id,
            "entities_updated": update_results["successful_updates"],
            "entities_failed": update_results["failed_updates"],
            "total_entities": update_results["total_entities"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing conversation {conversation_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error completing conversation: {str(e)}")

@router.get("/conversations/entity/{entity_type}/{entity_id}")
async def get_conversations_by_entity(
    entity_type: str,
    entity_id: int,
    request: Request
):
    """
    Get all conversations that involved a specific entity
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    try:
        chatbot_service = ChatbotService()
        conversations = chatbot_service.get_conversations_by_entity(
            entity_id, entity_type, int(tenant_id)
        )

        return {
            "entity_id": entity_id,
            "entity_type": entity_type,
            "total_conversations": len(conversations),
            "conversations": conversations
        }

    except Exception as e:
        logger.error(f"Error getting conversations for {entity_type} {entity_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving conversations: {str(e)}")

@router.get("/conversations/account/{connected_account_id}")
async def get_conversations_by_account(
    connected_account_id: int,
    request: Request
):
    """
    Get all conversations for a specific connected account
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    try:
        chatbot_service = ChatbotService()
        conversations = chatbot_service.get_conversations_by_account(
            connected_account_id, int(tenant_id)
        )

        return {
            "connected_account_id": connected_account_id,
            "total_conversations": len(conversations),
            "conversations": conversations
        }

    except Exception as e:
        logger.error(f"Error getting conversations for account {connected_account_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving conversations: {str(e)}")

@router.post("/conversations/{conversation_id}")
async def continue_conversation(
    conversation_id: str,
    message: ConversationMessage,
    db: Session = Depends(get_db)
):
    # Get conversation state from Redis
    redis_service = RedisService()
    state = redis_service.get_conversation_state(conversation_id)

    if not state:
        raise HTTPException(status_code=404, detail="Conversation not found or expired")

    # Update conversation last activity for idle monitoring
    redis_service.update_conversation_last_activity(conversation_id)

    # Clean and normalize conversation state to ensure required fields exist
    state = clean_conversation_state(state)
    
    # Update conversation history
    state["history"].append({"role": "user", "content": message.message})
    
    # Initialize ElasticsearchService
    es_service = ElasticsearchService()
    
    # Get tenant_id for token usage tracking
    tenant_id = state.get("tenant_id", "unknown")
    
    # Check if conversation is already completed (in knowledge search phase)
    if state.get("completed", False):
        print("---------------Questions are completed")

        # Check if conversation is already ended
        if state.get("ended", False):
            # Conversation is already ended, don't allow more messages
            return {
                "conversation_id": conversation_id,
                "answer": "This conversation has already ended. Thank you for using our service!",
                "completed": True,
                "ended": True,
                "is_knowledge_response": True
            }

        # Check if user has already reached the 5-question limit
        chatbot_service = ChatbotService()
        current_question_count = chatbot_service.get_conversation_questions_count(state)

        if current_question_count >= 5:
            # User has already asked 5 questions, don't allow more
            limit_response = "You've already asked 5 questions, which is our limit for this session. Your information has been completed. Thank you!"

            # Update state
            state["history"].append({"role": "assistant", "content": limit_response})
            state["ended"] = True
            redis_service.store_conversation_state(conversation_id, state)

            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)

            return {
                "conversation_id": conversation_id,
                "answer": limit_response,
                "nextQuestion": None,
                "completed": True,
                "is_knowledge_response": True,
                "auto_completed": True,
                "reason": "5_question_limit_already_reached"
            }

        # First, check if user wants to end the conversation using enhanced LLM detection
        conversation_context = ""
        if state.get("history"):
            # Get last few messages for context
            recent_messages = state["history"][-4:]  # Last 4 messages for context
            conversation_context = "\n".join([f"{msg.get('role', '')}: {msg.get('content', '')}" for msg in recent_messages])

        wants_to_end, termination_input_tokens, termination_output_tokens, termination_model = es_service.detect_conversation_termination(
            message.message,
            conversation_context
        )
        print(f"------------Whatas_to_end----{wants_to_end}")

        if wants_to_end:
            print(f"------------User wants to end conversation")

            # Generate a personalized farewell message using enhanced method
            conversation_summary = ""
            if state.get("answers"):
                conversation_summary = f"We collected information about: {', '.join([ans.get('question', '') for ans in state['answers']])}"

            farewell, input_tokens, output_tokens, model = es_service.generate_farewell_message(
                message.message,
                conversation_summary
            )

            # Create the farewell prompt for storage (reconstruct what was sent to LLM)
            farewell_prompt = [
                {
                    "role": "system",
                    "content": """You are a helpful and polite assistant. The user is ending the conversation. Generate a warm, professional farewell message that:

                    1. Acknowledges their decision to end the conversation
                    2. Thanks them for their time and interaction
                    3. Expresses that it was a pleasure helping them
                    4. Invites them to return if they need future assistance
                    5. Wishes them well

                    Keep the message concise but warm, professional, and genuinely appreciative."""
                },
                {
                    "role": "user",
                    "content": f"""The user said: "{message.message}"

Conversation summary: {conversation_summary}

Generate a polite farewell message that acknowledges their decision to end the conversation and thanks them warmly."""
                }
            ]

            # Store conversation turn (user input + AI response)
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=farewell_prompt,  # Use the reconstructed farewell prompt
                llm_response=farewell,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )
            
            # Update state
            state["history"].append({"role": "assistant", "content": farewell})
            state["ended"] = True
            redis_service.store_conversation_state(conversation_id, state)
            
            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)

            # Publish completion event (user-driven completion)
            from app.services.conversation_event_publisher import conversation_event_publisher
            import asyncio

            # Since this is a sync function, we need to handle the async call
            try:
                # Create a new event loop if one doesn't exist
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Schedule the completion event as a conversation response to include chatbotType
            loop.create_task(
                conversation_event_publisher.publish_conversation_response(
                    chatbot_conversation_id=conversation_id,
                    message=farewell,
                    completed=True,
                    charge=0,
                    extra={"chatbotType": state.get("chatbotType")}
                )
            )

            return {
                "conversation_id": conversation_id,
                "answer": farewell,
                "completed": True,
                "ended": True
            }
        
        # Perform semantic search in the knowledgebase
        chatbot_id = state["chatbot_id"]
        tenant_id = state["tenant_id"]

        # Check if chatbot has any knowledgebase documents
        chatbot_service = ChatbotService()
        has_knowledgebase = chatbot_service.has_knowledgebase(chatbot_id, int(tenant_id))

        # Check if user has already reached the 5-question limit in this conversation
        chatbot_service = ChatbotService()
        current_question_count = chatbot_service.get_conversation_questions_count(state)

        if current_question_count >= 5:
            # User has already asked 5 questions, don't allow more
            limit_response = "You've already asked 5 questions, which is our limit for this session. Your information has been completed. Thank you!"

            # Update state
            state["history"].append({"role": "assistant", "content": limit_response})
            state["completed"] = True
            redis_service.store_conversation_state(conversation_id, state)

            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state)

            return {
                "conversation_id": conversation_id,
                "answer": limit_response,
                "nextQuestion": None,
                "completed": True,
                "is_knowledge_response": True,
                "auto_completed": True,
                "reason": "5_question_limit_already_reached"
            }

        # Perform semantic search with chatbot ID filtering using master index strategy
        search_results = []
        if has_knowledgebase:
            print(f"-------------Searching for question in chatbot {chatbot_id} knowledgebase")
            search_results = es_service.semantic_search(
                tenant_id=tenant_id,
                query=message.message,
                chatbot_id=chatbot_id,
                limit=3,
                db=db  # Pass database session for master index strategy
            )
        else:
            print(f"-------------No knowledgebase associated with this chatbot")

        # Sort results by score and check relevance
        search_results.sort(key=lambda x: x["score"], reverse=True)
        best_score = search_results[0]["score"] if search_results else 0
        
        # Check if the question is out of context (low relevance score)
        # Threshold can be adjusted based on testing - 0.5 is a reasonable starting point
        OUT_OF_CONTEXT_THRESHOLD = 0.5
        
        if not search_results or best_score < OUT_OF_CONTEXT_THRESHOLD:
            # Question is out of context - return strict 15-word response
            logger.info(f"Out-of-context question detected. Best score: {best_score}, threshold: {OUT_OF_CONTEXT_THRESHOLD}")
            out_of_context_response = "This question is out of context and we do not have answer for it now."
            
            # Store conversation turn
            kb_prompt = [
                {"role": "system", "content": "Out-of-context question detected."},
                {"role": "user", "content": message.message}
            ]
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=kb_prompt,
                llm_response=out_of_context_response,
                input_tokens=0,
                output_tokens=0
            )
            
            # Collect user question in conversation state and check limit
            chatbot_service = ChatbotService()
            question_result = chatbot_service.add_question_to_conversation(
                state,
                message.message  # user question
            )

            # Also add to position 6 field for entity updates
            chatbot_service.add_user_question_to_position_6(
                state["chatbot_id"],
                int(tenant_id),
                message.message
            )

            # Update state
            state["history"].append({"role": "assistant", "content": out_of_context_response})
            redis_service.store_conversation_state(conversation_id, state)

            # Check if this was the 5th question (limit reached)
            if question_result.get("limit_reached", False):
                # Auto-complete the conversation after 5th question
                logger.info(f"User reached 5-question limit for conversation {conversation_id}. Auto-completing chat.")

                # Add completion message to response
                completion_message = "\n\nYou've asked 5 questions, which is our limit for this session. Let me complete your information now."
                out_of_context_response += completion_message

                # Update state with completion message
                state["history"][-1]["content"] = out_of_context_response
                state["completed"] = True
                redis_service.store_conversation_state(conversation_id, state)

                # Update conversation in database
                update_conversation_in_db(db, conversation_id, state)

                # Trigger entity updates immediately
                try:
                    entity_details = state.get("entity_details", [])
                    collected_answers = state.get("answers", [])

                    if entity_details and collected_answers:
                        update_results = await chatbot_service.update_entities_after_conversation(
                            entity_details,
                            collected_answers,
                            int(tenant_id),
                            state.get("user_id", "unknown"),
                            state.get("chatbot_id"),
                            state  # Pass conversation state
                        )
                        logger.info(f"✅ Entity updates completed for out-of-context question: {update_results}")
                    else:
                        logger.warning("⚠️ No entities or answers to update for out-of-context question")
                except Exception as e:
                    logger.error(f"❌ Entity update failed for out-of-context question: {str(e)}")

                # Publish completion event
                await conversation_event_publisher.publish_conversation_completion(
                    chatbot_conversation_id=conversation_id,
                    completion_message=out_of_context_response,
                    charge=0
                )

                return {
                    "conversation_id": conversation_id,
                    "answer": out_of_context_response,
                    "nextQuestion": None,
                    "completed": True,
                    "is_knowledge_response": True,
                    "auto_completed": True,
                    "reason": "5_question_limit_reached"
                }

            # Publish out-of-context response event
            await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id=conversation_id,
                message=out_of_context_response,
                completed=False,
                charge=0
            )

            return {
                "conversation_id": conversation_id,
                "answer": out_of_context_response,
                "nextQuestion": None,
                "completed": False,
                "is_knowledge_response": True,
                "is_out_of_context": True
            }

        # Question is relevant - proceed with normal processing
        merged_context = "".join([result['content'] for result in search_results])
        merged_context = re.sub(r"\s+", " ", merged_context).strip()

        # Return knowledgebase results directly (no OpenAI processing)
        # Format the search results into a readable response
        response_parts = []
        for i, result in enumerate(search_results[:3], 1):  # Top 3 results
            response_parts.append(f"• {result['content'][:200]}...")  # First 200 chars of each result

        kb_response = "Based on our knowledge base, here's what I found:\n\n" + "\n".join(response_parts)

        # Add a follow-up question to encourage user to indicate when they're done
        follow_up_options = [
            "Is there anything else I can help you with?",
            "Do you have any other questions?",
            "What else would you like to know?",
            "Is there anything else you'd like to ask about?"
        ]

        import random
        follow_up = random.choice(follow_up_options)

        complete_response = f"{kb_response}\n\n{follow_up}"

        # Store conversation turn (user input + knowledgebase response, no AI generation)
        kb_prompt = [
            {"role": "system", "content": "Returning knowledgebase search results directly without AI processing."},
            {"role": "user", "content": message.message}
        ]
        store_conversation_turn(
            db, conversation_id, tenant_id,
            llm_prompt=kb_prompt,
            llm_response=complete_response,
            input_tokens=0,  # No AI generation
            output_tokens=0  # No AI generation
        )
        # Collect user question in conversation state and check limit
        chatbot_service = ChatbotService()
        question_result = chatbot_service.add_question_to_conversation(
            state,
            message.message  # user question
        )

        # Also add to position 6 field for entity updates (but don't use for limit checking)
        chatbot_service.add_user_question_to_position_6(
            state["chatbot_id"],
            int(tenant_id),
            message.message
        )

        # Update state
        state["history"].append({"role": "assistant", "content": complete_response})
        redis_service.store_conversation_state(conversation_id, state)

        # Check if this was the 5th question (limit reached)
        if question_result.get("limit_reached", False):
            # Auto-complete the conversation after 5th question
            logger.info(f"User reached 5-question limit for conversation {conversation_id}. Auto-completing chat.")

            # Add completion message to response
            completion_message = "\n\nYou've asked 5 questions, which is our limit for this session. Let me complete your information now."
            complete_response += completion_message

            # Update state with completion message
            state["history"][-1]["content"] = complete_response
            state["completed"] = True
            redis_service.store_conversation_state(conversation_id, state)

            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state)

            # Trigger entity updates immediately
            try:
                entity_details = state.get("entity_details", [])
                collected_answers = state.get("answers", [])

                if entity_details and collected_answers:
                    update_results = await chatbot_service.update_entities_after_conversation(
                        entity_details,
                        collected_answers,
                        int(tenant_id),
                        state.get("user_id", "unknown"),
                        state.get("chatbot_id"),
                        state  # Pass conversation state
                    )
                    logger.info(f"Auto-triggered entity updates for conversation {conversation_id}: {update_results}")
            except Exception as e:
                logger.error(f"Error auto-triggering entity updates: {str(e)}")

            return {
                "conversation_id": conversation_id,
                "answer": complete_response,
                "nextQuestion": None,
                "completed": True,  # Mark as completed
                "is_knowledge_response": True,
                "auto_completed": True,
                "reason": "5_question_limit_reached"
            }

        # Update conversation in database
        update_conversation_in_db(db, conversation_id, state)

        return {
            "conversation_id": conversation_id,
            "answer": complete_response,
            "nextQuestion": follow_up,
            "completed": False,  # Not completed until user indicates they're done
            "is_knowledge_response": True
        }
    else:
        # No relevant information found
        base_response = "I don't have specific information about that."
        follow_up_options = [
            "Is there anything else I can help you with?",
            "Do you have any other questions?",
            "What else would you like to know?",
            "Is there anything else you'd like to ask about?"
        ]

        import random
        follow_up = random.choice(follow_up_options)
        ai_response = f"{base_response} {follow_up}"

        # Store conversation turn (user input + standard response, no AI generation)
        # Create a mock prompt for this non-AI response
        no_info_prompt = [
            {"role": "system", "content": "You are a helpful assistant. Respond when no relevant information is found in the knowledge base."},
            {"role": "user", "content": message.message}
        ]
        store_conversation_turn(
            db, conversation_id, tenant_id,
            llm_prompt=no_info_prompt,
            llm_response=ai_response,
            input_tokens=0,  # No AI generation
            output_tokens=0  # No AI generation
        )
        
        # Collect user question in conversation state and check limit
        chatbot_service = ChatbotService()
        question_result = chatbot_service.add_question_to_conversation(
            state,
            message.message  # user question
        )

        # Also add to position 6 field for entity updates (but don't use for limit checking)
        chatbot_service.add_user_question_to_position_6(
            state["chatbot_id"],
            int(tenant_id),
            message.message
        )

        # Update state
        state["history"].append({"role": "assistant", "content": ai_response})
        redis_service.store_conversation_state(conversation_id, state)

        # Check if this was the 5th question (limit reached)
        if question_result.get("limit_reached", False):
            # Auto-complete the conversation after 5th question
            logger.info(f"User reached 5-question limit for conversation {conversation_id}. Auto-completing chat.")

            # Add completion message to response
            completion_message = "\n\nYou've asked 5 questions, which is our limit for this session. Let me complete your information now."
            ai_response += completion_message

            # Update state with completion message
            state["history"][-1]["content"] = ai_response
            state["completed"] = True
            redis_service.store_conversation_state(conversation_id, state)

            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state)

            # Trigger entity updates immediately
            try:
                entity_details = state.get("entity_details", [])
                collected_answers = state.get("answers", [])

                if entity_details and collected_answers:
                    update_results = await chatbot_service.update_entities_after_conversation(
                        entity_details,
                        collected_answers,
                        int(tenant_id),
                        state.get("user_id", "unknown"),
                        state.get("chatbot_id"),
                        state  # Pass conversation state
                    )
                    logger.info(f"Auto-triggered entity updates for conversation {conversation_id}: {update_results}")
            except Exception as e:
                logger.error(f"Error auto-triggering entity updates: {str(e)}")

            return {
                "conversation_id": conversation_id,
                "answer": ai_response,
                "nextQuestion": None,
                "completed": True,  # Mark as completed
                "is_knowledge_response": True,
                "auto_completed": True,
                "reason": "5_question_limit_reached"
            }

        # Update conversation in database
        update_conversation_in_db(db, conversation_id, state)
        
        return {
            "conversation_id": conversation_id,
            "answer": ai_response,
            "nextQuestion": follow_up,
            "completed": False,  # Not completed until user indicates they're done
            "is_knowledge_response": True
        }
    print("------Conversion is not complete")
    # If not completed, continue with the normal flow
    # Get the current question being asked (last question in asked_questions)
    if not state.get("asked_questions"):
        raise HTTPException(status_code=500, detail="No questions have been asked yet")

    current_question = state["asked_questions"][-1]

    # Check if the message content is valid (not None, empty, whitespace, or image-related content)
    def is_valid_text_answer(message_content):
        """Check if the message content is a valid text answer"""
        if not message_content or not message_content.strip():
            return False
        
        # Check for common image-related patterns
        message_lower = message_content.lower().strip()
        
        # Common image file extensions
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico', '.tiff', '.tif']
        if any(message_lower.endswith(ext) for ext in image_extensions):
            return False
        
        # Check for base64 image patterns
        if message_lower.startswith('data:image/') or 'base64' in message_lower:
            return False
        
        # Allow common short answers that are clearly valid responses
        common_short_answers = ['yes', 'no', 'ok', 'okay', 'yeah', 'nope', 'nah', 'sure', 'maybe']
        if message_lower in common_short_answers:
            return True
        
        # Check for very short content that might be image metadata
        # Allow emojis and single characters that are clearly not image-related
        if len(message_content.strip()) < 3:
            # Check if it's an emoji or special character
            if len(message_content.strip()) == 1:
                # Single characters are allowed (emojis, punctuation, etc.)
                pass
            else:
                # Very short content (2 characters) might be image metadata
                # But we already checked for common short answers above
                return False
        
        # Check for common image-related keywords (only if they are standalone or very short)
        image_keywords = ['image', 'photo', 'picture', 'img', 'pic', 'upload', 'file', 'attachment']
        if any(keyword == message_lower for keyword in image_keywords):
            return False
        
        # Check if content looks like a file path or URL
        if '/' in message_content or '\\' in message_content or 'http' in message_lower:
            return False
        
        return True
    
    if not is_valid_text_answer(message.message):
        # User provided invalid content, ask the previous question again
        logger.info(f"📸 User provided invalid content for question: {current_question['question']}")
        logger.info(f"📸 Invalid message content: '{message.message}'")
        
        # Create a response asking for the same question again
        repeat_question_response = f"I received invalid content. Please provide a text answer for: {current_question['question']}"
        
        # Update state with the repeat question
        state["history"].append({"role": "assistant", "content": repeat_question_response})
        redis_service.store_conversation_state(conversation_id, state)
        
        # Update conversation in database
        update_conversation_in_db(db, conversation_id, state)
        
        return {
            "conversation_id": conversation_id,
            "answer": repeat_question_response,
            "nextQuestion": current_question["question"],
            "image_upload_detected": True,
            "message": "Please provide a text answer instead of an image"
        }
    
    # Store the answer for the current question with field mapping for entity updates
    # Handle multiple entity fields per question
    if "entity_fields" in current_question:
        # New format: question maps to multiple entity fields
        for entity_field in current_question["entity_fields"]:
            state["answers"].append({
                "question_id": current_question["id"],
                "question": current_question["question"],
                "answer": message.message,
                "field_name": entity_field["name"],  # Field name for entity update
                "entity_type": entity_field["entity_type"]  # Entity type this question applies to
            })
            logger.info(f"📝 Stored answer for {entity_field['entity_type']}: {entity_field['name']} = {message.message}")
    else:
        # Legacy format: single entity field per question
        state["answers"].append({
            "question_id": current_question["id"],
            "question": current_question["question"],
            "answer": message.message,
            "field_name": current_question.get("field_name"),  # Field name for entity update
            "entity_type": current_question.get("entity_type")  # Entity type this question applies to
        })

    # Track credit usage for this question-answer interaction
    chatbot_service = ChatbotService()
    has_knowledgebase = chatbot_service.has_knowledgebase(state["chatbot_id"], int(tenant_id))

    track_credit_usage(
        db=db,
        chatbot_id=state["chatbot_id"],
        conversation_id=conversation_id,
        tenant_id=tenant_id,
        question=current_question["question"],
        answer=message.message,
        has_knowledgebase=has_knowledgebase
    )

    # Check if we have more questions remaining
    if state.get("remaining_questions"):
        # Use LLM to select the next question
        print(f"DEBUG: Selecting next question from {len(state['remaining_questions'])} remaining questions")
        next_question, input_tokens, output_tokens, model = es_service.select_next_question(
            state["history"],
            state["remaining_questions"],
            state["answers"]
        )

        # Fallback: if LLM doesn't select a question, use the first available question
        if not next_question and state["remaining_questions"]:
            next_question = state["remaining_questions"][0]
            input_tokens, output_tokens, model = 0, 0, "fallback"
            print(f"DEBUG: LLM didn't select next question, using fallback: {next_question}")

        print(f"DEBUG: Selected next question: {next_question}")

        if next_question:
            # Move the selected question from remaining to asked
            state["remaining_questions"] = [q for q in state["remaining_questions"] if q["id"] != next_question["id"]]
            state["asked_questions"].append(next_question)

            # Create a prompt for OpenAI to generate a natural transition
            prompt = [
                {"role": "system", "content": "You are a helpful assistant collecting information from users. Based on the conversation history, acknowledge the user's response and ask the next question in a natural way."},
                {"role": "user", "content": f"Conversation history: {json.dumps(state['answers'])}\n\nNext question to ask: {next_question['question']}\n\nGenerate a natural response that acknowledges what the user just said and then asks the next question."}
            ]

            ai_response, transition_input_tokens, transition_output_tokens, transition_model = es_service.generate_chat_response(prompt, max_tokens=150)

            # Store conversation turn (user input + AI response for next question)
            store_conversation_turn(
                db, conversation_id, tenant_id,
                llm_prompt=prompt,  # Use the actual prompt sent to LLM
                llm_response=ai_response,
                input_tokens=transition_input_tokens,
                output_tokens=transition_output_tokens
            )

            # Update state
            state["history"].append({"role": "assistant", "content": ai_response})
            redis_service.store_conversation_state(conversation_id, state)

            # Update conversation in database
            update_conversation_in_db(db, conversation_id, state)

            return {
                "conversation_id": conversation_id,
                "answer": ai_response,
                "nextQuestion": next_question["question"]
            }
        else:
            # No more questions could be selected, proceed to completion
            pass

    # All questions answered or no more questions available
    # Check if chatbot has knowledgebase to determine conversation flow
    chatbot_service = ChatbotService()
    has_knowledgebase = chatbot_service.has_knowledgebase(state["chatbot_id"], int(tenant_id))

    if has_knowledgebase:
        # If knowledgebase exists, provide a simple transition message (no OpenAI)
        transition_message = "Thank you for providing all the information! I'm here to help if you have any questions. Feel free to ask me anything from our knowledge base."
        next_question = "What can I help you with today? Feel free to ask me any questions!"
        completed = False  # NOT completed yet - will be completed when user indicates they're done

        # Store simple transition (no AI generation)
        input_tokens, output_tokens, model = 0, 0, "static_message"
    else:
        # If no knowledgebase, end conversation with simple thank you message (no OpenAI)
        chatbot = chatbot_service.get_chatbot_for_conversation(state["chatbot_id"], int(tenant_id))
        thank_you_message = chatbot.thank_you_message if chatbot and chatbot.thank_you_message else "Thank you for your time!"

        transition_message = f"{thank_you_message} We have collected all the necessary information. Have a great day!"
        next_question = None  # No next question since conversation is ending

        # Store simple message (no AI generation)
        input_tokens, output_tokens, model = 0, 0, "static_message"
        completed = True

    # Store conversation turn (user input + AI transition message)
    store_conversation_turn(
        db, conversation_id, tenant_id,
        llm_prompt=prompt,  # Use the actual prompt sent to LLM
        llm_response=transition_message,
        input_tokens=input_tokens,
        output_tokens=output_tokens
    )

    # Update state to indicate transition to knowledge phase (but not completed yet)
    if has_knowledgebase:
        state["completed"] = True  # Transition to knowledge phase
        state["questions_completed"] = True  # Mark questions as done
    else:
        state["completed"] = True  # End conversation immediately
        state["ended"] = True

    state["history"].append({"role": "assistant", "content": transition_message})
    redis_service.store_conversation_state(conversation_id, state)

    # Update conversation in database - do NOT mark as completed yet if has knowledgebase
    if has_knowledgebase:
        update_conversation_in_db(db, conversation_id, state, completed=False)  # Not completed yet - waiting for user to indicate they're done
    else:
        update_conversation_in_db(db, conversation_id, state, completed=True)  # Complete immediately

    return {
        "conversation_id": conversation_id,
        "answer": transition_message,
        "nextQuestion": next_question,
        "completed": completed,
        "answers": state["answers"]
    }

# Get chatbot by ID endpoint
@router.get("/{chatbot_id}")
async def get_chatbot(
    request: Request,
    chatbot_id: str
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to get the chatbot with details
    chatbot_service = ChatbotService()
    chatbot_data = chatbot_service.get_chatbot_with_details(chatbot_id, int(tenant_id))
    return format_chatbot_response(chatbot_data)




@router.post("/{chatbot_id}/questions")
async def configure_chatbot_questions(
    chatbot_id: str,
    request: Request,
    questions: List[QuestionCreate]
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    # Use ChatbotService to configure questions
    chatbot_service = ChatbotService()
    result = chatbot_service.configure_chatbot_questions(chatbot_id, questions, tenant_id, user_id, token)
    return format_response(result)

@router.post("/{chatbot_id}/questions/legacy")
async def configure_chatbot_questions_legacy(
    chatbot_id: str,
    request: Request,
    questions: List[QuestionCreateLegacy]
):
    """Legacy endpoint for backward compatibility"""
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    # Use ChatbotService to configure questions
    chatbot_service = ChatbotService()
    return chatbot_service.configure_chatbot_questions(chatbot_id, questions, tenant_id, user_id, token)

@router.put("/{chatbot_id}/questions")
async def update_all_chatbot_questions(
    chatbot_id: str,
    request: Request,
    questions: List[QuestionCreate]
):
    """Update all questions for a chatbot (replaces existing questions)"""
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    # Use ChatbotService to update all questions
    chatbot_service = ChatbotService()
    result = chatbot_service.update_all_chatbot_questions(chatbot_id, questions, int(tenant_id), user_id, token)
    return format_response(result)

@router.put("/{chatbot_id}/questions/{question_id}")
async def update_chatbot_question(
    chatbot_id: str,
    question_id: str,
    request: Request,
    question_data: QuestionUpdate
):
    """Update a specific question for a chatbot"""
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    # Use ChatbotService to update the question
    chatbot_service = ChatbotService()
    result = chatbot_service.update_chatbot_question(chatbot_id, question_id, question_data, int(tenant_id), token)
    return format_response(result)

@router.post("/{chatbot_id}/knowledgebase")
async def add_chatbot_knowledgebase(
    chatbot_id: str,
    request: Request,
    files: List[UploadFile] = File(...)
):
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    # Use ChatbotService to handle multiple file uploads (blocking/synchronous)
    chatbot_service = ChatbotService()
    result = await chatbot_service.upload_multiple_knowledgebase_files(
        chatbot_id, files, tenant_id, user_id, token
    )
    return format_response(result)


@router.post("/{chatbot_id}/knowledgebase/enhanced", status_code=status.HTTP_202_ACCEPTED, response_model=BackgroundTaskResponse)
async def add_chatbot_knowledgebase_enhanced(
    chatbot_id: str,
    request: Request,
    background_tasks: BackgroundTasks
):
    """
    Enhanced knowledgebase upload endpoint that handles form data with metadata.

    This endpoint implements a "replace all" strategy - the submitted files completely
    replace the current knowledgebase. Files not included will be removed.

    Supports three scenarios:
    1. New file upload (binary files)
    2. Edit mode (existing document IDs)
    3. Empty payload (removes all files)

    Expected form data format for new uploads:
    files[0][file]: (binary)
    files[0][documentName]: filename.pdf
    files[0][documentSize]: 12345
    files[0][documentType]: application/pdf

    Expected form data format for edit mode:
    files[0][documentId]: 61654517-6166-405d-b416-2049cde5aeb5
    files[0][documentName]: filename.pdf
    files[0][documentSize]: 12345
    files[0][documentType]: pdf

    Empty payload removes all knowledgebase files.
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Get the authorization token from the request
    token = request.headers.get("authorization", "").replace("Bearer ", "")

    try:
        form_data = await request.form()
        files_with_metadata = []
        file_indices = set()
        for key in form_data.keys():
            if key.startswith("files[") and (key.endswith("][file]") or key.endswith("][documentId]")):
                index = key.split("[")[1].split("]")[0]
                file_indices.add(index)
        for index in sorted(file_indices):
            file_key = f"files[{index}][file]"
            document_id_key = f"files[{index}][documentId]"
            name_key = f"files[{index}][documentName]"
            size_key = f"files[{index}][documentSize]"
            type_key = f"files[{index}][documentType]"
            if file_key in form_data:
                file = form_data[file_key]
                document_name = form_data.get(name_key, file.filename if hasattr(file, 'filename') else f"file_{index}")
                document_size = int(form_data.get(size_key, 0))
                document_type = form_data.get(type_key, "application/octet-stream")
                enhanced_file = {
                    "file": file,
                    "document_name": document_name,
                    "document_size": document_size,
                    "document_type": document_type,
                    "index": index,
                    "is_existing_document": False
                }
                files_with_metadata.append(enhanced_file)
            elif document_id_key in form_data:
                document_id = form_data.get(document_id_key)
                document_name = form_data.get(name_key, f"document_{index}")
                document_size = int(form_data.get(size_key, 0))
                document_type = form_data.get(type_key, "pdf")
                enhanced_file = {
                    "document_id": document_id,
                    "document_name": document_name,
                    "document_size": document_size,
                    "document_type": document_type,
                    "index": index,
                    "is_existing_document": True
                }
                files_with_metadata.append(enhanced_file)

        chatbot_service = ChatbotService()
        
        # Generate a unique task ID for tracking
        task_id = str(uuid.uuid4())
        
        # Import the background task service
        from app.services.background_task_service import background_task_service
        
        # Define the background task function with improved error handling
        async def process_upload():
            try:
                # Log the start of processing
                logger.info(f"Starting knowledgebase processing for chatbot {chatbot_id}, task_id: {task_id}")
                
                if not files_with_metadata:
                    await chatbot_service.upload_multiple_knowledgebase_files_enhanced(
                        chatbot_id, [], tenant_id, user_id, token
                    )
                    logger.info(f"Completed knowledgebase cleanup for chatbot {chatbot_id}, task_id: {task_id}")
                else:
                    await chatbot_service.upload_multiple_knowledgebase_files_enhanced(
                        chatbot_id, files_with_metadata, tenant_id, user_id, token
                    )
                    logger.info(f"Completed knowledgebase upload for chatbot {chatbot_id}, task_id: {task_id}, files: {len(files_with_metadata)}")
                    
            except Exception as e:
                # Log the error with task details
                error_msg = f"Error in knowledgebase processing for chatbot {chatbot_id}, task_id: {task_id}: {str(e)}"
                logger.error(error_msg, exc_info=True)

        # Submit the task to the background task service (non-blocking)
        background_task_id = background_task_service.submit_task(
            process_upload,
            task_name=f"knowledgebase_upload_{chatbot_id}"
        )
        
        # Return detailed response with task information
        return {
            "status": "accepted",
            "message": "Knowledgebase upload started. Processing in background.",
            "task_id": background_task_id,
            "chatbot_id": chatbot_id,
            "files_count": len(files_with_metadata),
            "processing_type": "cleanup" if not files_with_metadata else "upload"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing form data: {str(e)}")


@router.get("/{chatbot_id}/knowledgebase")
async def get_chatbot_knowledgebase(
    chatbot_id: str,
    request: Request
):
    """Get all knowledgebase documents for a chatbot"""
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to get knowledgebase documents
    chatbot_service = ChatbotService()
    return chatbot_service.get_chatbot_knowledgebase(chatbot_id, int(tenant_id))

@router.delete("/{chatbot_id}/knowledgebase/{document_id}")
async def remove_chatbot_knowledgebase_document(
    chatbot_id: str,
    document_id: str,
    request: Request
):
    """Remove a specific document from chatbot knowledgebase"""
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to remove the document
    chatbot_service = ChatbotService()
    return chatbot_service.remove_knowledgebase_document(chatbot_id, document_id, int(tenant_id))

@router.put("/{chatbot_id}/knowledgebase")
async def update_chatbot_knowledgebase(
    chatbot_id: str,
    request: Request,
    knowledgebase_data: KnowledgebaseUpdateRequest
):
    """
    Update chatbot knowledgebase with enhanced document handling

    This endpoint allows updating the knowledgebase with document details including:
    - documentId: Existing document ID (null for new documents)
    - documentName: Name of the document
    - documentSize: Size of the document in bytes
    - documentType: Type/extension of the document

    The system will:
    - Remove documents not in the new list from ES and database
    - Add new documents (with documentId=null)
    - Update metadata for existing documents
    - Synchronize changes with Elasticsearch vectors
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Use ChatbotService to update knowledgebase
    chatbot_service = ChatbotService()
    return chatbot_service.update_chatbot_knowledgebase(chatbot_id, knowledgebase_data, int(tenant_id))

@router.get("/background-tasks/{task_id}")
async def get_background_task_status(
    task_id: str,
    request: Request
):
    """
    Get the status of a background task
    
    Args:
        task_id: The task ID returned from the background task
        request: FastAPI request object
        
    Returns:
        Dict containing task status information
    """
    from app.services.background_task_service import background_task_service
    
    task_status = background_task_service.get_task_status(task_id)
    
    if not task_status:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return task_status

@router.get("/background-tasks")
async def list_background_tasks(
    request: Request,
    status: Optional[str] = None
):
    """
    List all background tasks
    
    Args:
        request: FastAPI request object
        status: Optional filter by status (pending, running, completed, failed, cancelled)
        
    Returns:
        Dict containing all task information
    """
    from app.services.background_task_service import background_task_service
    
    all_tasks = background_task_service.get_all_tasks()
    
    if status:
        filtered_tasks = {
            task_id: task_info 
            for task_id, task_info in all_tasks.items() 
            if task_info.get("status") == status
        }
        return filtered_tasks
    
    return all_tasks

@router.delete("/background-tasks/{task_id}")
async def cancel_background_task(
    task_id: str,
    request: Request
):
    """
    Cancel a background task
    
    Args:
        task_id: The task ID to cancel
        request: FastAPI request object
        
    Returns:
        Dict containing cancellation result
    """
    from app.services.background_task_service import background_task_service
    
    cancelled = background_task_service.cancel_task(task_id)
    
    if not cancelled:
        raise HTTPException(status_code=404, detail="Task not found or cannot be cancelled")
    
    return {"message": "Task cancelled successfully", "task_id": task_id}

@router.post("/background-tasks/cleanup")
async def cleanup_background_tasks(
    request: Request,
    max_age_hours: int = 24
):
    """
    Clean up completed background tasks older than the specified age
    
    Args:
        request: FastAPI request object
        max_age_hours: Maximum age in hours for completed tasks (default: 24)
        
    Returns:
        Dict containing cleanup result
    """
    from app.services.background_task_service import background_task_service
    
    cleaned_count = background_task_service.cleanup_completed_tasks(max_age_hours)
    
    return {
        "message": f"Cleaned up {cleaned_count} completed background tasks",
        "cleaned_count": cleaned_count,
        "max_age_hours": max_age_hours
    }

@router.get("/credit-usage", response_model=List[CreditUsageResponse])
async def get_credit_usage(
    request: Request,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """
    Get credit usage across all chatbots for the tenant with optional date filtering

    Args:
        start_date: Optional start date for filtering (YYYY-MM-DD)
        end_date: Optional end date for filtering (YYYY-MM-DD)

    Returns:
        List of credit usage records across all chatbots
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Build query for credit usage across all chatbots for the tenant
    query = db.query(ChatbotCreditUsage).filter(
        ChatbotCreditUsage.tenant_id == tenant_id
    )

    # Apply date filters if provided
    if start_date:
        start_datetime = datetime.combine(start_date, datetime.min.time())
        query = query.filter(ChatbotCreditUsage.timestamp >= start_datetime)

    if end_date:
        end_datetime = datetime.combine(end_date, datetime.max.time())
        query = query.filter(ChatbotCreditUsage.timestamp <= end_datetime)

    # Order by timestamp descending (most recent first)
    credit_usage_records = query.order_by(ChatbotCreditUsage.timestamp.desc()).all()

    # Convert to response format
    return [
        CreditUsageResponse(
            id=record.id,
            chatbot_id=record.chatbot_id,
            conversation_id=record.conversation_id,
            question=record.question,
            answer=record.answer,
            credits_used=record.credits_used,
            has_knowledgebase=record.has_knowledgebase,
            timestamp=record.timestamp
        )
        for record in credit_usage_records
    ]

@router.get("/credit-summary")
async def get_credit_summary(
    request: Request,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """
    Get credit usage summary across all chatbots for the tenant

    Args:
        start_date: Optional start date for filtering (YYYY-MM-DD)
        end_date: Optional end date for filtering (YYYY-MM-DD)

    Returns:
        Summary of credit usage including total credits, question count, etc. across all chatbots
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id

    # Build query for credit usage across all chatbots for the tenant
    query = db.query(ChatbotCreditUsage).filter(
        ChatbotCreditUsage.tenant_id == tenant_id
    )

    # Apply date filters if provided
    if start_date:
        start_datetime = datetime.combine(start_date, datetime.min.time())
        query = query.filter(ChatbotCreditUsage.timestamp >= start_datetime)

    if end_date:
        end_datetime = datetime.combine(end_date, datetime.max.time())
        query = query.filter(ChatbotCreditUsage.timestamp <= end_datetime)

    credit_usage_records = query.all()

    # Calculate summary statistics
    total_credits = sum(record.credits_used for record in credit_usage_records)
    total_questions = len(credit_usage_records)
    knowledgebase_questions = len([r for r in credit_usage_records if r.has_knowledgebase])
    non_knowledgebase_questions = total_questions - knowledgebase_questions

    return {
        "tenant_id": tenant_id,
        "period": {
            "start_date": start_date.isoformat() if start_date else None,
            "end_date": end_date.isoformat() if end_date else None
        },
        "summary": {
            "total_credits_used": total_credits,
            "total_questions_answered": total_questions,
            "knowledgebase_questions": knowledgebase_questions,
            "non_knowledgebase_questions": non_knowledgebase_questions,
            "average_credits_per_question": round(total_credits / total_questions, 2) if total_questions > 0 else 0
        }
    }

# Rule-based chatbot flow endpoints
@router.post("/{chatbot_id}/rules")
async def create_rule_based_flow(
    request: Request,
    chatbot_id: str,
    flow_data: RuleBasedChatbotFlow,
    db: Session = Depends(get_db)
):
    """
    Create or update the flow for a rule-based chatbot
    
    This endpoint validates the flow first, then creates or updates it.
    For rule-based chatbots, it also checks for active chatbots and either throws
    an exception or directly activates the chatbot.
    """
    # Get auth context from request state
    auth_context = request.state.auth_context
    tenant_id = auth_context.tenant_id
    user_id = auth_context.user_id

    # Verify chatbot exists and belongs to tenant
    chatbot = db.query(Chatbot).filter(
        Chatbot.id == chatbot_id,
        Chatbot.tenant_id == tenant_id
    ).first()
    
    if not chatbot:
        raise ChatbotNotFoundException(chatbot_id=chatbot_id)
    
    if chatbot.type != "RULE":
        raise HTTPException(status_code=400, detail="This endpoint is only for rule-based chatbots")

    # Note: Multiple active chatbots per account are now allowed
    # The workflow event listener determines which chatbot to trigger based on the event payload

    # Create the flow - validation happens inside the service
    rule_based_service = RuleBasedChatbotService()
    result = rule_based_service.create_flow(db, chatbot_id, tenant_id, flow_data)
    
    # If no active chatbot exists, directly move this RULE-based chatbot to active and publish event
    # This logic only applies to RULE-based chatbots, not AI-based chatbots
    if chatbot.type == "RULE" and chatbot.connected_account_id and chatbot.status == "DRAFT":
        # Update chatbot status to ACTIVE
        chatbot.status = "ACTIVE"
        chatbot.updated_by = user_id
        db.commit()
        db.refresh(chatbot)
        
        logger.info(f"Rule-based chatbot {chatbot_id} automatically activated")
        
        # Publish chatbot status updated event
        try:
            from app.services.chatbot_event_publisher import ChatbotEventPublisher
            event_publisher = ChatbotEventPublisher()
            
            # Ensure exchange exists
            await event_publisher.ensure_exchange_exists()
            
            # Publish status updated event
            await event_publisher.publish_status_updated_event(
                status="ACTIVE",
                connected_account_id=chatbot.connected_account_id,
                connected_account_name=chatbot.connected_account_display_name,
                chatbot_id=chatbot_id,
                tenant_id=tenant_id
            )
            
            logger.info(f"Published status updated event for rule-based chatbot {chatbot_id}")
        except Exception as e:
            logger.error(f"Failed to publish status updated event for rule-based chatbot {chatbot_id}: {str(e)}")
            # Don't fail the flow creation if event publishing fails
    
    return result
