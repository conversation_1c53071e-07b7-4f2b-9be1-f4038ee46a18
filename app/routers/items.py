from fastapi import APIRouter, Depends, HTTPException
from app.dependencies import get_auth_context
from app.database import get_database_health, test_database_connection
import logging

router = APIRouter(
    prefix="/v1/items",
    tags=["items"],
    dependencies=[Depends(get_auth_context)],
    responses={404: {"description": "Not found"}},
)

logger = logging.getLogger(__name__)

@router.get("/")
async def read_items():
    return [{"name": "Item 1"}, {"name": "Item 2"}]

# Health check endpoints (no auth required)
@router.get("/health", include_in_schema=False)
async def health_check():
    """Health check endpoint to monitor service status"""
    try:
        # Test database connection
        db_health = get_database_health()
        
        return {
            "status": "healthy" if db_health["status"] == "healthy" else "unhealthy",
            "database": db_health,
            "service": "whatsapp_chatbot",
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "service": "whatsapp_chatbot",
            "version": "1.0.0"
        }

@router.get("/health/database", include_in_schema=False)
async def database_health_check():
    """Detailed database health check"""
    try:
        health_status = get_database_health()
        return health_status
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database health check failed: {str(e)}")

@router.post("/health/database/test", include_in_schema=False)
async def test_database():
    """Test database connection manually"""
    try:
        success = test_database_connection()
        if success:
            return {"status": "success", "message": "Database connection test passed"}
        else:
            return {"status": "failed", "message": "Database connection test failed"}
    except Exception as e:
        logger.error(f"Database test failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database test failed: {str(e)}")