"""
Custom Exceptions for WhatsApp Chatbot Application

This module defines all custom exceptions used throughout the application.
All error codes start with '041' for chatbot-specific errors.
"""

import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

class BaseChatbotException(Exception):
    """Base exception class for all chatbot-related exceptions."""
    
    def __init__(self, message: str, error_code: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary format."""
        return {
            "errorCode": self.error_code,
            "message": self.message,
            "success": False,
            "details": self.details
        }
    
    def to_error_resource(self):
        """Convert to ErrorResource format matching Java structure."""
        # Import here to avoid circular dependency
        from app.exceptions import ErrorResource
        error_resource = ErrorResource(self.error_code, self.message)
        # Add a general field error for the exception
        error_resource.add_field_error("general", self.message)
        return error_resource
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}: {self.message} (Code: {self.error_code})"

# Authentication and Authorization Errors (041001-041009)
class AuthenticationError(BaseChatbotException):
    """Raised when authentication fails (e.g., invalid token, access denied)."""
    
    def __init__(self, message: str = "JWT Authentication failed", error_code: str = "041001", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, details)

class TokenLimitExceededException(BaseChatbotException):
    """Raised when token limit is exceeded."""
    
    def __init__(self, message: str = "Token Limit Exceeded", error_code: str = "041002", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, details)

class RateLimitExceededException(BaseChatbotException):
    """Raised when rate limit is exceeded."""
    
    def __init__(self, message: str = "Rate Limit Exceeded", error_code: str = "041003", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, details)

class InsufficientPermissionsException(BaseChatbotException):
    """Raised when user does not have sufficient permissions."""
    
    def __init__(self, permission: str = "permission", message: str = "You do not have sufficient permissions for this action", error_code: str = "041004", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["permission"] = permission
        super().__init__(message, error_code, details)

# Resource and Data Errors (041010-041019)
class ResourceNotFoundException(BaseChatbotException):
    """Raised when a resource is not found."""
    
    def __init__(self, resource_type: str = "resource", resource_id: Optional[str] = None, message: str = "Resource not found", error_code: str = "041010", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["resourceType"] = resource_type
        if resource_id:
            details["resourceId"] = resource_id
        super().__init__(message, error_code, details)

class ChatbotNotFoundException(ResourceNotFoundException):
    """Raised when a chatbot is not found."""
    
    def __init__(self, chatbot_id: str, message: str = "Chatbot not found", error_code: str = "041011", details: Optional[Dict[str, Any]] = None):
        super().__init__("chatbot", chatbot_id, message, error_code, details)

class ConversationNotFoundException(ResourceNotFoundException):
    """Raised when a conversation is not found."""
    
    def __init__(self, conversation_id: str, message: str = "Conversation not found", error_code: str = "041012", details: Optional[Dict[str, Any]] = None):
        super().__init__("conversation", conversation_id, message, error_code, details)

class EntityNotFoundException(ResourceNotFoundException):
    """Raised when an entity is not found."""
    
    def __init__(self, entity_type: str, entity_id: str, message: str = "Entity not found", error_code: str = "041013", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["entityType"] = entity_type
        details["entityId"] = entity_id
        super().__init__("entity", entity_id, message, error_code, details)

# Database and Storage Errors (041020-041029)
class DatabaseConnectionError(BaseChatbotException):
    """Raised when database connection fails."""
    
    def __init__(self, message: str = "Database connection failed", error_code: str = "041020", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, details)

class DatabaseQueryError(BaseChatbotException):
    """Raised when a database query fails."""
    
    def __init__(self, query: str, message: str = "Database query failed", error_code: str = "041021", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["query"] = query
        super().__init__(message, error_code, details)

class RedisConnectionError(BaseChatbotException):
    """Raised when Redis connection fails."""
    
    def __init__(self, message: str = "Redis connection failed", error_code: str = "041022", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, details)

class ElasticsearchConnectionError(BaseChatbotException):
    """Raised when Elasticsearch connection fails."""
    
    def __init__(self, message: str = "Elasticsearch connection failed", error_code: str = "041023", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, details)

# External Service Errors (041030-041039)
class ExternalAIModelException(BaseChatbotException):
    """Raised when an error occurs while interacting with an external AI model API."""
    
    def __init__(self, model_name: str = "AI model", message: str = "Error occurred while interacting with the external AI model", error_code: str = "041030", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["modelName"] = model_name
        super().__init__(message, error_code, details)

class IAMServiceException(BaseChatbotException):
    """Raised when IAM service interaction fails."""
    
    def __init__(self, operation: str, message: str = "IAM service operation failed", error_code: str = "041034", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["operation"] = operation
        super().__init__(message, error_code, details)

class IAMUserNotFoundException(BaseChatbotException):
    """Raised when user is not found in IAM service."""
    
    def __init__(self, tenant_id: str, user_id: str, message: str = "User not found in IAM service", error_code: str = "041035", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["tenantId"] = tenant_id
        details["userId"] = user_id
        super().__init__(message, error_code, details)

class IAMPermissionException(BaseChatbotException):
    """Raised when user has insufficient permissions from IAM."""
    
    def __init__(self, user_id: str, required_permissions: List[str], message: str = "User has insufficient permissions", error_code: str = "041036", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["userId"] = user_id
        details["requiredPermissions"] = required_permissions
        super().__init__(message, error_code, details)

class JWTTokenCreationException(BaseChatbotException):
    """Raised when JWT token creation fails."""
    
    def __init__(self, reason: str, message: str = "JWT token creation failed", error_code: str = "041037", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["reason"] = reason
        super().__init__(message, error_code, details)

class RabbitMQConnectionError(BaseChatbotException):
    """Raised when RabbitMQ connection fails."""
    
    def __init__(self, message: str = "RabbitMQ connection failed", error_code: str = "041031", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, details)

class RabbitMQPublishException(BaseChatbotException):
    """Raised when an error occurs while publishing a message to RabbitMQ."""
    
    def __init__(self, routing_key: str = "", message: str = "Error occurred while publishing to RabbitMQ", error_code: str = "041032", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["routingKey"] = routing_key
        super().__init__(message, error_code, details)

class S3ConnectionError(BaseChatbotException):
    """Raised when S3 connection fails."""
    
    def __init__(self, message: str = "S3 connection failed", error_code: str = "041033", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, details)

# File and Upload Errors (041040-041049)
class FileSizeLimitExceededException(BaseChatbotException):
    """Raised when the file size exceeds the allowed limit."""
    
    def __init__(self, file_size: int, max_size: int, message: str = "File size exceeds the allowed limit", error_code: str = "041040", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["fileSize"] = file_size
        details["maxSize"] = max_size
        super().__init__(message, error_code, details)

class FileTypeNotSupportedException(BaseChatbotException):
    """Raised when the file type is not supported."""
    
    def __init__(self, file_type: str, supported_types: list, message: str = "File type not supported", error_code: str = "041041", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["fileType"] = file_type
        details["supportedTypes"] = supported_types
        super().__init__(message, error_code, details)

class FileUploadError(BaseChatbotException):
    """Raised when file upload fails."""
    
    def __init__(self, file_name: str, message: str = "File upload failed", error_code: str = "041042", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["fileName"] = file_name
        super().__init__(message, error_code, details)

# Knowledge Base and Search Errors (041050-041059)
class NamespaceNotFoundException(BaseChatbotException):
    """Raised when namespace not found in vector database."""
    
    def __init__(self, namespace: str, message: str = "Namespace not found in vector database", error_code: str = "041050", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["namespace"] = namespace
        super().__init__(message, error_code, details)

class FiltersNotFoundException(BaseChatbotException):
    """Raised when filters not found in vector database."""
    
    def __init__(self, filters: str, message: str = "Filters not found in vector database", error_code: str = "041051", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["filters"] = filters
        super().__init__(message, error_code, details)

class KnowledgeBaseSearchError(BaseChatbotException):
    """Raised when knowledge base search fails."""
    
    def __init__(self, query: str, message: str = "Knowledge base search failed", error_code: str = "041052", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["query"] = query
        super().__init__(message, error_code, details)

# Conversation and Message Errors (041060-041069)
class ConversationStateError(BaseChatbotException):
    """Raised when conversation state is invalid."""
    
    def __init__(self, conversation_id: str, state: str, message: str = "Invalid conversation state", error_code: str = "041060", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["conversationId"] = conversation_id
        details["state"] = state
        super().__init__(message, error_code, details)

class MessageProcessingError(BaseChatbotException):
    """Raised when message processing fails."""
    
    def __init__(self, message_id: str, message: str = "Message processing failed", error_code: str = "041061", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["messageId"] = message_id
        super().__init__(message, error_code, details)

class ConversationTerminationError(BaseChatbotException):
    """Raised when conversation termination fails."""
    
    def __init__(self, conversation_id: str, message: str = "Conversation termination failed", error_code: str = "041062", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["conversationId"] = conversation_id
        super().__init__(message, error_code, details)

# Entity and Field Errors (041070-041079)
class EntityUpdateError(BaseChatbotException):
    """Raised when entity update fails."""
    
    def __init__(self, entity_type: str, entity_id: str, message: str = "Entity update failed", error_code: str = "041070", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["entityType"] = entity_type
        details["entityId"] = entity_id
        super().__init__(message, error_code, details)

class FieldMappingError(BaseChatbotException):
    """Raised when field mapping fails."""
    
    def __init__(self, field_name: str, entity_type: str, message: str = "Field mapping failed", error_code: str = "041071", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["fieldName"] = field_name
        details["entityType"] = entity_type
        super().__init__(message, error_code, details)

# Validation Errors (041080-041089)
class ValidationError(BaseChatbotException):
    """Raised when validation fails."""
    
    def __init__(self, field: str, value: Any, message: str = "Validation failed", error_code: str = "041080", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["field"] = field
        details["value"] = str(value)
        super().__init__(message, error_code, details)

class RequiredFieldMissingError(ValidationError):
    """Raised when a required field is missing."""
    
    def __init__(self, field: str, message: str = "Required field is missing", error_code: str = "041081", details: Optional[Dict[str, Any]] = None):
        super().__init__(field, None, message, error_code, details)

class InvalidFormatError(ValidationError):
    """Raised when data format is invalid."""
    
    def __init__(self, field: str, value: Any, expected_format: str, message: str = "Invalid data format", error_code: str = "041082", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["expectedFormat"] = expected_format
        super().__init__(field, value, message, error_code, details)

class ChatbotQuestionValidationError(ValidationError):
    """Raised when chatbot question configuration is invalid."""
    
    def __init__(self, validation_type: str, provided_count: int, max_allowed: int, message: str = "Chatbot question validation failed", error_code: str = "041083", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["validationType"] = validation_type
        details["providedCount"] = provided_count
        details["maxAllowed"] = max_allowed
        super().__init__("questions", f"{provided_count} questions", message, error_code, details)

class RestrictedFieldNameError(ValidationError):
    """Raised when trying to add a field with a restricted name."""
    
    def __init__(self, field_name: str, restricted_names: list, message: str = "Field name is restricted and cannot be used", error_code: str = "041089", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["fieldName"] = field_name
        details["restrictedNames"] = restricted_names
        super().__init__("field_name", field_name, message, error_code, details)

class FieldIdNameMismatchError(ValidationError):
    """Raised when field ID does not match the field name from sd-config API."""
    
    def __init__(self, field_id: int, provided_name: str, actual_name: str, entity_type: str, message: str = "Field ID does not match the field name", error_code: str = "041090", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["fieldId"] = field_id
        details["providedName"] = provided_name
        details["actualName"] = actual_name
        details["entityType"] = entity_type
        super().__init__("field_id_name_mismatch", f"{field_id}:{provided_name}", message, error_code, details)

# Configuration and Setup Errors (041091-041099)
class ConfigurationError(BaseChatbotException):
    """Raised when configuration is invalid."""
    
    def __init__(self, config_key: str, message: str = "Configuration error", error_code: str = "041091", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["configKey"] = config_key
        super().__init__(message, error_code, details)

class EnvironmentVariableError(ConfigurationError):
    """Raised when required environment variable is missing."""
    
    def __init__(self, env_var: str, message: str = "Required environment variable is missing", error_code: str = "041092", details: Optional[Dict[str, Any]] = None):
        super().__init__(env_var, message, error_code, details)

# Addon and Feature Errors (041100-041109)
class AddonNotPresentException(BaseChatbotException):
    """Raised when addon is not present."""
    
    def __init__(self, addon_name: str, message: str = None, error_code: str = "041100", details: Optional[Dict[str, Any]] = None):
        if message is None:
            message = f"You do not have {' '.join(addon_name.split('-'))} add-on present"
        details = details or {}
        details["addonName"] = addon_name
        super().__init__(message, error_code, details)

class FeatureNotEnabledException(BaseChatbotException):
    """Raised when a feature is not enabled."""
    
    def __init__(self, feature_name: str, message: str = "Feature is not enabled", error_code: str = "041101", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["featureName"] = feature_name
        super().__init__(message, error_code, details)

# Task and Workflow Errors (041110-041119)
class TaskFormFieldExtractionError(BaseChatbotException):
    """Raised when there is an error extracting task form fields."""
    
    def __init__(self, task_id: str, message: str = "Error extracting task form fields", error_code: str = "041110", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["taskId"] = task_id
        super().__init__(message, error_code, details)

class TaskCreationError(BaseChatbotException):
    """Raised when there is an error creating a task."""
    
    def __init__(self, task_data: Dict[str, Any], message: str = "Error creating task", error_code: str = "041111", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["taskData"] = task_data
        super().__init__(message, error_code, details)

class CallAnalysisInProgressException(BaseChatbotException):
    """Raised when call analysis is already in progress for the same call ID."""
    
    def __init__(self, call_id: str, message: str = "Call analysis is already in progress for this call ID", error_code: str = "041112", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["callId"] = call_id
        super().__init__(message, error_code, details)

# Generic Errors (041200-041299)
class InternalServerError(BaseChatbotException):
    """Raised when an internal server error occurs."""
    
    def __init__(self, message: str = "Internal server error", error_code: str = "041200", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, details)

class ServiceUnavailableError(BaseChatbotException):
    """Raised when a service is unavailable."""
    
    def __init__(self, service_name: str, message: str = "Service is unavailable", error_code: str = "041201", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["serviceName"] = service_name
        super().__init__(message, error_code, details)

class TimeoutError(BaseChatbotException):
    """Raised when an operation times out."""
    
    def __init__(self, operation: str, timeout_seconds: int, message: str = "Operation timed out", error_code: str = "041202", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["operation"] = operation
        details["timeoutSeconds"] = timeout_seconds
        super().__init__(message, error_code, details)


class FieldErrorResource:
    """Represents a field-level error matching Java FieldErrorResource structure."""
    
    def __init__(self, field: str, message: str):
        self.field = field
        self.message = message
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "field": self.field,
            "message": self.message
        }


class ErrorResource:
    """Represents an error response matching Java ErrorResource structure."""
    
    def __init__(self, code: str, message: str):
        self.errorCode = code  # Changed to match expected API
        self.message = message
        self.fieldErrors = []  # Changed to match expected API
    
    def add_field_error(self, field: str, message: str):
        """Add a field error to the error resource."""
        field_error = FieldErrorResource(field, message)
        self.fieldErrors.append(field_error)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format matching Java ErrorResource."""
        return {
            "errorCode": self.errorCode,  # Updated to match new property name
            "message": self.message,
            "fieldErrors": [field_error.to_dict() for field_error in self.fieldErrors]  # Updated property name
        }


class QuestionLengthValidationError(BaseChatbotException):
    """Raised when question length exceeds the allowed limit."""
    
    def __init__(self, question: str, max_length: int = 255, message: str = None, error_code: str = "041093", details: Optional[Dict[str, Any]] = None):
        if message is None:
            message = f"Question text cannot exceed {max_length} characters. Current length: {len(question)} characters."
        
        details = details or {}
        details["question"] = question
        details["maxLength"] = max_length
        details["currentLength"] = len(question)
        
        super().__init__(message, error_code, details)
    
    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        error_resource.add_field_error("question", self.message)
        return error_resource

class ConnectedAccountNotFoundError(BaseChatbotException):
    """Raised when connected account is not found."""
    
    def __init__(self, account_id: int, message: str = None, error_code: str = "041094", details: Optional[Dict[str, Any]] = None):
        if message is None:
            message = f"Connected account with ID {account_id} not found"
        
        details = details or {}
        details["accountId"] = account_id
        
        super().__init__(message, error_code, details)
    
    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        error_resource.add_field_error("connectedAccountId", self.message)
        return error_resource

class ConnectedAccountNotActiveError(BaseChatbotException):
    """Raised when connected account is not active."""
    
    def __init__(self, account_id: int, status: str, message: str = None, error_code: str = "041095", details: Optional[Dict[str, Any]] = None):
        if message is None:
            message = f"Connected account with ID {account_id} is not active. Current status: {status}"
        
        details = details or {}
        details["accountId"] = account_id
        details["status"] = status
        
        super().__init__(message, error_code, details)
    
    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        error_resource.add_field_error("connectedAccountId", self.message)
        return error_resource 

class ChatbotLimitExceededError(BaseChatbotException):
    """Raised when the maximum number of chatbots is exceeded."""
    
    def __init__(self, current_count: int, max_allowed: int, tenant_id: int, message: str = None, error_code: str = "041096", details: Optional[Dict[str, Any]] = None):
        if message is None:
            message = f"Maximum number of chatbots ({max_allowed}) exceeded. Current count: {current_count}"
        
        details = details or {}
        details["currentCount"] = current_count
        details["maxAllowed"] = max_allowed
        details["tenantId"] = tenant_id
        
        super().__init__(message, error_code, details)
    
    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        error_resource.add_field_error("chatbot", self.message)
        return error_resource


class ActiveChatbotExistsError(BaseChatbotException):
    """Raised when an active chatbot already exists for a connected account."""
    
    def __init__(self, connected_account_id: int, existing_chatbot_name: str, existing_chatbot_id: str, message: str = None, error_code: str = "041097", details: Optional[Dict[str, Any]] = None):
        if message is None:
            message = f"An active chatbot already exists for the connected account. Only one active chatbot is allowed per connected account."
        
        details = details or {}
        details["connectedAccountId"] = connected_account_id
        details["existingChatbotName"] = existing_chatbot_name
        details["existingChatbotId"] = existing_chatbot_id
        
        super().__init__(message, error_code, details)
    
    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        error_resource.add_field_error("connectedAccount", self.message)
        return error_resource


# Rule-based Chatbot Flow Errors (041120-041129)
class RuleBasedFlowValidationError(BaseChatbotException):
    """Raised when rule-based chatbot flow validation fails."""
    
    def __init__(self, validation_errors: List[str], validation_warnings: Optional[List[str]] = None, message: str = "Rule-based chatbot flow validation failed", error_code: str = "041120", details: Optional[Dict[str, Any]] = None):
        if details is None:
            details = {}
            details["validationErrors"] = validation_errors
            details["validationWarnings"] = validation_warnings or []
        
        super().__init__(message, error_code, details)
    
    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        
        # Add node-specific errors
        node_errors = self.details.get("nodeErrors", [])
        for node_error in node_errors:
            field_name = f"node.{node_error.get('nodeId', 'unknown')}"
            error_message = f"Node '{node_error.get('nodeName', 'unknown')}' ({node_error.get('nodeType', 'unknown')}): {node_error.get('error', 'Unknown error')}"
            error_resource.add_field_error(field_name, error_message)
        
        # Add edge-specific errors
        edge_errors = self.details.get("edgeErrors", [])
        for edge_error in edge_errors:
            field_name = f"edge.{edge_error.get('edgeId', 'unknown')}"
            error_message = f"Edge '{edge_error.get('edgeId', 'unknown')}' (Source: {edge_error.get('sourceNode', 'unknown')}, Target: {edge_error.get('targetNode', 'unknown')}): {edge_error.get('error', 'Unknown error')}"
            error_resource.add_field_error(field_name, error_message)
        
        # Add general validation errors that don't belong to specific nodes/edges
        general_errors = self.details.get("validationErrors", [])
        node_edge_errors = [err.get("error", "") for err in node_errors + edge_errors]
        
        for i, error in enumerate(general_errors):
            # Skip errors that are already included in node/edge specific errors
            if not any(node_edge_err in error for node_edge_err in node_edge_errors):
                error_resource.add_field_error(f"flow.general_{i+1}", error)
        
        return error_resource


class RuleBasedFlowCreationError(BaseChatbotException):
    """Raised when rule-based chatbot flow creation fails."""
    
    def __init__(self, chatbot_id: str, operation: str, message: str = "Rule-based chatbot flow creation failed", error_code: str = "041121", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["chatbotId"] = chatbot_id
        details["operation"] = operation
        
        super().__init__(message, error_code, details)
    
    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        error_resource.add_field_error("flow", self.message)
        return error_resource


class RuleBasedNodeValidationError(BaseChatbotException):
    """Raised when rule-based chatbot node validation fails."""
    
    def __init__(self, node_name: str, node_type: str, validation_error: str, message: str = None, error_code: str = "041122", details: Optional[Dict[str, Any]] = None):
        if message is None:
            message = f"Node '{node_name}' validation failed: {validation_error}"
        
        details = details or {}
        details["nodeName"] = node_name
        details["nodeType"] = node_type
        details["validationError"] = validation_error
        
        super().__init__(message, error_code, details)
    
    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        error_resource.add_field_error("node", self.message)
        return error_resource


class RuleBasedEdgeValidationError(BaseChatbotException):
    """Raised when rule-based chatbot edge validation fails."""
    
    def __init__(self, edge_id: str, validation_error: str, message: str = None, error_code: str = "041123", details: Optional[Dict[str, Any]] = None):
        if message is None:
            message = f"Edge '{edge_id}' validation failed: {validation_error}"
        
        details = details or {}
        details["edgeId"] = edge_id
        details["validationError"] = validation_error
        
        super().__init__(message, error_code, details)
    
    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        error_resource.add_field_error("edge", self.message)
        return error_resource


class NodeIdValidationError(BaseChatbotException):
    """Raised when node ID validation fails."""
    
    def __init__(self, node_id: str, validation_error: str, message: str = None, error_code: str = "041124", details: Optional[Dict[str, Any]] = None):
        if message is None:
            message = f"Node ID '{node_id}' validation failed: {validation_error}"
        
        details = details or {}
        details["nodeId"] = node_id
        details["validationError"] = validation_error
        
        super().__init__(message, error_code, details)
    
    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        error_resource.add_field_error("nodeId", self.message)
        return error_resource


class RuleBasedFlowLoopDetectionError(BaseChatbotException):
    """Raised when loop detection fails in rule-based chatbot flow."""

    def __init__(self, validation_errors: List[str], validation_warnings: Optional[List[str]] = None, message: str = "Infinite loops detected in chatbot flow", error_code: str = "041125", details: Optional[Dict[str, Any]] = None):
        if details is None:
            details = {}
        details["validationErrors"] = validation_errors
        details["validationWarnings"] = validation_warnings or []

        super().__init__(message, error_code, details)

    def to_error_resource(self) -> ErrorResource:
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)

        # Add loop-specific errors from validation_errors parameter
        loop_errors = self.details.get("validationErrors", [])
        for i, error in enumerate(loop_errors):
            error_resource.add_field_error(f"loop.detection_{i+1}", error)

        # Add recommendations as warnings from validation_warnings parameter
        recommendations = self.details.get("validationWarnings", [])
        for i, rec in enumerate(recommendations):
            error_resource.add_field_error(f"loop.recommendation_{i+1}", rec)

        return error_resource


class EntityConfigurationError(BaseChatbotException):
    """Raised when entity configuration is invalid."""

    def __init__(self, message: str = "At least one entity must be configured for the chatbot", error_code: str = "041126", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, error_code, details)

    def to_error_resource(self) -> 'ErrorResource':
        """Convert to ErrorResource format matching Java structure."""
        error_resource = ErrorResource(self.error_code, self.message)
        error_resource.add_field_error("entities", self.message)
        return error_resource