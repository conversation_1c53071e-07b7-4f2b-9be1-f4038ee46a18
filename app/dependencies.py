from fastapi import Header, HTTPEx<PERSON>, Depends, Request, Security
from typing import Optional, Annotated
from jose import jwt, JWTError
import json
import os
from dotenv import load_dotenv
from pydantic import ValidationError
from app.models import JWTPayload
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

# Load environment variables
load_dotenv()

# JWT configuration
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key")  # Load from environment variables
JWT_ALGORITHM = "HS256"

# Security scheme for Swagger UI
security = HTTPBearer(
    scheme_name="Bearer Authentication",
    description="Enter JWT token",
    auto_error=False,
)

class AuthContext:
    """Class to hold authentication context throughout the request lifecycle"""
    def __init__(self, user_id: str, tenant_id: str, source_type: str, source_id: str, source_name: str, permissions: list):
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.source_type = source_type
        self.source_id = source_id
        self.source_name = source_name
        self.permissions = permissions

async def get_auth_context(
    credentials: HTTPAuthorizationCredentials = Security(security),
    request: Request = None
):
    """
    Extract and validate JWT token from Authorization header.
    Creates an AuthContext object and stores it in request.state for use throughout the request lifecycle.
    """
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Authorization header missing",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token = credentials.credentials
    
    try:
        # Try different approaches to parse the token
        payload = None
        error_messages = []
        
        # Approach 1: Try to decode as JWT
        try:
            payload_dict = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM], options={"verify_signature": False})
            print(f"Decoded JWT payload: {payload_dict}")
            payload = JWTPayload.model_validate(payload_dict)
            print("Successfully decoded JWT token")
        except Exception as e:
            error_messages.append(f"JWT decode error: {str(e)}")

        # Approach 2: Try to parse as JSON
        if payload is None:
            try:
                payload_json = json.loads(token)
                print(f"Parsed JSON payload: {payload_json}")
                payload = JWTPayload.model_validate(payload_json)
                print("Successfully parsed token as JSON")
            except Exception as e:
                error_messages.append(f"JSON parse error: {str(e)}")

        # Approach 3: Try to create a minimal payload from available data
        if payload is None:
            try:
                # Try to extract basic info from the token
                payload_dict = jwt.decode(token, key="test", options={"verify_signature": False})
                print(f"Raw token payload: {payload_dict}")

                # Create a minimal payload with available data
                minimal_payload = {
                    "iss": payload_dict.get("iss"),
                    "data": payload_dict.get("data"),
                    "accessToken": payload_dict.get("accessToken"),
                    "userId": payload_dict.get("userId") or (payload_dict.get("data", {}).get("userId") if payload_dict.get("data") else None),
                    "tenantId": payload_dict.get("tenantId") or (payload_dict.get("data", {}).get("tenantId") if payload_dict.get("data") else None),
                    "permissions": payload_dict.get("permissions", []),
                    "username": payload_dict.get("username"),
                    "refreshToken": payload_dict.get("refreshToken"),
                    "source": payload_dict.get("source"),
                    "meta": payload_dict.get("meta")
                }

                payload = JWTPayload.model_validate(minimal_payload)
                print("Successfully created minimal payload")
            except Exception as e:
                error_messages.append(f"Minimal payload creation error: {str(e)}")
        
        # If both approaches failed, raise exception with detailed error messages
        if payload is None:
            raise HTTPException(
                status_code=401,
                detail=f"Failed to parse token: {'; '.join(error_messages)}",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create auth context - handle both nested and flat token structures
        user_id = "unknown"
        tenant_id = "unknown"
        source_type = "unknown"
        source_id = "unknown"
        source_name = "unknown"
        permissions = []

        # Try to extract user_id and tenant_id from various possible locations
        if payload.data and hasattr(payload.data, 'userId') and payload.data.userId:
            user_id = str(payload.data.userId)
        elif payload.userId:
            user_id = str(payload.userId)

        if payload.data and hasattr(payload.data, 'tenantId') and payload.data.tenantId:
            tenant_id = str(payload.data.tenantId)
        elif payload.tenantId:
            tenant_id = str(payload.tenantId)

        # Try to extract source information
        if payload.data and hasattr(payload.data, 'source') and payload.data.source:
            source_type = payload.data.source.type if hasattr(payload.data.source, 'type') else "unknown"
            source_id = payload.data.source.id if hasattr(payload.data.source, 'id') else "unknown"
            source_name = payload.data.source.name if hasattr(payload.data.source, 'name') else "unknown"
        elif payload.source:
            source_type = payload.source.type if hasattr(payload.source, 'type') else "unknown"
            source_id = payload.source.id if hasattr(payload.source, 'id') else "unknown"
            source_name = payload.source.name if hasattr(payload.source, 'name') else "unknown"

        # Try to extract permissions
        if payload.data and hasattr(payload.data, 'permissions') and payload.data.permissions:
            permissions = payload.data.permissions
        elif payload.permissions:
            permissions = payload.permissions

        print(f"Extracted auth context: user_id={user_id}, tenant_id={tenant_id}, source_type={source_type}")

        auth_context = AuthContext(
            user_id=user_id,
            tenant_id=tenant_id,
            source_type=source_type,
            source_id=source_id,
            source_name=source_name,
            permissions=permissions
        )
        
        # Store in request state for use throughout the request lifecycle
        if request:
            request.state.auth_context = auth_context
        
        return auth_context
        
    except Exception as e:
        # Print detailed error for debugging
        import traceback
        print(f"Authentication error: {str(e)}")
        print(traceback.format_exc())
        
        raise HTTPException(
            status_code=401,
            detail=f"Invalid authentication credentials: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )