import os
import logging
from dotenv import load_dotenv
import time
from fastapi.responses import JSONResponse, PlainTextResponse
from app.services.rabbitmq_manager import rabbitmq_manager
from fastapi.security import HTTPAuthorizationCredentials
from app.logging_config import setup_logging

# Load environment variables at the very beginning
load_dotenv(verbose=True)

# Set up logging first
log_level = os.getenv("LOG_LEVEL", "INFO")
logger = setup_logging(
    log_level=getattr(logging, log_level.upper())
)
logger.info(f"Setting up logging with level: {log_level}")
logger.info("Logging system initialized successfully")

# Log the loaded environment variables (without secrets)
logger.info("Environment variables loaded:")
logger.info(f"AWS_ACCESS_KEY_ID present: {os.getenv('AWS_ACCESS_KEY_ID') is not None}")
logger.info(f"AWS_SECRET_ACCESS_KEY present: {os.getenv('AWS_SECRET_ACCESS_KEY') is not None}")
logger.info(f"AWS_REGION: {os.getenv('AWS_REGION')}")
logger.info(f"AWS_S3_BUCKET_NAME: {os.getenv('AWS_S3_BUCKET_NAME')}")

from fastapi import FastAPI, Depends, Request, Response, Security
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from fastapi.openapi.models import SecuritySchemeType, SecurityScheme
from app.routers import chatbot
from app.database import engine, Base
from app.dependencies import get_auth_context, AuthContext, security
from prometheus_client import make_asgi_app
from app.monitoring import REQUEST_COUNT, LatencyTimer, ACTIVE_CONNECTIONS, AUTH_FAILURE_COUNT
import uuid
from fastapi.responses import RedirectResponse
from app.error_handlers import register_error_handlers
from app.startup_events import lifespan

# Database tables will be created in the lifespan context manager

app = FastAPI(
    title="Knowledge Base API",
    description="API for managing knowledge base and chatbot interactions",
    version="1.0.0",
    openapi_tags=[
        {"name": "chatbot", "description": "Chatbot operations"}
    ],
    swagger_ui_parameters={
        "persistAuthorization": True,
        "displayRequestDuration": True,
        "filter": True,
        "tryItOutEnabled": True
    },
    openapi_url="/v2/api-docs",  # Customize the OpenAPI JSON URL
    lifespan=lifespan
)

# Override the default OpenAPI schema to add security scheme
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Add security scheme
    openapi_schema["components"] = openapi_schema.get("components", {})
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Enter JWT token"
        }
    }
    
    # Add global security requirement
    openapi_schema["security"] = [{"BearerAuth": []}]
    
    # Add additional metadata if needed
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

# Create metrics endpoint
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

# Register error handlers
register_error_handlers(app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify the allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add request logging middleware
@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    start_time = time.time()
    
    # Generate request ID
    request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
    
    # Add request ID to request state
    request.state.request_id = request_id
    
    # Log request
    logger.info(
        f"Request started",
        extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "client_ip": request.client.host,
            "user_agent": request.headers.get("User-Agent", "Unknown")
        }
    )
    
    # Process request
    try:
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            f"Request completed",
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "process_time_ms": round(process_time * 1000, 2)
            }
        )
        
        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        
        return response
    except Exception as e:
        # Log exception
        logger.info(
            f"Request failed: {str(e)}",
            exc_info=True,
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path
            }
        )
        raise

# Add Prometheus middleware
@app.middleware("http")
async def prometheus_middleware(request: Request, call_next):
    # Skip metrics endpoint to avoid recursion
    if request.url.path in ["/metrics", "/health"]:
        return await call_next(request)
    
    # Increment active connections
    ACTIVE_CONNECTIONS.inc()
    
    # Track request latency
    method = request.method
    endpoint = request.url.path
    
    with LatencyTimer(method, endpoint):
        # Process the request
        response = await call_next(request)
    
    # Record request count
    REQUEST_COUNT.labels(
        method=method,
        endpoint=endpoint,
        status_code=response.status_code
    ).inc()
    
    # Decrement active connections
    ACTIVE_CONNECTIONS.dec()
    
    return response

# Add authentication middleware
@app.middleware("http")
async def auth_middleware(request: Request, call_next):
    # Skip authentication for certain paths
    skip_auth_paths = [
        "/",
        "/docs",
        "/openapi.json",
        "/v2/api-docs",
        "/metrics",
        "/health"
    ]
    
    # Check exact path match
    if request.url.path in skip_auth_paths:
        response = await call_next(request)
        return response
    
    # Check for path prefixes that should skip auth
    skip_auth_prefixes = ["/docs", "/openapi"]
    if any(request.url.path.startswith(prefix) for prefix in skip_auth_prefixes):
        response = await call_next(request)
        return response
    
    # Get Authorization header
    authorization = request.headers.get("Authorization")
    if not authorization:
        AUTH_FAILURE_COUNT.labels(reason="missing_header").inc()
        logger.warning(
            "Authentication failed: Missing Authorization header",
            extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "path": request.url.path,
                "client_ip": request.client.host
            }
        )
        return Response(
            content='{"detail":"Authorization header missing"}',
            status_code=401,
            media_type="application/json",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    try:
        # Extract token from Authorization header
        if authorization.startswith("Bearer "):
            token = authorization.replace("Bearer ", "")
        else:
            AUTH_FAILURE_COUNT.labels(reason="invalid_scheme").inc()
            logger.warning(
                "Authentication failed: Invalid scheme",
                extra={
                    "request_id": getattr(request.state, "request_id", "unknown"),
                    "path": request.url.path,
                    "client_ip": request.client.host
                }
            )
            return Response(
                content='{"detail":"Invalid authentication scheme. Expected Bearer"}',
                status_code=401,
                media_type="application/json",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Create credentials object
        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials=token)
        
        # Process the token and store auth context in request.state
        auth_context = await get_auth_context(credentials, request)
        
        # Log successful authentication
        logger.info(
            "Authentication successful",
            extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "user_id": auth_context.user_id,
                "tenant_id": auth_context.tenant_id
            }
        )
        
        # Continue with the request
        return await call_next(request)
    except Exception as e:
        # Handle authentication errors
        AUTH_FAILURE_COUNT.labels(reason="auth_error").inc()
        logger.info(
            f"Authentication error: {str(e)}",
            exc_info=True,
            extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "path": request.url.path,
                "client_ip": request.client.host
            }
        )
        return Response(
            content=f'{{"detail":"Authentication error: {str(e)}"}}',
            status_code=401,
            media_type="application/json",
            headers={"WWW-Authenticate": "Bearer"}
        )

# RabbitMQ startup and shutdown handlers
# Note: Startup and shutdown events are now handled by the lifespan context manager
# in app/startup_events.py which includes the idle conversation monitor

# Health check endpoint for Kubernetes probes
@app.get("/health")
async def health_check():
    """
    Health check endpoint for Kubernetes probes
    """
    try:
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "service": "sd-whatsapp-chatbot",
            "version": "1.0.0"
        }
        
        # Add database health check with pool monitoring
        try:
            from app.database import get_database_health, get_pool_status, pool_manager
            db_health = get_database_health()
            pool_status = get_pool_status()
            
            # Get additional pool statistics from the pool manager
            pool_stats = pool_manager.get_pool_stats()
            
            health_status["database"] = {
                "status": db_health.get("status", "unknown"),
                "pool": pool_status,
                "pool_stats": pool_stats,
                "chatbot_count": db_health.get("chatbot_count", 0)
            }
        except Exception as e:
            health_status["database"] = {"error": str(e)}
        
        # Optional: Add RabbitMQ health check
        try:
            rabbitmq_status = await rabbitmq_manager.get_status()
            health_status["rabbitmq"] = {
                "initialized": rabbitmq_status.get("manager", {}).get("is_initialized", False),
                "running": rabbitmq_status.get("manager", {}).get("is_running", False)
            }
        except Exception as e:
            health_status["rabbitmq"] = {"error": str(e)}
        
        return JSONResponse(
            status_code=200,
            content=health_status
        )
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time(),
                "service": "sd-whatsapp-chatbot"
            }
        )

# Database pool monitoring endpoint
@app.get("/health/database")
async def database_pool_health():
    """
    Database pool monitoring endpoint for detailed connection pool information
    """
    try:
        from app.database import get_pool_status, get_database_health, pool_manager
        
        pool_status = get_pool_status()
        db_health = get_database_health()
        pool_stats = pool_manager.get_pool_stats()
        
        # Get monitoring thresholds
        from app.database_config import get_pool_monitoring_config
        try:
            thresholds = get_pool_monitoring_config()
        except ImportError:
            # Fallback thresholds if config module is not available
            thresholds = {
                "high_utilization": 80,
                "connection_timeout": 5,
                "invalid_connections": 3
            }
        
        # Calculate pool health score
        pool_health_score = 100
        warnings = []
        
        if "utilization_percentage" in pool_status:
            utilization = pool_status["utilization_percentage"]
            if utilization > thresholds["high_utilization"]:
                pool_health_score -= 20
                warnings.append(f"High pool utilization: {utilization}%")
        
        if "invalid" in pool_status and pool_status["invalid"] > thresholds["invalid_connections"]:
            pool_health_score -= 15
            warnings.append(f"High invalid connections: {pool_status['invalid']}")
        
        if "failed_connections" in pool_status and pool_status["failed_connections"] > 0:
            pool_health_score -= 10
            warnings.append(f"Failed connections detected: {pool_status['failed_connections']}")
        
        # Determine overall pool health
        if pool_health_score >= 80:
            pool_health = "healthy"
        elif pool_health_score >= 60:
            pool_health = "warning"
        else:
            pool_health = "unhealthy"
        
        response_data = {
            "status": "success",
            "timestamp": time.time(),
            "pool_health": {
                "overall": pool_health,
                "score": pool_health_score,
                "warnings": warnings
            },
            "pool_status": pool_status,
            "pool_stats": pool_stats,
            "database_health": db_health,
            "monitoring_thresholds": thresholds
        }
        
        return JSONResponse(
            status_code=200,
            content=response_data
        )
    except Exception as e:
        logger.error(f"Database pool health check failed: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "error",
                "error": str(e),
                "timestamp": time.time()
            }
        )

# Advanced pool management endpoint
@app.get("/health/database/advanced")
async def advanced_pool_management():
    """
    Advanced pool management endpoint with detailed statistics and pool information
    """
    try:
        from app.database import pool_manager, get_pool_status, get_database_health
        
        pool_status = get_pool_status()
        db_health = get_database_health()
        pool_stats = pool_manager.get_pool_stats()
        
        # Get environment configuration
        env_config = {
            "DB_POOL_SIZE": os.getenv("DB_POOL_SIZE", "8"),
            "DB_MAX_OVERFLOW": os.getenv("DB_MAX_OVERFLOW", "0"),
            "DB_POOL_TIMEOUT": os.getenv("DB_POOL_TIMEOUT", "60"),
            "DB_POOL_RECYCLE": os.getenv("DB_POOL_RECYCLE", "1800"),
            "DB_POOL_PRE_PING": os.getenv("DB_POOL_PRE_PING", "True"),
            "ENVIRONMENT": os.getenv("ENVIRONMENT", "development")
        }
        
        response_data = {
            "status": "success",
            "timestamp": time.time(),
            "environment": env_config,
            "pool_status": pool_status,
            "pool_stats": pool_stats,
            "database_health": db_health,
            "pool_manager_info": {
                "total_pools": len(pool_manager._pools),
                "pool_names": list(pool_manager._pools.keys()),
                "thread_safe": True,
                "lock_acquired": pool_manager._lock.locked()
            }
        }
        
        return JSONResponse(
            status_code=200,
            content=response_data
        )
    except Exception as e:
        logger.error(f"Advanced pool management check failed: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "error": str(e),
                "timestamp": time.time()
            }
        )

# Database pool reset endpoint
@app.post("/health/database/reset")
async def reset_database_pool():
    """
    Reset the database connection pool (use with caution)
    """
    try:
        from app.database import reset_pool
        
        result = reset_pool()
        
        if result["status"] == "success":
            return JSONResponse(
                status_code=200,
                content={
                    "status": "success",
                    "message": "Database connection pool reset successfully",
                    "timestamp": time.time()
                }
            )
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "message": "Failed to reset database connection pool",
                    "error": result.get("error", "Unknown error"),
                    "timestamp": time.time()
                }
            )
    except Exception as e:
        logger.error(f"Database pool reset failed: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": "Database pool reset failed",
                "error": str(e),
                "timestamp": time.time()
            }
        )

# Root endpoint
@app.get("/")
async def root():
    """
    Root endpoint
    """
    return {
        "message": "SD WhatsApp Chatbot API",
        "status": "running",
        "version": "1.0.0",
        "docs": "/docs"
    }

# Prometheus metrics endpoint
@app.get("/metrics")
async def metrics():
    """
    Prometheus metrics endpoint
    """
    try:
        from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
        return PlainTextResponse(
            content=generate_latest(),
            media_type=CONTENT_TYPE_LATEST
        )
    except Exception as e:
        logger.info(f"Error generating metrics: {str(e)}")
        return PlainTextResponse(
            content=f"# Error generating metrics: {str(e)}\n",
            status_code=500
        )

# Include routers
app.include_router(chatbot.router)

@app.get("/v2/api-docs", include_in_schema=False)
async def get_openapi_json():
    return app.openapi()