"""
Global Error Handlers for FastAPI Application

This module provides comprehensive error handling for all exceptions
that may occur in the application, ensuring consistent error responses.
"""

import logging
import traceback
from typing import Dict, Any, Union
from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError, OperationalError, IntegrityError
from psycopg2 import OperationalError as Psycopg2OperationalError
from redis.exceptions import RedisError
import httpx

from app.exceptions import (
    BaseChatbotException,
    AuthenticationError,
    TokenLimitExceededException,
    RateLimitExceededException,
    InsufficientPermissionsException,
    ResourceNotFoundException,
    ChatbotNotFoundException,
    ConversationNotFoundException,
    EntityNotFoundException,
    DatabaseConnectionError,
    DatabaseQueryError,
    RedisConnectionError,
    ElasticsearchConnectionError,
    ExternalAIModelException,
    RabbitMQConnectionError,
    RabbitMQPublishException,
    S3ConnectionError,
    FileSizeLimitExceededException,
    FileTypeNotSupportedException,
    FileUploadError,
    NamespaceNotFoundException,
    FiltersNotFoundException,
    KnowledgeBaseSearchError,
    ConversationStateError,
    MessageProcessingError,
    ConversationTerminationError,
    EntityUpdateError,
    FieldMappingError,
    ValidationError,
    RequiredFieldMissingError,
    InvalidFormatError,
    RuleBasedFlowValidationError,
    RuleBasedFlowCreationError,
    RuleBasedNodeValidationError,
    RuleBasedEdgeValidationError,
    QuestionLengthValidationError,
    ConnectedAccountNotFoundError,
    ConnectedAccountNotActiveError,
    ChatbotLimitExceededError,
    ActiveChatbotExistsError,
    ConfigurationError,
    EnvironmentVariableError,
    AddonNotPresentException,
    FeatureNotEnabledException,
    TaskFormFieldExtractionError,
    TaskCreationError,
    CallAnalysisInProgressException,
    InternalServerError,
    ServiceUnavailableError,
    TimeoutError,
    ErrorResource
)

logger = logging.getLogger(__name__)



async def chatbot_exception_handler(request: Request, exc: BaseChatbotException) -> JSONResponse:
    """Handle custom chatbot exceptions with Java-style ErrorResource format."""
    logger.error(f"Chatbot Exception: {exc}")
    
    # Determine status code based on exception type
    status_code = 400  # Default
    
    if isinstance(exc, (AuthenticationError, InsufficientPermissionsException)):
        status_code = 401
    elif isinstance(exc, ResourceNotFoundException):
        status_code = 404
    elif isinstance(exc, (TokenLimitExceededException, RateLimitExceededException)):
        status_code = 429
    elif isinstance(exc, (DatabaseConnectionError, RedisConnectionError, ElasticsearchConnectionError, 
                         RabbitMQConnectionError, S3ConnectionError, ServiceUnavailableError)):
        status_code = 503
    elif isinstance(exc, (InternalServerError, RuleBasedFlowCreationError)):
        status_code = 500
    
    # Convert to Java-style ErrorResource format
    error_resource = exc.to_error_resource()
    
    return JSONResponse(
        status_code=status_code,
        content=error_resource.to_dict()
    )


async def question_length_validation_handler(request: Request, exc: QuestionLengthValidationError) -> JSONResponse:
    """Handle question length validation errors with Java-style ErrorResource format."""
    logger.error(f"Question Length Validation Error: {exc}")
    
    # Convert to Java-style ErrorResource format
    error_resource = exc.to_error_resource()
    
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )

async def connected_account_not_found_handler(request: Request, exc: ConnectedAccountNotFoundError) -> JSONResponse:
    """Handle connected account not found errors with Java-style ErrorResource format."""
    logger.error(f"Connected Account Not Found Error: {exc}")
    error_resource = exc.to_error_resource()
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )

async def connected_account_not_active_handler(request: Request, exc: ConnectedAccountNotActiveError) -> JSONResponse:
    """Handle connected account not active errors with Java-style ErrorResource format."""
    logger.error(f"Connected Account Not Active Error: {exc}")
    error_resource = exc.to_error_resource()
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )

async def chatbot_limit_exceeded_handler(request: Request, exc: ChatbotLimitExceededError) -> JSONResponse:
    """Handle chatbot limit exceeded errors with Java-style ErrorResource format."""
    logger.error(f"Chatbot Limit Exceeded Error: {exc}")
    error_resource = exc.to_error_resource()
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )


async def active_chatbot_exists_handler(request: Request, exc: ActiveChatbotExistsError) -> JSONResponse:
    """Handle active chatbot exists errors with Java-style ErrorResource format."""
    logger.error(f"Active Chatbot Exists Error: {exc}")
    error_resource = exc.to_error_resource()
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle FastAPI validation errors with Java-style ErrorResource format."""
    logger.error(f"Validation Error: {exc}")
    
    # Create ErrorResource with field errors
    error_resource = ErrorResource("041080", "Validation failed")
    
    # Extract field errors
    for error in exc.errors():
        field_name = " -> ".join(str(loc) for loc in error["loc"])
        error_message = error["msg"]
        error_resource.add_field_error(field_name, error_message)
    
    return JSONResponse(
        status_code=422,
        content=error_resource.to_dict()
    )

async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """Handle SQLAlchemy database errors with Java-style ErrorResource format."""
    logger.error(f"Database Error: {exc}")
    
    if isinstance(exc, OperationalError):
        error_resource = ErrorResource("041020", "Database connection failed")
        error_resource.add_field_error("database", str(exc))
        return JSONResponse(
            status_code=503,
            content=error_resource.to_dict()
        )
    elif isinstance(exc, IntegrityError):
        error_resource = ErrorResource("041021", "Database integrity error")
        error_resource.add_field_error("database", str(exc))
        return JSONResponse(
            status_code=400,
            content=error_resource.to_dict()
        )
    else:
        error_resource = ErrorResource("041021", "Database error")
        error_resource.add_field_error("database", str(exc))
        return JSONResponse(
            status_code=500,
            content=error_resource.to_dict()
        )

async def psycopg2_exception_handler(request: Request, exc: Psycopg2OperationalError) -> JSONResponse:
    """Handle PostgreSQL-specific errors with Java-style ErrorResource format."""
    logger.error(f"PostgreSQL Error: {exc}")
    
    error_resource = ErrorResource("041020", "Database connection failed")
    error_resource.add_field_error("database", str(exc))
    
    return JSONResponse(
        status_code=503,
        content=error_resource.to_dict()
    )

async def redis_exception_handler(request: Request, exc: RedisError) -> JSONResponse:
    """Handle Redis errors with Java-style ErrorResource format."""
    logger.error(f"Redis Error: {exc}")
    
    error_resource = ErrorResource("041022", "Redis connection failed")
    error_resource.add_field_error("redis", str(exc))
    
    return JSONResponse(
        status_code=503,
        content=error_resource.to_dict()
    )

async def httpx_exception_handler(request: Request, exc: httpx.HTTPError) -> JSONResponse:
    """Handle HTTP client errors (for external API calls) with Java-style ErrorResource format."""
    logger.error(f"HTTP Client Error: {exc}")
    
    if isinstance(exc, httpx.TimeoutException):
        error_resource = ErrorResource("041202", "External service timeout")
        error_resource.add_field_error("service", str(exc.request.url))
        return JSONResponse(
            status_code=504,
            content=error_resource.to_dict()
        )
    elif isinstance(exc, httpx.ConnectError):
        error_resource = ErrorResource("041201", "External service unavailable")
        error_resource.add_field_error("service", str(exc.request.url))
        return JSONResponse(
            status_code=503,
            content=error_resource.to_dict()
        )
    else:
        error_resource = ErrorResource("041030", "External service error")
        error_resource.add_field_error("service", str(exc.request.url))
        error_resource.add_field_error("error", str(exc))
        return JSONResponse(
            status_code=502,
            content=error_resource.to_dict()
        )

async def value_error_handler(request: Request, exc: ValueError) -> JSONResponse:
    """Handle ValueError exceptions with Java-style ErrorResource format."""
    logger.error(f"Value Error: {exc}")
    
    error_resource = ErrorResource("041080", "Invalid value provided")
    error_resource.add_field_error("value", str(exc))
    
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )

async def type_error_handler(request: Request, exc: TypeError) -> JSONResponse:
    """Handle TypeError exceptions with Java-style ErrorResource format."""
    logger.error(f"Type Error: {exc}")
    
    error_resource = ErrorResource("041080", "Invalid data type")
    error_resource.add_field_error("type", str(exc))
    
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )

async def key_error_handler(request: Request, exc: KeyError) -> JSONResponse:
    """Handle KeyError exceptions with Java-style ErrorResource format."""
    logger.error(f"Key Error: {exc}")
    
    error_resource = ErrorResource("041081", "Required field missing")
    error_resource.add_field_error("missing_key", str(exc))
    
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )

async def index_error_handler(request: Request, exc: IndexError) -> JSONResponse:
    """Handle IndexError exceptions with Java-style ErrorResource format."""
    logger.error(f"Index Error: {exc}")
    
    error_resource = ErrorResource("041080", "Invalid index or position")
    error_resource.add_field_error("index", str(exc))
    
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )

async def attribute_error_handler(request: Request, exc: AttributeError) -> JSONResponse:
    """Handle AttributeError exceptions with Java-style ErrorResource format."""
    logger.error(f"Attribute Error: {exc}")
    
    error_resource = ErrorResource("041200", "Internal server error")
    error_resource.add_field_error("attribute", str(exc))
    
    return JSONResponse(
        status_code=500,
        content=error_resource.to_dict()
    )

async def file_not_found_error_handler(request: Request, exc: FileNotFoundError) -> JSONResponse:
    """Handle FileNotFoundError exceptions with Java-style ErrorResource format."""
    logger.error(f"File Not Found Error: {exc}")
    
    error_resource = ErrorResource("041010", "File not found")
    error_resource.add_field_error("file_path", str(exc))
    
    return JSONResponse(
        status_code=404,
        content=error_resource.to_dict()
    )

async def permission_error_handler(request: Request, exc: PermissionError) -> JSONResponse:
    """Handle PermissionError exceptions with Java-style ErrorResource format."""
    logger.error(f"Permission Error: {exc}")
    
    error_resource = ErrorResource("041004", "Permission denied")
    error_resource.add_field_error("permission", str(exc))
    
    return JSONResponse(
        status_code=403,
        content=error_resource.to_dict()
    )

async def timeout_error_handler(request: Request, exc: TimeoutError) -> JSONResponse:
    """Handle TimeoutError exceptions with Java-style ErrorResource format."""
    logger.error(f"Timeout Error: {exc}")
    
    error_resource = ErrorResource("041202", "Operation timed out")
    error_resource.add_field_error("timeout", str(exc))
    
    return JSONResponse(
        status_code=504,
        content=error_resource.to_dict()
    )

async def memory_error_handler(request: Request, exc: MemoryError) -> JSONResponse:
    """Handle MemoryError exceptions with Java-style ErrorResource format."""
    logger.error(f"Memory Error: {exc}")
    
    error_resource = ErrorResource("041200", "Insufficient memory")
    error_resource.add_field_error("memory", str(exc))
    
    return JSONResponse(
        status_code=503,
        content=error_resource.to_dict()
    )

async def overflow_error_handler(request: Request, exc: OverflowError) -> JSONResponse:
    """Handle OverflowError exceptions with Java-style ErrorResource format."""
    logger.error(f"Overflow Error: {exc}")
    
    error_resource = ErrorResource("041080", "Value too large")
    error_resource.add_field_error("overflow", str(exc))
    
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )

async def zero_division_error_handler(request: Request, exc: ZeroDivisionError) -> JSONResponse:
    """Handle ZeroDivisionError exceptions with Java-style ErrorResource format."""
    logger.error(f"Zero Division Error: {exc}")
    
    error_resource = ErrorResource("041080", "Division by zero")
    error_resource.add_field_error("division", str(exc))
    
    return JSONResponse(
        status_code=400,
        content=error_resource.to_dict()
    )

async def assertion_error_handler(request: Request, exc: AssertionError) -> JSONResponse:
    """Handle AssertionError exceptions with Java-style ErrorResource format."""
    logger.error(f"Assertion Error: {exc}")
    
    error_resource = ErrorResource("041200", "Internal validation failed")
    error_resource.add_field_error("assertion", str(exc))
    
    return JSONResponse(
        status_code=500,
        content=error_resource.to_dict()
    )

async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle any unhandled exceptions with Java-style ErrorResource format."""
    logger.error(f"Unhandled Exception: {exc}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    
    # Log additional request information
    logger.error(f"Request URL: {request.url}")
    logger.error(f"Request Method: {request.method}")
    logger.error(f"Request Headers: {dict(request.headers)}")
    
    error_resource = ErrorResource("041200", "Internal server error")
    error_resource.add_field_error("general", "An unexpected error occurred")
    
    return JSONResponse(
        status_code=500,
        content=error_resource.to_dict()
    )

def register_error_handlers(app):
    """Register all error handlers with the FastAPI app."""
    
    # Custom chatbot exceptions
    app.add_exception_handler(BaseChatbotException, chatbot_exception_handler)
    
    # Specific validation exceptions (must be before BaseChatbotException to take precedence)
    app.add_exception_handler(QuestionLengthValidationError, question_length_validation_handler)
    app.add_exception_handler(ConnectedAccountNotFoundError, connected_account_not_found_handler)
    app.add_exception_handler(ConnectedAccountNotActiveError, connected_account_not_active_handler)
    app.add_exception_handler(ChatbotLimitExceededError, chatbot_limit_exceeded_handler)
    app.add_exception_handler(ActiveChatbotExistsError, active_chatbot_exists_handler)
    
    # FastAPI exceptions
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # Database exceptions
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
    app.add_exception_handler(Psycopg2OperationalError, psycopg2_exception_handler)
    
    # External service exceptions
    app.add_exception_handler(RedisError, redis_exception_handler)
    app.add_exception_handler(httpx.HTTPError, httpx_exception_handler)
    
    # Python built-in exceptions
    app.add_exception_handler(ValueError, value_error_handler)
    app.add_exception_handler(TypeError, type_error_handler)
    app.add_exception_handler(KeyError, key_error_handler)
    app.add_exception_handler(IndexError, index_error_handler)
    app.add_exception_handler(AttributeError, attribute_error_handler)
    app.add_exception_handler(FileNotFoundError, file_not_found_error_handler)
    app.add_exception_handler(PermissionError, permission_error_handler)
    app.add_exception_handler(TimeoutError, timeout_error_handler)
    app.add_exception_handler(MemoryError, memory_error_handler)
    app.add_exception_handler(OverflowError, overflow_error_handler)
    app.add_exception_handler(ZeroDivisionError, zero_division_error_handler)
    app.add_exception_handler(AssertionError, assertion_error_handler)
    
    # General exception handler (must be last)
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("✅ All error handlers registered successfully") 