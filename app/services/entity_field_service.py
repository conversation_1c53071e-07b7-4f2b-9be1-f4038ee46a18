import os
import requests
import logging
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from fastapi import HTTPException

logger = logging.getLogger(__name__)


class EntityFieldService:
    """
    Service class for retrieving entity field values for variable substitution
    """
    
    def __init__(self):
        """
        Initialize the EntityFieldService
        """
        logger.info("Initialized EntityFieldService")
    
    def get_entity_field_value(self, entity_id: str, entity_type: str, field_name: str, token: str) -> Optional[str]:
        """
        Get field value from entity by ID and field name
        
        Args:
            entity_id: The entity ID
            entity_type: The entity type (contact, lead, etc.)
            field_name: The field name to retrieve
            token: Authorization token
            
        Returns:
            Optional[str]: Field value or None if not found
        """
        try:
            logger.info(f"🔧 GET_ENTITY_FIELD_VALUE - entity_id: {entity_id}, entity_type: '{entity_type}', field_name: '{field_name}', token_length: {len(token) if token else 0}")
            # Map entity types to API endpoints
            entity_type_mapping = {
                "contact": "contacts",
                "lead": "leads"
            }
            
            api_entity_type = entity_type_mapping.get(entity_type.lower())
            if not api_entity_type:
                logger.warning(f"Unknown entity type: {entity_type}")
                return None
            
            # Use sd-sales API for leads and contacts
            if entity_type.lower() in ["lead", "contact"]:
                base_url = "sd-sales"
            else:
                base_url = os.getenv("SD_CONFIG_BASE_PATH", "sd-config")
            
            full_url = f"http://{base_url}/v1/{api_entity_type}/{entity_id}"
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            logger.info(f"Fetching entity field value: {entity_type}/{entity_id}/{field_name}")
            
            response = requests.get(full_url, headers=headers, timeout=10)
            response.raise_for_status()
            
            entity_data = response.json()
            
            # Extract field value based on field name and entity type
            field_value = self._extract_field_value(entity_data, field_name, entity_type)
            
            if field_value is not None:
                logger.info(f"Retrieved field value: {field_name}={field_value}")
                return str(field_value)
            else:
                logger.warning(f"Field {field_name} not found in entity {entity_id}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error fetching entity field: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error fetching entity field value: {str(e)}")
            return None
    
    def _extract_field_value(self, entity_data: Dict[str, Any], field_name: str, entity_type: str) -> Optional[str]:
        """
        Extract field value from entity data based on field name
        
        Args:
            entity_data: The entity data from API response
            field_name: The field name to extract
            entity_type: The entity type (lead, contact, etc.) - kept for logging
            
        Returns:
            Optional[str]: Extracted field value or None
        """
        try:
            # Check custom field values first (they have priority)
            if "customFieldValues" in entity_data and field_name in entity_data["customFieldValues"]:
                custom_value = entity_data["customFieldValues"][field_name]
                return str(custom_value) if custom_value is not None else None
            
            # Handle special computed fields
            if field_name == "fullName":
                first_name = entity_data.get("firstName", "")
                last_name = entity_data.get("lastName", "")
                full_name = f"{first_name} {last_name}".strip()
                return full_name if full_name else None
            
            if field_name == "phone":
                return self._extract_primary_phone(entity_data)
            
            if field_name == "owner":
                return self._extract_owner_name(entity_data)
            
            if field_name == "pipeline":
                return self._extract_pipeline_name(entity_data)
            
            if field_name == "stage":
                return self._extract_stage_name(entity_data)
            
            # Handle nested field access (e.g., pipeline.name, pipeline.stage.name)
            if "." in field_name:
                return self._extract_nested_field_value(entity_data, field_name)
            
            # Direct field mapping
            if field_name in entity_data:
                field_value = entity_data[field_name]
                if field_value is not None:
                    # Check if this field has an idNameStore mapping for the value
                    if "metaData" in entity_data and "idNameStore" in entity_data["metaData"]:
                        id_name_store = entity_data["metaData"]["idNameStore"]
                        if field_name in id_name_store:
                            field_id_mappings = id_name_store[field_name]
                            if isinstance(field_id_mappings, dict) and str(field_value) in field_id_mappings:
                                # Return the name from idNameStore instead of the raw ID
                                mapped_name = field_id_mappings[str(field_value)]
                                logger.info(f"🔄 ID_NAME_STORE MAPPING - {field_name}: {field_value} -> {mapped_name}")
                                return str(mapped_name) if mapped_name is not None else str(field_value)
                    
                    return str(field_value)
            
            # Check metadata for ID-based fields (legacy fallback)
            if "metaData" in entity_data and "idNameStore" in entity_data["metaData"]:
                id_name_store = entity_data["metaData"]["idNameStore"]
                if field_name in id_name_store:
                    # Get the first value from the ID mapping
                    values = id_name_store[field_name]
                    if values and isinstance(values, dict):
                        first_value = list(values.values())[0]
                        return str(first_value) if first_value is not None else None
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting field value {field_name}: {str(e)}")
            return None
    
    def _extract_nested_field_value(self, entity_data: Dict[str, Any], field_name: str) -> Optional[str]:
        """
        Extract nested field value (e.g., pipeline.name, pipeline.stage.name)
        
        Args:
            entity_data: The entity data from API response
            field_name: The nested field name (e.g., "pipeline.name")
            
        Returns:
            Optional[str]: Extracted field value or None
        """
        try:
            # Split the field name by dots
            field_parts = field_name.split(".")
            current_data = entity_data
            
            # Navigate through the nested structure
            for part in field_parts:
                if isinstance(current_data, dict) and part in current_data:
                    current_data = current_data[part]
                else:
                    return None
            
            return str(current_data) if current_data is not None else None
            
        except Exception as e:
            logger.error(f"Error extracting nested field value {field_name}: {str(e)}")
            return None
    
    def _extract_primary_phone(self, entity_data: Dict[str, Any]) -> Optional[str]:
        """Extract primary phone number from entity data"""
        phone_numbers = entity_data.get("phoneNumbers", [])
        for phone in phone_numbers:
            if phone.get("primary", False):
                return phone.get("value")
        # Return first phone if no primary found
        if phone_numbers:
            return phone_numbers[0].get("value")
        return None
    
    def _extract_owner_name(self, entity_data: Dict[str, Any]) -> Optional[str]:
        """Extract owner name from entity data"""
        owner_id = entity_data.get("ownerId")
        if owner_id and "metaData" in entity_data:
            id_name_store = entity_data["metaData"].get("idNameStore", {})
            owner_mapping = id_name_store.get("ownerId", {})
            if str(owner_id) in owner_mapping:
                return owner_mapping[str(owner_id)]
        return None
    
    def _extract_pipeline_name(self, entity_data: Dict[str, Any]) -> Optional[str]:
        """Extract pipeline name from entity data"""
        pipeline = entity_data.get("pipeline")
        if pipeline and isinstance(pipeline, dict):
            return pipeline.get("name")
        return None
    
    def _extract_stage_name(self, entity_data: Dict[str, Any]) -> Optional[str]:
        """Extract stage name from entity data"""
        pipeline = entity_data.get("pipeline")
        if pipeline and isinstance(pipeline, dict):
            stage = pipeline.get("stage")
            if stage and isinstance(stage, dict):
                return stage.get("name")
        return None
    
    def get_entity_field_values_from_mappings(self, entity_details: List[Dict[str, Any]], variable_mappings: List[Dict[str, Any]], token: str, allowed_entities: Optional[List[str]] = None) -> Dict[str, str]:
        """
        Get field values based on variable mappings from nodes
        
        Args:
            entity_details: List of entity details from conversation
            variable_mappings: List of variable mappings from node
            token: Authorization token
            allowed_entities: Optional list of allowed entity types (uppercase) from chatbot configuration
            
        Returns:
            Dict[str, str]: Mapping of variable names to field values
        """
        field_values = {}
        
        # Create entity lookup by type
        entity_lookup = {}
        for entity in entity_details:
            # Support both 'entityType' and 'entity' field names
            entity_type = (entity.get("entityType") or entity.get("entity") or "").lower()
            entity_id = entity.get("id") or entity.get("entityId")  # Support both formats
            if entity_type and entity_id:
                entity_lookup[entity_type] = entity_id
                logger.info(f"🔧 ENTITY LOOKUP - Added {entity_type}: {entity_id}")
        
        # Filter variable mappings based on allowed entities
        if allowed_entities is not None:
            original_count = len(variable_mappings)
            variable_mappings = [
                mapping for mapping in variable_mappings
                if mapping.get("entity", "").upper() in allowed_entities
            ]
            filtered_count = original_count - len(variable_mappings)
            if filtered_count > 0:
                logger.info(f"🔒 VARIABLE MAPPING FILTER - Filtered out {filtered_count} mappings not in chatbot entities. Allowed: {allowed_entities}")
        
        # Process each variable mapping
        logger.info(f"🔧 VARIABLE MAPPING DEBUG - Processing {len(variable_mappings)} mappings with entity_lookup: {entity_lookup}")
        for mapping in variable_mappings:
            entity_type = mapping.get("entity", "").lower()
            internal_name = mapping.get("internalName")
            variable_name = mapping.get("variable")
            fallback_value = mapping.get("fallbackValue")  # NEW: Support fallback values
            
            logger.info(f"🔧 PROCESSING MAPPING - entity: '{entity_type}', internalName: '{internal_name}', variable: '{variable_name}', fallback: '{fallback_value}'")
            
            if not entity_type or not internal_name or not variable_name:
                logger.warning(f"🔧 SKIPPING MAPPING - Missing required fields: entity_type={bool(entity_type)}, internal_name={bool(internal_name)}, variable_name={bool(variable_name)}")
                continue
                
            # Get entity ID for this entity type
            entity_id = entity_lookup.get(entity_type)
            if not entity_id:
                logger.warning(f"🔧 NO ENTITY FOUND - No entity found for type: '{entity_type}', available types: {list(entity_lookup.keys())}")
                # Use fallback value if no entity found
                if fallback_value is not None:
                    field_values[variable_name] = fallback_value
                    logger.info(f"🔧 USING FALLBACK - Variable {variable_name}: '{fallback_value}' (no entity found)")
                continue
            
            # Fetch the specific field value
            logger.info(f"🔧 FETCHING FIELD - entity_id: {entity_id}, entity_type: '{entity_type}', internal_name: '{internal_name}'")
            field_value = self.get_entity_field_value(entity_id, entity_type, internal_name, token)
            logger.info(f"🔧 FIELD RESULT - Retrieved value: '{field_value}' for {entity_type}.{internal_name}")
            
            if field_value:
                field_values[variable_name] = field_value
                logger.info(f"🔧 MAPPED SUCCESSFULLY - {entity_type}.{internal_name} -> {variable_name} = '{field_value}'")
            else:
                # Use fallback value if field value not found
                if fallback_value is not None:
                    field_values[variable_name] = fallback_value
                    logger.info(f"🔧 USING FALLBACK - Variable {variable_name}: '{fallback_value}' (field not found)")
                else:
                    logger.warning(f"🔧 NO VALUE FOUND - Could not fetch {entity_type}.{internal_name} for variable {variable_name} and no fallback value provided")
        
        logger.info(f"Retrieved field values from mappings: {field_values}")
        return field_values
    
    def get_entity_field_values(self, entity_details: List[Dict[str, Any]], token: str) -> Dict[str, str]:
        """
        Get field values for all entities in conversation (legacy method for backward compatibility)
        
        Args:
            entity_details: List of entity details from conversation
            token: Authorization token
            
        Returns:
            Dict[str, str]: Mapping of variable names to field values
        """
        # This method is kept for backward compatibility but should not be used
        # Use get_entity_field_values_from_mappings instead
        logger.warning("Using deprecated get_entity_field_values method. Use get_entity_field_values_from_mappings instead.")
        return {}
    
    def substitute_variables(self, text: str, field_values: Dict[str, str]) -> str:
        """
        Substitute variables in text with actual field values
        
        Args:
            text: Text containing variables like {{variableName}}
            field_values: Mapping of variable names to values
            
        Returns:
            str: Text with variables substituted
        """
        if not text or not field_values:
            return text
            
        import re
        
        # Pattern to match {{variableName}}
        pattern = r'\{\{([^}]+)\}\}'
        
        def replace_variable(match):
            variable_name = match.group(1).strip()
            value = field_values.get(variable_name)
            if value:
                return value
            else:
                # Return a default fallback value instead of the original placeholder
                return f"[{variable_name}]"  # Default fallback format
        
        substituted_text = re.sub(pattern, replace_variable, text)
        
        if substituted_text != text:
            logger.info(f"Variable substitution: '{text}' -> '{substituted_text}'")
        
        return substituted_text
    
    def substitute_node_variables(self, node_data: Dict[str, Any], field_values: Dict[str, str]) -> Dict[str, Any]:
        """
        Substitute variables in node data (text, captions, etc.)
        
        Args:
            node_data: Node data dictionary
            field_values: Mapping of variable names to values
            
        Returns:
            Dict[str, Any]: Node data with variables substituted
        """
        if not node_data or not field_values:
            return node_data
            
        # Create a copy to avoid modifying original
        substituted_data = node_data.copy()
        
        # Substitute text field
        if "text" in substituted_data and substituted_data["text"]:
            substituted_data["text"] = self.substitute_variables(substituted_data["text"], field_values)
        
        # Substitute button node fields (body, header, footer)
        if "body" in substituted_data and substituted_data["body"]:
            substituted_data["body"] = self.substitute_variables(substituted_data["body"], field_values)
        
        if "header" in substituted_data and substituted_data["header"]:
            header = substituted_data["header"]
            if isinstance(header, dict) and "text" in header and header["text"]:
                header["text"] = self.substitute_variables(header["text"], field_values)
            elif isinstance(header, str):
                substituted_data["header"] = self.substitute_variables(header, field_values)
        
        if "footer" in substituted_data and substituted_data["footer"]:
            substituted_data["footer"] = self.substitute_variables(substituted_data["footer"], field_values)
        
        # Substitute options
        if "options" in substituted_data and substituted_data["options"]:
            substituted_options = []
            for option in substituted_data["options"]:
                substituted_option = option.copy()
                
                # Substitute text in option
                if "text" in substituted_option and substituted_option["text"]:
                    substituted_option["text"] = self.substitute_variables(substituted_option["text"], field_values)
                
                # Substitute media file caption
                if "mediaFile" in substituted_option and substituted_option["mediaFile"]:
                    media_file = substituted_option["mediaFile"].copy()
                    if "fileCaption" in media_file and media_file["fileCaption"]:
                        media_file["fileCaption"] = self.substitute_variables(media_file["fileCaption"], field_values)
                    substituted_option["mediaFile"] = media_file
                
                substituted_options.append(substituted_option)
            substituted_data["options"] = substituted_options
        
        return substituted_data
    
    def get_entity_details(self, entity_id: str, entity_type: str, token: str) -> Dict[str, Any]:
        """
        Get entity details (ownerId and entityName) in a single API call
        
        Args:
            entity_id: The entity ID
            entity_type: The entity type (LEAD, CONTACT, etc.)
            token: Authorization token
            
        Returns:
            Dict[str, Any]: Dictionary containing ownerId and entityName
        """
        global base_url
        try:
            logger.info(f"🔍 ENTITY FIELD SERVICE - Starting get_entity_details")
            logger.info(f"🔍 ENTITY FIELD SERVICE - Entity ID: {entity_id}, Type: {entity_type}")
            logger.info(f"🔍 ENTITY FIELD SERVICE - Token length: {len(token) if token else 0}")
            
            # Validate entity_type is not None
            if not entity_type:
                logger.warning(f"❌ ENTITY TYPE IS NONE - Cannot fetch entity details")
                return {"ownerId": None, "entityName": None}
            
            # Map entity types to API endpoints
            entity_type_mapping = {
                "contact": "contacts",
                "lead": "leads"
            }
            
            api_entity_type = entity_type_mapping.get(entity_type.lower())
            if not api_entity_type:
                logger.warning(f"❌ UNKNOWN ENTITY TYPE - {entity_type}")
                return {"ownerId": None, "entityName": None}
            
            logger.info(f"🔍 ENTITY FIELD SERVICE - Mapped entity type: {entity_type} -> {api_entity_type}")
            
            # Use sd-sales API for leads and contacts
            if entity_type.lower() in ["lead", "contact"]:
                base_url = "sd-sales"
                logger.info(f"🔍 ENTITY FIELD SERVICE - Using sd-sales API for {entity_type}")
            else:
                base_url = os.getenv("SD_CONFIG_BASE_PATH", "sd-config")
                logger.info(f"🔍 ENTITY FIELD SERVICE - Using config API for {entity_type}")
            
            full_url = f"http://{base_url}/v1/{api_entity_type}/{entity_id}"
            logger.info(f"🔍 ENTITY FIELD SERVICE - Full URL: {full_url}")
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            logger.info(f"🔍 ENTITY FIELD SERVICE - Making API request to: {full_url}")
            logger.info(f"🔍 ENTITY FIELD SERVICE - Headers: Authorization=Bearer {token[:10]}..., Content-Type=application/json")
            
            response = requests.get(full_url, headers=headers, timeout=10)
            logger.info(f"🔍 ENTITY FIELD SERVICE - Response status: {response.status_code}")
            
            response.raise_for_status()
            
            entity_data = response.json()
            logger.info(f"🔍 ENTITY FIELD SERVICE - Response data keys: {list(entity_data.keys()) if isinstance(entity_data, dict) else 'Not a dict'}")
            
            # Extract owner ID
            owner_id = entity_data.get("ownerId")
            owner_id = int(owner_id) if owner_id is not None else None
            logger.info(f"🔍 ENTITY FIELD SERVICE - Extracted ownerId: {owner_id}")
            
            # Extract entity name (firstName + lastName)
            first_name = entity_data.get("firstName", "")
            last_name = entity_data.get("lastName", "")
            entity_name = f"{first_name} {last_name}".strip()
            entity_name = entity_name if entity_name else None
            logger.info(f"🔍 ENTITY FIELD SERVICE - Extracted entityName: '{entity_name}' (firstName: '{first_name}', lastName: '{last_name}')")
            
            result = {
                "ownerId": owner_id,
                "entityName": entity_name
            }
            
            logger.info(f"✅ ENTITY FIELD SERVICE - Successfully retrieved entity details: ownerId={owner_id}, entityName={entity_name}")
            return result
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ ENTITY FIELD SERVICE - Request error fetching entity details: {str(e)}")
            logger.warning(f"⚠️ ENTITY FIELD SERVICE - API CALL FAILED - Entity {entity_id} ({entity_type}): {str(e)}")
            logger.warning(f"⚠️ ENTITY FIELD SERVICE - This is likely due to service connectivity issues in the microservices environment")
            return {"ownerId": None, "entityName": None}
        except Exception as e:
            logger.error(f"❌ ENTITY FIELD SERVICE - Unexpected error fetching entity details: {str(e)}")
            logger.warning(f"⚠️ ENTITY FIELD SERVICE - UNEXPECTED ERROR - Entity {entity_id} ({entity_type}): {str(e)}")
            logger.error(f"❌ ENTITY FIELD SERVICE - Error type: {type(e).__name__}")
            return {"ownerId": None, "entityName": None}

    def get_entity_name(self, entity_id: str, entity_type: str, token: str) -> Optional[str]:
        """
        Get entity name by concatenating first name and last name (legacy method)
        
        Args:
            entity_id: The entity ID
            entity_type: The entity type (LEAD, CONTACT, etc.)
            token: Authorization token
            
        Returns:
            Optional[str]: Entity name (firstName + lastName) or None if not found
        """
        try:
            # Use the new get_entity_details method for efficiency
            details = self.get_entity_details(entity_id, entity_type, token)
            return details.get("entityName")
                
        except Exception as e:
            logger.error(f"Error fetching entity name: {str(e)}")
            return None

    def get_entity_owner_id(self, entity_id: str, entity_type: str, token: str) -> Optional[int]:
        """
        Get owner ID from entity by ID and entity type (legacy method)
        
        Args:
            entity_id: The entity ID
            entity_type: The entity type (LEAD, CONTACT, etc.)
            token: Authorization token
            
        Returns:
            Optional[int]: Owner ID or None if not found
        """
        try:
            # Use the new get_entity_details method for efficiency
            details = self.get_entity_details(entity_id, entity_type, token)
            return details.get("ownerId")
                
        except Exception as e:
            logger.error(f"Error fetching entity owner ID: {str(e)}")
            return None