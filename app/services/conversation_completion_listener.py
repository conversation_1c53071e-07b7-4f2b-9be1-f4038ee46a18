"""
Conversation Completion Listener Service

This service handles incoming conversation completion messages from ex.message exchange
and marks conversations as completed.
"""

import json
import logging
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from aio_pika.abc import AbstractIncomingMessage

from app.database import get_db
from app.services.rabbitmq_service import rabbitmq_service
from app.services.conversation_event_publisher import conversation_event_publisher
from app.services.redis_service import RedisService
from app.services.entity_field_service import EntityFieldService
from app.models import ChatbotConversation
from app.utils.conversation_state_utils import update_conversation_in_db

logger = logging.getLogger(__name__)


class ConversationCompletionListener:
    """
    Listener for conversation completion messages
    """
    
    def __init__(self):
        self.queue_name = "q.message.chatbot.conversation.completed"
        self.routing_key = "message.chatbot.conversation.completed"
        self.exchange_name = "ex.message"
        self.is_running = False
    
    async def start(self):
        """
        Start the conversation completion listener
        """
        if self.is_running:
            logger.warning("Conversation completion listener is already running")
            return
        
        try:
            logger.info("Starting conversation completion listener...")
            
            # Connect to RabbitMQ
            connected = await rabbitmq_service.connect()
            if not connected:
                raise RuntimeError("Failed to connect to RabbitMQ")
            
            # Setup the conversation completion listener infrastructure
            await rabbitmq_service.setup_conversation_completion_listener()
            
            # Setup publisher for completion events
            await rabbitmq_service.setup_whatsapp_chatbot_publisher()
            
            # Register our event handler with the RabbitMQ service
            rabbitmq_service.register_event_handler(
                self.routing_key,
                self.handle_completion_event
            )
            
            # Start consuming messages from the queue
            await rabbitmq_service.start_consuming(self.queue_name)
            
            self.is_running = True
            logger.info(f"✅ Conversation completion listener started successfully")
            logger.info(f"   - Queue: {self.queue_name}")
            logger.info(f"   - Routing Key: {self.routing_key}")
            logger.info(f"   - Exchange: {self.exchange_name}")
            
        except Exception as e:
            logger.error(f"Failed to start conversation completion listener: {str(e)}")
            raise
    
    async def stop(self):
        """
        Stop the conversation completion listener
        """
        if not self.is_running:
            logger.warning("Conversation completion listener is not running")
            return
        
        try:
            logger.info("Stopping conversation completion listener...")
            self.is_running = False
            logger.info("✅ Conversation completion listener stopped successfully")
        except Exception as e:
            logger.error(f"Error stopping conversation completion listener: {str(e)}")
    
    async def handle_completion_event(self, payload: Dict[str, Any], message: AbstractIncomingMessage):
        """
        Handle incoming conversation completion event
        
        Args:
            payload: Parsed message payload
            message: Incoming RabbitMQ message
        """
        try:
            logger.info(f"📥 RECEIVED COMPLETION MESSAGE - Routing Key: {self.routing_key}")
            logger.info(f"📥 RECEIVED COMPLETION MESSAGE - Exchange: {self.exchange_name}")
            logger.info(f"📥 RECEIVED COMPLETION MESSAGE - Payload: {payload}")
            
            # Extract data
            message_conversation_id = payload.get("messageConversationId")
            chatbot_conversation_id = payload.get("chatbotConversationId")
            
            if not chatbot_conversation_id:
                logger.error("❌ Missing chatbotConversationId in completion message")
                await message.ack()
                return
            
            logger.info(f"🎯 HANDLING COMPLETION EVENT")
            logger.info(f"🎯 MESSAGE CONVERSATION ID: {message_conversation_id}")
            logger.info(f"🎯 CHATBOT CONVERSATION ID: {chatbot_conversation_id}")
            
            # Process the completion
            await self._process_completion(chatbot_conversation_id, message_conversation_id)
            
            # Acknowledge the message
            await message.ack()
            
            logger.info(f"✅ COMPLETION EVENT PROCESSED - Conversation: {chatbot_conversation_id}")
            
        except Exception as e:
            logger.error(f"❌ Error handling completion event: {str(e)}", exc_info=True)
            logger.error(f"❌ ERROR DETAILS - Conversation ID: {chatbot_conversation_id if 'chatbot_conversation_id' in locals() else 'unknown'}")
            logger.error(f"❌ ERROR DETAILS - Exception Type: {type(e).__name__}")
            # Acknowledge anyway to prevent message requeue
            await message.ack()
    
    async def _process_completion(self, conversation_id: str, message_conversation_id: Optional[int] = None):
        """
        Process conversation completion
        
        Args:
            conversation_id: Chatbot conversation ID
            message_conversation_id: Message conversation ID (optional)
        """
        redis_service = RedisService()
        db_gen = get_db()
        db: Session = next(db_gen)
        
        try:
            logger.info(f"🔄 PROCESSING COMPLETION - Conversation: {conversation_id}")
            logger.info(f"🔄 PROCESSING COMPLETION - Message Conversation: {message_conversation_id}")
            
            # Get conversation from database
            logger.info(f"🔍 STEP 1: Querying database for conversation {conversation_id}")
            conversation = db.query(ChatbotConversation).filter(
                ChatbotConversation.id == conversation_id
            ).first()
            
            if not conversation:
                logger.error(f"❌ Conversation {conversation_id} not found in database")
                return
            
            logger.info(f"✅ STEP 1 COMPLETE: Found conversation {conversation_id} in database")
            logger.info(f"   - Current Status: completed={conversation.completed}")
            
            # Get conversation state from Redis
            logger.info(f"🔍 STEP 2: Getting conversation state from Redis")
            state = redis_service.get_conversation_state(conversation_id)
            
            if not state:
                logger.warning(f"⚠️ No state found in Redis for conversation {conversation_id}")
                logger.info(f"🔧 Creating minimal state for completion")
                state = {
                    "conversation_id": conversation_id,
                    "completed": True,
                    "ended": True
                }
            else:
                logger.info(f"✅ STEP 2 COMPLETE: Found state in Redis")
                logger.info(f"   - Current State: completed={state.get('completed')}, ended={state.get('ended')}")
                # Update state
                state["completed"] = True
                state["ended"] = True
            
            # Store updated state in Redis
            logger.info(f"🔍 STEP 3: Updating Redis state")
            redis_service.store_conversation_state(conversation_id, state)
            logger.info(f"✅ STEP 3 COMPLETE: Redis state updated")
            logger.info(f"✅ Updated conversation state in Redis - completed=True, ended=True")
            
            # Update conversation in database
            logger.info(f"🔍 STEP 4: Updating conversation in database")
            update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)
            logger.info(f"✅ STEP 4 COMPLETE: Database updated - completed=True, ended=True")
            
            # Publish chatbot conversation completed event
            logger.info(f"🔍 STEP 5: Publishing completion event")
            await self._publish_completion_event(conversation_id, state)
            logger.info(f"✅ STEP 5 COMPLETE: Completion event published")
            
            logger.info(f"✅ COMPLETION PROCESSED SUCCESSFULLY - Conversation: {conversation_id}")
            
        except Exception as e:
            logger.error(f"❌ Error processing completion for conversation {conversation_id}: {str(e)}", exc_info=True)
            logger.error(f"❌ PROCESSING FAILED - Exception Type: {type(e).__name__}")
            raise
        finally:
            logger.info(f"🔧 Closing database connection")
            db.close()
    
    async def _publish_completion_event(self, conversation_id: str, state: Dict[str, Any]):
        """
        Publish chatbot conversation completed event

        Args:
            conversation_id: Chatbot conversation ID
            state: Conversation state
        """
        try:
            logger.info(f"📤 PUBLISHING COMPLETION EVENT - Conversation: {conversation_id}")
            logger.info(f"📤 STATE KEYS: {list(state.keys())}")

            # Build event payload
            event_payload = {
                "chatbotConversationId": conversation_id,
                "completed": True,
                "ended": True,
                "chatbotType": state.get("chatbotType"),
                "chatbotId": state.get("chatbot_id"),
                "chatbotName": state.get("chatbot_name"),
                "tenantId": state.get("tenant_id"),
                "messageConversationId": state.get("message_conversation_id")
            }

            # Enrich entity details with ownerId and entityName
            entity_details = state.get("entity_details")
            if entity_details:
                enriched_entity_details = await self._enrich_entity_details(entity_details, state)
                event_payload["entityDetails"] = enriched_entity_details
                logger.info(f"📤 Including {len(enriched_entity_details)} enriched entity details in event")
            
            logger.info(f"📤 Calling conversation_event_publisher.publish_conversation_completion")
            # Publish to conversation completion event without sending a message to user
            # The actual completion message was already sent by message_event_listener
            await conversation_event_publisher.publish_conversation_completion(
                chatbot_conversation_id=conversation_id,
                completion_message=None,  # No message - already sent by message_event_listener
                completed=True,
                charge=0,
                chatbot_type=state.get("chatbotType", "AI"),
                tenant_id=state.get("tenant_id"),
                entity_details=enriched_entity_details if entity_details else None,
                message_conversation_id=state.get("message_conversation_id"),
                chatbot_id=state.get("chatbot_id"),
                chatbot_name=state.get("chatbot_name")
            )
            
            logger.info(f"✅ COMPLETION EVENT PUBLISHED - Conversation: {conversation_id}")
            logger.info(f"   - Exchange: ex.whatsappChatbot")
            logger.info(f"   - Routing Key: chatbot.conversation.response")
            logger.info(f"   - Payload: {event_payload}")
            
        except Exception as e:
            logger.error(f"❌ Error publishing completion event for conversation {conversation_id}: {str(e)}", exc_info=True)
            raise

    async def _enrich_entity_details(self, entity_details: list, state: Dict[str, Any]) -> list:
        """
        Enrich entity details with ownerId and entityName by fetching from entity API

        Args:
            entity_details: List of entity details from conversation state
            state: Conversation state containing auth token

        Returns:
            List of enriched entity details with ownerId and entityName
        """
        try:
            logger.info(f"🔍 ENRICHING ENTITY DETAILS - Count: {len(entity_details)}")

            # Get auth token from state
            auth_token = state.get("auth_token")
            if not auth_token:
                logger.warning("⚠️ No auth token found in state, cannot enrich entity details")
                return entity_details

            entity_field_service = EntityFieldService()
            enriched_details = []

            for i, entity in enumerate(entity_details):
                try:
                    # Extract entity information - support both old and new formats
                    entity_id = entity.get("id") or entity.get("entityId")
                    entity_type = entity.get("entityType") or entity.get("entity")

                    # Normalize entity type to uppercase
                    if entity_type:
                        entity_type = entity_type.upper()

                    if not entity_id or not entity_type:
                        logger.warning(f"⚠️ SKIPPING ENTITY {i} - Missing entityId or entityType: {entity}")
                        # Even if skipping, convert to new format
                        enriched_details.append({
                            "entityId": int(entity_id) if entity_id else None,
                            "entityType": entity_type,
                            "ownerId": None,
                            "entityName": None
                        })
                        continue

                    # Check if ownerId and entityName are already present
                    owner_id = entity.get("ownerId")
                    entity_name = entity.get("entityName") or entity.get("name")  # Support old 'name' field

                    # If ownerId or entityName is missing, fetch from API
                    if not owner_id or not entity_name:
                        logger.info(f"🔍 FETCHING ENTITY DETAILS - Entity {i}: ID={entity_id}, Type={entity_type}")

                        entity_info = entity_field_service.get_entity_details(str(entity_id), entity_type, auth_token)
                        fetched_owner_id = entity_info.get("ownerId")
                        fetched_entity_name = entity_info.get("entityName")

                        if fetched_owner_id:
                            owner_id = fetched_owner_id
                            logger.info(f"✅ OWNER ID FETCHED - Entity {i}: OwnerId={fetched_owner_id}")
                        else:
                            logger.warning(f"⚠️ OWNER ID NOT FOUND - Entity {i}: No ownerId returned from API")

                        if fetched_entity_name:
                            entity_name = fetched_entity_name
                            logger.info(f"✅ ENTITY NAME FETCHED - Entity {i}: EntityName={fetched_entity_name}")
                        else:
                            logger.warning(f"⚠️ ENTITY NAME NOT FOUND - Entity {i}: No entityName returned from API")

                    # Build enriched entity detail matching Java structure
                    # ALWAYS use the new format regardless of input format
                    enriched_entity = {
                        "entityId": int(entity_id) if entity_id else None,
                        "entityType": entity_type,
                        "ownerId": int(owner_id) if owner_id else None,
                        "entityName": entity_name
                    }

                    enriched_details.append(enriched_entity)
                    logger.info(f"✅ ENRICHED ENTITY {i}: {enriched_entity}")

                except Exception as e:
                    logger.error(f"❌ Error enriching entity {i}: {str(e)}", exc_info=True)
                    # Even on error, convert to new format
                    entity_id = entity.get("id") or entity.get("entityId")
                    entity_type = entity.get("entityType") or entity.get("entity")
                    if entity_type:
                        entity_type = entity_type.upper()
                    enriched_details.append({
                        "entityId": int(entity_id) if entity_id else None,
                        "entityType": entity_type,
                        "ownerId": None,
                        "entityName": entity.get("entityName") or entity.get("name")
                    })

            logger.info(f"✅ ENTITY ENRICHMENT COMPLETE - Enriched {len(enriched_details)} entities")
            return enriched_details

        except Exception as e:
            logger.error(f"❌ Error enriching entity details: {str(e)}", exc_info=True)
            # Return original entity details if enrichment fails
            return entity_details


# Global instance
conversation_completion_listener = ConversationCompletionListener()

