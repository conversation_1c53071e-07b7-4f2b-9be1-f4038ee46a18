"""
Charge Calculator Service

This service handles calculating charges for conversation events based on
whether questions are predefined or custom generated.
"""

import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class ChargeCalculator:
    """
    Calculator for conversation charges based on question types
    """
    
    # Charge constants
    PREDEFINED_QUESTION_CHARGE = 1
    CUSTOM_QUESTION_CHARGE = 2
    COMPLETION_CHARGE = 0
    
    def __init__(self):
        pass
    
    def calculate_question_charge(
        self,
        question: Dict[str, Any],
        is_predefined: bool = True,
        is_llm_generated: bool = False
    ) -> int:
        """
        Calculate charge for a question based on its type
        
        Args:
            question: Question object containing question data
            is_predefined: Whether the question is from predefined list
            is_llm_generated: Whether the question was generated by LLM
            
        Returns:
            int: Charge amount (1 for predefined, 2 for custom)
        """
        try:
            # If question is from predefined list, charge is 1
            if is_predefined and not is_llm_generated:
                return self.PREDEFINED_QUESTION_CHARGE
            
            # If question is custom/LLM generated, charge is 2
            if is_llm_generated or not is_predefined:
                return self.CUSTOM_QUESTION_CHARGE
            
            # Default to predefined charge
            return self.PREDEFINED_QUESTION_CHARGE
            
        except Exception as e:
            logger.error(f"Error calculating question charge: {str(e)}")
            return self.PREDEFINED_QUESTION_CHARGE
    
    def calculate_conversation_charge(
        self,
        conversation_state: Dict[str, Any],
        current_question_index: int
    ) -> int:
        """
        Calculate charge for current conversation state
        
        Args:
            conversation_state: Current conversation state
            current_question_index: Index of current question
            
        Returns:
            int: Charge amount for current question
        """
        try:
            questions = conversation_state.get("questions", [])
            
            if current_question_index >= len(questions):
                # No more questions, this is completion
                return self.COMPLETION_CHARGE
            
            current_question = questions[current_question_index]
            
            # Check if question is from original predefined list
            is_predefined = self._is_predefined_question(
                current_question, conversation_state
            )
            
            # Check if question was generated by LLM
            is_llm_generated = self._is_llm_generated_question(
                current_question, conversation_state
            )
            
            return self.calculate_question_charge(
                current_question, is_predefined, is_llm_generated
            )
            
        except Exception as e:
            logger.error(f"Error calculating conversation charge: {str(e)}")
            return self.PREDEFINED_QUESTION_CHARGE
    
    def calculate_completion_charge(self) -> int:
        """
        Calculate charge for conversation completion
        
        Returns:
            int: Charge amount for completion (always 0)
        """
        return self.COMPLETION_CHARGE
    
    def _is_predefined_question(
        self, 
        question: Dict[str, Any], 
        conversation_state: Dict[str, Any]
    ) -> bool:
        """
        Check if a question is from the predefined list
        
        Args:
            question: Question to check
            conversation_state: Conversation state
            
        Returns:
            bool: True if question is predefined
        """
        try:
            # Get original predefined questions from conversation state
            original_questions = conversation_state.get("original_questions", [])
            
            # Check if current question matches any original question
            question_text = question.get("question", "")
            
            for original_q in original_questions:
                if original_q.get("question", "") == question_text:
                    return True
            
            # Also check if question has a predefined flag
            return question.get("is_predefined", False)
            
        except Exception as e:
            logger.error(f"Error checking if question is predefined: {str(e)}")
            return True  # Default to predefined to avoid overcharging
    
    def _is_llm_generated_question(
        self, 
        question: Dict[str, Any], 
        conversation_state: Dict[str, Any]
    ) -> bool:
        """
        Check if a question was generated by LLM
        
        Args:
            question: Question to check
            conversation_state: Conversation state
            
        Returns:
            bool: True if question was LLM generated
        """
        try:
            # Check if question has LLM generation flag
            is_llm_generated = question.get("is_llm_generated", False)
            
            # Check if question was added during conversation flow
            # (not in original questions list)
            if not self._is_predefined_question(question, conversation_state):
                return True
            
            return is_llm_generated
            
        except Exception as e:
            logger.error(f"Error checking if question is LLM generated: {str(e)}")
            return False  # Default to not LLM generated
    
    def get_charge_summary(
        self, 
        conversation_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Get a summary of charges for the entire conversation
        
        Args:
            conversation_state: Complete conversation state
            
        Returns:
            dict: Summary of charges including breakdown
        """
        try:
            questions = conversation_state.get("questions", [])
            total_charge = 0
            predefined_count = 0
            custom_count = 0
            
            for i, question in enumerate(questions):
                charge = self.calculate_conversation_charge(conversation_state, i)
                total_charge += charge
                
                if charge == self.PREDEFINED_QUESTION_CHARGE:
                    predefined_count += 1
                elif charge == self.CUSTOM_QUESTION_CHARGE:
                    custom_count += 1
            
            return {
                "total_charge": total_charge,
                "predefined_questions": predefined_count,
                "custom_questions": custom_count,
                "predefined_charge": predefined_count * self.PREDEFINED_QUESTION_CHARGE,
                "custom_charge": custom_count * self.CUSTOM_QUESTION_CHARGE,
                "completion_charge": self.COMPLETION_CHARGE
            }
            
        except Exception as e:
            logger.error(f"Error getting charge summary: {str(e)}")
            return {
                "total_charge": 0,
                "predefined_questions": 0,
                "custom_questions": 0,
                "predefined_charge": 0,
                "custom_charge": 0,
                "completion_charge": 0
            }


# Global instance
charge_calculator = ChargeCalculator()
