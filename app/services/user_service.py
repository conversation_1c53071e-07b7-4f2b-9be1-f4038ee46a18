import os
import requests
import logging
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException
from app.models import User
from app.database import get_db

logger = logging.getLogger(__name__)


class UserService:
    """
    Service class for user-related operations including IAM integration
    """
    
    def __init__(self):
        """
        Initialize the UserService
        """
        logger.info("Initialized UserService")
    
    def get_user_from_iam(self, user_id: str, token: str) -> Dict[str, Any]:
        """
        Fetch user data from IAM service
        
        Args:
            user_id: The user ID to fetch
            token: Authorization token
            
        Returns:
            Dict[str, Any]: User data from IAM
            
        Raises:
            HTTPException: If IAM service is unavailable or user not found
        """
        base_url = os.getenv("IAM_BASE_PATH", "sd-iam")
        
        logger.info(f"Using IAM base URL: {base_url}")

        full_url = f"http://sd-iam/v1/users/{user_id}"

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            response = requests.get(full_url, headers=headers)
            response.raise_for_status()

            user_data = response.json()
            logger.info(f"User data fetched from IAM: {user_data}")

            extracted_data = {
                "id": str(user_data.get("id")),  # Convert to string to match database schema
                "name": f"{user_data.get('firstName', '')} {user_data.get('lastName', '')}".strip(),
                "tenant_id": user_data.get("tenantId"),
                "email": user_data.get("email"),
            }

            logger.info(f"User data extracted from IAM: {extracted_data}")
            return extracted_data

        except requests.exceptions.RequestException as e:
            logger.error(f"Request error in get_user_from_iam - Error details: {str(e)}")
            raise HTTPException(status_code=400, detail="Something didn't work as expected.")
    
    def create_or_update_user(self, user_data: Dict[str, Any], db: Session = None) -> User:
        """
        Create or update user in database

        Args:
            user_data: User data dictionary from IAM
            db: Database session (optional, will create if not provided)

        Returns:
            User: Created or updated user object
        """
        close_db = False
        if db is None:
            db = next(get_db())
            close_db = True

        try:
            # Check if user exists
            existing_user = db.query(User).filter(User.id == user_data["id"]).first()

            if existing_user:
                # Update existing user
                existing_user.name = user_data["name"]
                existing_user.tenant_id = user_data["tenant_id"]
                existing_user.email = user_data.get("email")
                db.commit()
                logger.info(f"Updated user {user_data['id']} in database")
                return existing_user
            else:
                # Create new user
                new_user = User(
                    id=user_data["id"],
                    name=user_data["name"],
                    tenant_id=user_data["tenant_id"],
                    email=user_data.get("email")
                )
                db.add(new_user)
                db.commit()
                logger.info(f"Created new user {user_data['id']} in database")
                return new_user
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating/updating user {user_data.get('id', 'unknown')}: {str(e)}")
            raise
        finally:
            if close_db:
                db.close()
    
    def get_user_by_id(self, user_id: str, db: Session = None) -> Optional[User]:
        """
        Get user from database by ID

        Args:
            user_id: The user ID to fetch
            db: Database session (optional, will create if not provided)

        Returns:
            Optional[User]: User object if found, None otherwise
        """
        close_db = False
        if db is None:
            db = next(get_db())
            close_db = True

        try:
            user = db.query(User).filter(User.id == user_id).first()
            if user:
                logger.info(f"Found user {user_id} in database")
            else:
                logger.info(f"User {user_id} not found in database")
            return user
        except Exception as e:
            logger.error(f"Error getting user {user_id} from database: {str(e)}")
            raise
        finally:
            if close_db:
                db.close()
    
    def validate_and_get_user(self, user_id: str, token: str, db: Session = None) -> User:
        """
        Validate user exists in database, fetch from IAM if not found
        
        Args:
            user_id: The user ID to validate
            token: Authorization token for IAM service
            db: Database session (optional, will create if not provided)
            
        Returns:
            User: Valid user object
            
        Raises:
            HTTPException: If user validation fails
        """
        close_db = False
        if db is None:
            db = next(get_db())
            close_db = True
        
        try:
            # First check if user exists in database
            user = self.get_user_by_id(user_id, db)
            
            if user:
                logger.info(f"User {user_id} found in database")
                return user
            
            # User not found in database, fetch from IAM
            logger.info(f"User {user_id} not found in database, fetching from IAM")
            iam_user_data = self.get_user_from_iam(user_id, token)
            
            # Create user in database
            user = self.create_or_update_user(iam_user_data, db)
            return user
            
        except HTTPException:
            # Re-raise HTTP exceptions from IAM service
            raise
        except Exception as e:
            logger.error(f"Error validating user {user_id}: {str(e)}")
            raise HTTPException(status_code=500, detail="User validation failed")
        finally:
            if close_db:
                db.close()
    
    def get_users_by_ids(self, user_ids: list, db: Session = None) -> Dict[str, User]:
        """
        Get multiple users by their IDs
        
        Args:
            user_ids: List of user IDs to fetch
            db: Database session (optional, will create if not provided)
            
        Returns:
            Dict[str, User]: Dictionary mapping user IDs to User objects
        """
        close_db = False
        if db is None:
            db = next(get_db())
            close_db = True
        
        try:
            users = db.query(User).filter(User.id.in_(user_ids)).all()
            user_dict = {user.id: user for user in users}
            logger.info(f"Found {len(user_dict)} users out of {len(user_ids)} requested")
            return user_dict
        except Exception as e:
            logger.error(f"Error getting users by IDs: {str(e)}")
            raise
        finally:
            if close_db:
                db.close()


# Global instance
user_service = UserService()
