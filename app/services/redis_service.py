import os
import json
import redis
from dotenv import load_dotenv
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class RedisService:
    def __init__(self):
        redis_url = os.getenv("REDIS_HOST", "redis://localhost:6379/0")
        logger.info(f"Connecting to Redis and url is {redis_url}", extra={"redis_url": redis_url})
        
        self.redis_client = redis.from_url(redis_url)
        
        try:
            # Test connection
            self.redis_client.ping()
            logger.info("Connected to Redis successfully")
        except Exception as e:
            logger.error("Failed to connect to Red<PERSON>", exc_info=True, extra={"error": str(e)})
            raise
    
    def store_conversation_state(self, conversation_id, state, expire_seconds=3600):
        """Store conversation state in Redis with expiration"""
        try:
            key = f"conversation:{conversation_id}"
            
            # Ensure state is serializable
            json_state = json.dumps(state)
            
            # Store in Redis
            self.redis_client.set(key, json_state, ex=expire_seconds)
            
            # Update last activity timestamp
            self.update_conversation_last_activity(conversation_id)
            
            logger.info("Stored conversation state", extra={
                "conversation_id": conversation_id, 
                "expire_seconds": expire_seconds,
                "state_size": len(json_state)
            })
            
            return True
        except Exception as e:
            logger.error(f"Error storing conversation state: {str(e)}", exc_info=True, extra={
                "conversation_id": conversation_id
            })
            return False
    
    def get_conversation_state(self, conversation_id):
        """Retrieve conversation state from Redis"""
        try:
            key = f"conversation:{conversation_id}"
            data = self.redis_client.get(key)
            
            if data:
                state = json.loads(data)
                logger.info(f"Retrieved conversation state", extra={
                    "conversation_id": conversation_id,
                    "state_keys": list(state.keys()) if isinstance(state, dict) else "Not a dict"
                })
                return state
            
            logger.warning(f"No state found for conversation {conversation_id}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from Redis: {str(e)}", exc_info=True, extra={
                "conversation_id": conversation_id,
                "raw_data": data[:100] if data else "None"
            })
            return None
        except Exception as e:
            logger.error(f"Error retrieving conversation state: {str(e)}", exc_info=True, extra={
                "conversation_id": conversation_id
            })
            return None
    
    def update_conversation_ttl(self, conversation_id, expire_seconds=3600):
        """Update the TTL for a conversation"""
        key = f"conversation:{conversation_id}"
        self.redis_client.expire(key, expire_seconds)
        logger.info(f"Updated TTL for conversation {conversation_id}")
    
    def update_conversation_last_activity(self, conversation_id):
        """Update the last activity timestamp for a conversation"""
        try:
            key = f"conversation_last_activity:{conversation_id}"
            import time
            current_time = int(time.time())
            self.redis_client.set(key, current_time, ex=3600)  # 1 hour TTL
            logger.info(f"Updated last activity for conversation {conversation_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating last activity for conversation {conversation_id}: {str(e)}")
            return False
    
    def get_conversation_last_activity(self, conversation_id):
        """Get the last activity timestamp for a conversation"""
        try:
            key = f"conversation_last_activity:{conversation_id}"
            timestamp = self.redis_client.get(key)
            return int(timestamp) if timestamp else None
        except Exception as e:
            logger.error(f"Error getting last activity for conversation {conversation_id}: {str(e)}")
            return None
    
    def get_idle_conversations(self, idle_threshold_seconds=2700):  # 45 minutes default
        """Get conversations that have been idle for more than the threshold"""
        try:
            import time
            current_time = int(time.time())
            idle_conversations = []
            
            # Get all conversation activity keys
            pattern = "conversation_last_activity:*"
            keys = self.redis_client.keys(pattern)
            
            for key in keys:
                conversation_id = key.decode('utf-8').split(':', 1)[1]
                last_activity = self.get_conversation_last_activity(conversation_id)
                
                if last_activity and (current_time - last_activity) > idle_threshold_seconds:
                    idle_time = current_time - last_activity
                    idle_conversations.append({
                        'conversation_id': conversation_id,
                        'last_activity': last_activity,
                        'idle_time_seconds': idle_time
                    })
            
            return idle_conversations
        except Exception as e:
            logger.error(f"Error getting idle conversations: {str(e)}")
            return []
    
    def acquire_idle_lock(self, conversation_id: str, lock_ttl_seconds=300):  # 5 minutes lock
        """Acquire a distributed lock for handling idle conversation"""
        try:
            import time
            lock_key = f"idle_lock:{conversation_id}"
            current_time = int(time.time())
            
            # Try to set the lock with TTL (atomic operation)
            result = self.redis_client.set(
                lock_key, 
                current_time, 
                ex=lock_ttl_seconds, 
                nx=True  # Only set if key doesn't exist
            )
            
            if result:
                logger.info(f"Acquired idle lock for conversation {conversation_id}")
                return True
            else:
                logger.info(f"Failed to acquire idle lock for conversation {conversation_id} (already locked)")
                return False
                
        except Exception as e:
            logger.error(f"Error acquiring idle lock for conversation {conversation_id}: {str(e)}")
            return False
    
    def release_idle_lock(self, conversation_id: str):
        """Release the distributed lock for idle conversation"""
        try:
            lock_key = f"idle_lock:{conversation_id}"
            self.redis_client.delete(lock_key)
            logger.info(f"Released idle lock for conversation {conversation_id}")
            return True
        except Exception as e:
            logger.error(f"Error releasing idle lock for conversation {conversation_id}: {str(e)}")
            return False
    
    def acquire_lock(self, lock_key: str, timeout: int = 30):
        """Acquire a distributed lock to prevent race conditions"""
        try:
            import time
            current_time = int(time.time())
            
            # Try to set the lock with TTL (atomic operation)
            result = self.redis_client.set(
                lock_key, 
                current_time, 
                ex=timeout, 
                nx=True  # Only set if key doesn't exist
            )
            
            if result:
                logger.info(f"🔒 ACQUIRED LOCK: {lock_key}")
                return True
            else:
                logger.info(f"🔒 FAILED TO ACQUIRE LOCK: {lock_key} (already locked)")
                return False
                
        except Exception as e:
            logger.error(f"Error acquiring lock {lock_key}: {str(e)}")
            return False
    
    def release_lock(self, lock_key: str):
        """Release a distributed lock"""
        try:
            self.redis_client.delete(lock_key)
            logger.info(f"🔓 RELEASED LOCK: {lock_key}")
            return True
        except Exception as e:
            logger.error(f"Error releasing lock {lock_key}: {str(e)}")
            return False
    
    def clear_conversation_state(self, conversation_id: str):
        """Clear conversation state from Redis"""
        try:
            key = f"conversation:{conversation_id}"
            self.redis_client.delete(key)
            logger.info(f"Cleared conversation state for {conversation_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing conversation state for {conversation_id}: {str(e)}")
            return False
    
    def get_pod_identifier(self):
        """Get a unique identifier for this pod instance"""
        import os
        import socket
        
        # Try to get pod name from environment (Kubernetes)
        pod_name = os.getenv('POD_NAME', '')
        if pod_name:
            return pod_name
        
        # Fallback to hostname
        hostname = socket.gethostname()
        return hostname