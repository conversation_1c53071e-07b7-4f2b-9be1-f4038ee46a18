"""
Background Task Service

Manages background tasks using a thread pool executor to prevent blocking the main event loop.
"""

import asyncio
import logging
import threading
import uuid
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Dict, Any, Optional, Callable
from datetime import datetime

logger = logging.getLogger(__name__)

class BackgroundTaskService:
    """
    Manages background tasks using a thread pool executor
    """
    
    def __init__(self, max_workers: int = 4):
        """
        Initialize the background task service
        
        Args:
            max_workers: Maximum number of worker threads
        """
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="BackgroundTask")
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        
    def submit_task(
        self, 
        task_func: Callable, 
        *args, 
        task_name: str = None,
        **kwargs
    ) -> str:
        """
        Submit a task to run in the background
        
        Args:
            task_func: The function to run in the background
            *args: Arguments to pass to the function
            task_name: Optional name for the task
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            str: Task ID for tracking
        """
        task_id = str(uuid.uuid4())
        task_name = task_name or task_func.__name__
        
        # Create task info
        task_info = {
            "id": task_id,
            "name": task_name,
            "status": "pending",
            "created_at": datetime.utcnow(),
            "started_at": None,
            "completed_at": None,
            "result": None,
            "error": None,
            "future": None
        }
        
        # Store task info
        with self._lock:
            self.tasks[task_id] = task_info
        
        # Create wrapper function to handle async tasks
        def task_wrapper():
            try:
                # Update status to running
                with self._lock:
                    if task_id in self.tasks:
                        self.tasks[task_id]["status"] = "running"
                        self.tasks[task_id]["started_at"] = datetime.utcnow()
                
                logger.info(f"Starting background task {task_name} (ID: {task_id})")
                
                # Run the task
                if asyncio.iscoroutinefunction(task_func):
                    # If it's an async function, we need to run it in a new event loop
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        result = loop.run_until_complete(task_func(*args, **kwargs))
                    finally:
                        loop.close()
                else:
                    # Regular function
                    result = task_func(*args, **kwargs)
                
                # Update task info with success
                with self._lock:
                    if task_id in self.tasks:
                        self.tasks[task_id]["status"] = "completed"
                        self.tasks[task_id]["completed_at"] = datetime.utcnow()
                        self.tasks[task_id]["result"] = result
                
                logger.info(f"Completed background task {task_name} (ID: {task_id})")
                return result
                
            except Exception as e:
                # Update task info with error
                with self._lock:
                    if task_id in self.tasks:
                        self.tasks[task_id]["status"] = "failed"
                        self.tasks[task_id]["completed_at"] = datetime.utcnow()
                        self.tasks[task_id]["error"] = str(e)
                
                logger.error(f"Background task {task_name} (ID: {task_id}) failed: {str(e)}", exc_info=True)
                raise
        
        # Submit to thread pool
        future = self.executor.submit(task_wrapper)
        
        # Store future reference
        with self._lock:
            if task_id in self.tasks:
                self.tasks[task_id]["future"] = future
        
        logger.info(f"Submitted background task {task_name} (ID: {task_id})")
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the status of a background task
        
        Args:
            task_id: The task ID
            
        Returns:
            Optional[Dict[str, Any]]: Task status information
        """
        with self._lock:
            if task_id in self.tasks:
                task_info = self.tasks[task_id].copy()
                # Remove the future object as it's not serializable
                task_info.pop("future", None)
                return task_info
        return None
    
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all background tasks
        
        Returns:
            Dict[str, Dict[str, Any]]: All task information
        """
        with self._lock:
            tasks = {}
            for task_id, task_info in self.tasks.items():
                task_copy = task_info.copy()
                # Remove the future object as it's not serializable
                task_copy.pop("future", None)
                tasks[task_id] = task_copy
            return tasks
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a background task
        
        Args:
            task_id: The task ID to cancel
            
        Returns:
            bool: True if task was cancelled, False otherwise
        """
        with self._lock:
            if task_id in self.tasks:
                task_info = self.tasks[task_id]
                future = task_info.get("future")
                
                if future and not future.done():
                    cancelled = future.cancel()
                    if cancelled:
                        task_info["status"] = "cancelled"
                        task_info["completed_at"] = datetime.utcnow()
                        logger.info(f"Cancelled background task {task_info['name']} (ID: {task_id})")
                    return cancelled
        return False
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24) -> int:
        """
        Clean up completed tasks older than the specified age
        
        Args:
            max_age_hours: Maximum age in hours for completed tasks
            
        Returns:
            int: Number of tasks cleaned up
        """
        cutoff_time = datetime.utcnow().timestamp() - (max_age_hours * 3600)
        cleaned_count = 0
        
        with self._lock:
            task_ids_to_remove = []
            
            for task_id, task_info in self.tasks.items():
                if task_info["status"] in ["completed", "failed", "cancelled"]:
                    completed_at = task_info.get("completed_at")
                    if completed_at and completed_at.timestamp() < cutoff_time:
                        task_ids_to_remove.append(task_id)
            
            for task_id in task_ids_to_remove:
                del self.tasks[task_id]
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} completed background tasks")
        
        return cleaned_count
    
    def shutdown(self):
        """
        Shutdown the background task service
        """
        logger.info("Shutting down background task service")
        
        # Cancel all pending tasks
        with self._lock:
            for task_id, task_info in self.tasks.items():
                if task_info["status"] == "pending":
                    future = task_info.get("future")
                    if future and not future.done():
                        future.cancel()
        
        # Shutdown the executor
        self.executor.shutdown(wait=True)
        logger.info("Background task service shutdown complete")

# Global instance
background_task_service = BackgroundTaskService() 