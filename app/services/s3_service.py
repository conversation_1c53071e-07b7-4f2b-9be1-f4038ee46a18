import os
import boto3
from botocore.exceptions import ClientError
import logging
from io import BytesIO
from dotenv import load_dotenv
import uuid

# Set up logging
logger = logging.getLogger(__name__)

class S3Service:

    def __init__(self, environment=None):
        # Load environment variables
        load_dotenv()
        
        # Get environment from parameter or environment variable
        self.environment = environment or os.getenv("ENVIRONMENT", "prod")
        
        # Get bucket name from environment variable (without forward slashes)
        bucket_name = os.getenv("DO_SPACES_BUCKET", "whatsapp-chatbot-knowledgebase")
        
        # Log the original bucket name from environment
        logger.info(f"🔍 S3 CONFIGURATION - Original DO_SPACES_BUCKET from env: '{bucket_name}'")
        
        # Ensure bucket name doesn't contain forward slashes
        if "/" in bucket_name:
            # Remove everything after the first forward slash
            original_bucket = bucket_name
            bucket_name = bucket_name.split("/")[0]
            logger.warning(f"⚠️ S3 CONFIGURATION - Bucket name contained forward slash!")
            logger.warning(f"⚠️ S3 CONFIGURATION - Original: '{original_bucket}' → Sanitized: '{bucket_name}'")
        else:
            logger.info(f"✅ S3 CONFIGURATION - Bucket name is clean (no forward slashes)")
        
        self.bucket_name = bucket_name
        
        # Get credentials from environment variables
        aws_access_key_id = os.getenv("DO_SPACES_KEY")
        aws_secret_access_key = os.getenv("DO_SPACES_SECRET")
        self.region = os.getenv("DO_SPACES_REGION", "nyc3")
        self.endpoint_url = os.getenv("DO_SPACES_ENDPOINT", f"https://{self.region}.digitaloceanspaces.com")
        
        # Log the environment variable status (without revealing secrets)
        logger.info(f"🔍 S3 CONFIGURATION - Environment Variables Status:")
        logger.info(f"   📍 DO_SPACES_KEY present: {aws_access_key_id is not None}")
        logger.info(f"   📍 DO_SPACES_SECRET present: {aws_secret_access_key is not None}")
        logger.info(f"   📍 DO_SPACES_REGION: {self.region}")
        logger.info(f"   📍 DO_SPACES_BUCKET: '{self.bucket_name}'")
        logger.info(f"   📍 ENVIRONMENT: '{self.environment}'")
        logger.info(f"   📍 DO_SPACES_ENDPOINT: {self.endpoint_url}")
        
        # Log the final configuration
        logger.info(f"🎯 S3 CONFIGURATION - Final Configuration:")
        logger.info(f"   🪣 Bucket Name: '{self.bucket_name}'")
        logger.info(f"   🌍 Environment: '{self.environment}'")
        logger.info(f"   🌐 Region: '{self.region}'")
        logger.info(f"   🔗 Endpoint: {self.endpoint_url}")
        
        missing_vars = []
        if not aws_access_key_id:
            missing_vars.append("DO_SPACES_KEY")
        if not aws_secret_access_key:
            missing_vars.append("DO_SPACES_SECRET")
        if not self.bucket_name:
            missing_vars.append("DO_SPACES_BUCKET")
        
        if missing_vars:
            error_msg = f"Missing required environment variables: {', '.join(missing_vars)}"
            logger.error(f"❌ S3 CONFIGURATION ERROR: {error_msg}")
            raise ValueError(error_msg)
        
        # Initialize S3 client with DigitalOcean Spaces endpoint
        self.s3_client = boto3.client(
            's3',
            region_name=self.region,
            endpoint_url=self.endpoint_url,
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key
        )
        
        logger.info(f"✅ S3 SERVICE INITIALIZED SUCCESSFULLY")
        logger.info(f"   🪣 Using Bucket: '{self.bucket_name}'")
        logger.info(f"   🌍 Environment: '{self.environment}'")
        logger.info(f"   🌐 Endpoint: {self.endpoint_url}")
        logger.info(f"   🔑 Credentials: {'Present' if aws_access_key_id and aws_secret_access_key else 'Missing'}")
        
        # Log the complete configuration summary
        self.log_configuration_summary()
    
    def get_configuration_summary(self):
        """
        Get a summary of the current S3 configuration for debugging
        
        Returns:
            dict: Configuration summary
        """
        return {
            "bucket_name": self.bucket_name,
            "environment": self.environment,
            "region": self.region,
            "endpoint_url": self.endpoint_url,
            "has_credentials": bool(os.getenv("DO_SPACES_KEY") and os.getenv("DO_SPACES_SECRET"))
        }
    
    def log_configuration_summary(self):
        """Log the current S3 configuration summary"""
        config = self.get_configuration_summary()
        logger.info(f"📋 S3 CONFIGURATION SUMMARY:")
        logger.info(f"   🪣 Bucket Name: '{config['bucket_name']}'")
        logger.info(f"   🌍 Environment: '{config['environment']}'")
        logger.info(f"   🌐 Region: '{config['region']}'")
        logger.info(f"   🔗 Endpoint: {config['endpoint_url']}")
        logger.info(f"   🔑 Credentials: {'Present' if config['has_credentials'] else 'Missing'}")
        logger.info(f"   📁 Expected Folder Structure: {config['environment']}/tenant_id_knowledgebase/chatbot_id/files/")
    
    def _generate_s3_key(self, tenant_id, chatbot_id, filename):
        """
        Generate S3 key with environment-specific folder structure
        
        Args:
            tenant_id: The tenant ID
            chatbot_id: The chatbot ID
            filename: The filename
            
        Returns:
            The S3 key (path) with environment prefix
        """
        s3_key = f"{self.environment}/{tenant_id}_knowledgebase/{chatbot_id}/files/{filename}"
        
        # Log the S3 key generation
        logger.info(f"🔑 S3 KEY GENERATION - Creating S3 key:")
        logger.info(f"   🌍 Environment: '{self.environment}'")
        logger.info(f"   👥 Tenant ID: '{tenant_id}'")
        logger.info(f"   🤖 Chatbot ID: '{chatbot_id}'")
        logger.info(f"   📁 Filename: '{filename}'")
        logger.info(f"   🎯 Final S3 Key: '{s3_key}'")
        logger.info(f"   🪣 Target Bucket: '{self.bucket_name}'")
        
        return s3_key
    
    def upload_file(self, file_data, tenant_id, chatbot_id, filename, content_type="application/pdf"):
        """
        Upload a file to S3 bucket with the specified folder structure
        
        Args:
            file_data: The file data as bytes or file-like object
            tenant_id: The tenant ID
            chatbot_id: The chatbot ID
            filename: The original filename
            content_type: The content type of the file
        
        Returns:
            The S3 object key (path) where the file was stored
        """
        try:
            logger.info(f"📤 S3 UPLOAD - Starting file upload:")
            logger.info(f"   🪣 Target Bucket: '{self.bucket_name}'")
            logger.info(f"   🌍 Environment: '{self.environment}'")
            logger.info(f"   👥 Tenant ID: '{tenant_id}'")
            logger.info(f"   🤖 Chatbot ID: '{chatbot_id}'")
            logger.info(f"   📁 Original Filename: '{filename}'")
            logger.info(f"   📋 Content Type: '{content_type}'")
            
            # Generate a random string to ensure filename uniqueness
            random_suffix = uuid.uuid4().hex[:8]
            
            # Split filename and extension
            name, ext = os.path.splitext(filename)
            
            # Create unique filename with random suffix
            unique_filename = f"{name}_{random_suffix}{ext}"
            logger.info(f"   🔐 Unique Filename: '{unique_filename}'")
            
            # Create the S3 key with environment-specific folder structure
            s3_key = self._generate_s3_key(tenant_id, chatbot_id, unique_filename)
            
            logger.info(f"   🎯 Uploading to S3:")
            logger.info(f"      🪣 Bucket: '{self.bucket_name}'")
            logger.info(f"      🔑 Key: '{s3_key}'")
            logger.info(f"      🌐 Endpoint: {self.endpoint_url}")
            
            # Upload the file to S3
            self.s3_client.upload_fileobj(
                file_data,
                self.bucket_name,
                s3_key,
                ExtraArgs={
                    'ContentType': content_type
                }
            )
            
            logger.info(f"✅ S3 UPLOAD SUCCESSFUL")
            logger.info(f"   🪣 File uploaded to bucket: '{self.bucket_name}'")
            logger.info(f"   🔑 S3 key: '{s3_key}'")
            logger.info(f"   🌍 Environment folder: '{self.environment}/'")
            logger.info(f"   📁 Full path: '{self.bucket_name}/{s3_key}'")
            
            return s3_key
        except ClientError as e:
            logger.error(f"❌ S3 UPLOAD FAILED")
            logger.error(f"   🪣 Bucket: '{self.bucket_name}'")
            logger.error(f"   🔑 S3 key: '{s3_key if 's3_key' in locals() else 'N/A'}'")
            logger.error(f"   🌍 Environment: '{self.environment}'")
            logger.error(f"   📁 Filename: '{filename}'")
            logger.error(f"   💥 Error: {str(e)}")
            raise
    
    def download_file(self, s3_key):
        """
        Download a file from S3 bucket
        
        Args:
            s3_key: The S3 object key (path)
            
        Returns:
            The file data as bytes
        """
        try:
            # Create a BytesIO object to store the downloaded file
            file_data = BytesIO()
            
            # Download the file from S3
            self.s3_client.download_fileobj(
                self.bucket_name,
                s3_key,
                file_data
            )
            
            # Reset the file pointer to the beginning
            file_data.seek(0)
            
            logger.info(f"Successfully downloaded file from S3: {s3_key}")
            return file_data
        except ClientError as e:
            logger.error(f"Error downloading file from S3: {str(e)}")
            raise
    
    def delete_file(self, s3_key):
        """
        Delete a file from S3 bucket
        
        Args:
            s3_key: The S3 object key (path)
            
        Returns:
            True if the file was deleted successfully, False otherwise
        """
        try:
            # Delete the file from S3
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )
            
            logger.info(f"Successfully deleted file from S3: {s3_key}")
            return True
        except ClientError as e:
            logger.error(f"Error deleting file from S3: {str(e)}")
            return False
    
    def get_file_url(self, s3_key, expiration=3600):
        """
        Generate a presigned URL for a file in S3
        
        Args:
            s3_key: The S3 object key (path)
            expiration: The expiration time in seconds (default: 1 hour)
            
        Returns:
            The presigned URL
        """
        try:
            # Generate a presigned URL
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': self.bucket_name,
                    'Key': s3_key
                },
                ExpiresIn=expiration
            )
            
            logger.info(f"Generated presigned URL for S3 file: {s3_key}")
            return url
        except ClientError as e:
            logger.error(f"Error generating presigned URL for S3 file: {str(e)}")
            raise