from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from app.models import (
    <PERSON>t<PERSON>, ChatbotNode, ChatbotNodeEntityField, <PERSON><PERSON>botEdge,
    ChatbotNodeCreate, ChatbotEdgeCreate, RuleBasedChatbotFlow
)
from app.exceptions import (
    RuleBasedFlowValidationError,
    RuleBasedFlowCreationError,
    RuleBasedNodeValidationError,
    RuleBasedEdgeValidationError,
    RuleBasedFlowLoopDetectionError,
    DatabaseQueryError
)
import logging
import json

logger = logging.getLogger(__name__)


class RuleBasedChatbotService:
    """
    Service for managing rule-based chatbot flows
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def _normalize_list_node_sections(self, node_data_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalize list node sections to ensure id and position fields are present.
        This ensures data consistency when storing in the database.
        """
        if node_data_dict.get('sections'):
            for i, section in enumerate(node_data_dict['sections']):
                if isinstance(section, dict):
                    # Ensure section has id field
                    if 'id' not in section or not section['id']:
                        section['id'] = f"section-{i}"
                    # Ensure section has position field
                    if 'position' not in section:
                        section['position'] = i
        return node_data_dict
    
    def create_flow(
        self,
        db: Session,
        chatbot_id: str,
        tenant_id: int,
        flow_data: RuleBasedChatbotFlow
    ) -> Dict[str, Any]:
        """
        Create or update a complete rule-based chatbot flow
        """
        try:
            # Get allowed entities for this chatbot FIRST (before validation)
            allowed_entities = self._get_allowed_entities(db, chatbot_id, tenant_id)

            # Filter flow data to remove entities not in chatbot configuration
            filtered_flow_data = self._filter_flow_by_entities(flow_data, allowed_entities)

            # Validate the filtered flow (only validates entities that are configured)
            self.validate_flow(filtered_flow_data)

            # Perform loop detection validation
            self._validate_no_loops(db, chatbot_id, tenant_id, filtered_flow_data)

            # Delete existing flow if it exists
            self._delete_existing_flow(db, chatbot_id)

            # Create nodes (using filtered data)
            created_nodes = self._create_nodes(db, chatbot_id, tenant_id, filtered_flow_data.nodes, allowed_entities)

            # Create mapping from node names to node_id values for edge creation
            node_name_to_id_map = {node.name: node.node_id for node in created_nodes}

            # Create edges
            created_edges = self._create_edges(db, chatbot_id, tenant_id, filtered_flow_data.edges, node_name_to_id_map)

            # Calculate and store isFirstNode for all nodes based on edge structure
            self._calculate_and_store_first_nodes(db, chatbot_id, tenant_id, created_nodes, created_edges)

            # Validate isFirstNode consistency between UI and calculated values
            self._validate_first_node_consistency(db, chatbot_id, tenant_id, filtered_flow_data.nodes, created_edges)

            db.commit()

            return {
                "message": "Rule-based chatbot flow created successfully",
                "chatbot_id": chatbot_id,
                "nodes_count": len(created_nodes),
                "edges_count": len(created_edges)
            }

        except (RuleBasedFlowValidationError, RuleBasedEdgeValidationError, RuleBasedNodeValidationError, RuleBasedFlowLoopDetectionError):
            # Re-raise validation and loop detection errors as-is
            db.rollback()
            raise
        except Exception as e:
            # Handle any other database or unexpected errors
            db.rollback()
            self.logger.error(f"Error creating rule-based flow: {str(e)}")
            raise RuleBasedFlowCreationError(
                chatbot_id=chatbot_id,
                operation="flow_creation",
                message="An unexpected error occurred while creating the rule-based chatbot flow"
            )
    

    def validate_flow(
        self,
        flow_data: RuleBasedChatbotFlow
    ) -> Dict[str, Any]:
        """
        Validate a rule-based chatbot flow without saving it
        """
        validation_errors = []
        warnings = []
        
        # Check for empty flow
        if not flow_data.nodes:
            validation_errors.append("Flow must contain at least one node")
        
        # Validate nodes
        node_names = set()
        node_ids = set()  # Track node IDs for duplicate checking
        node_validation_errors = []  # Track node-specific errors
        
        for node in flow_data.nodes:
            node_errors = []  # Errors specific to this node
            
            # Validate node_id length (255 character limit)
            if node.id and len(node.id) > 255:
                node_errors.append(f"Node ID exceeds maximum length of 255 characters. Current length: {len(node.id)} characters")
            
            # Validate node_id is not empty
            if not node.id or not node.id.strip():
                node_errors.append("Node ID cannot be empty")
            
            # Check for duplicate node IDs within the same flow
            if node.id and node.id in node_ids:
                node_errors.append(f"Duplicate node ID '{node.id}' found in flow")
            elif node.id:
                node_ids.add(node.id)
            
            # Check for duplicate node names
            if node.name in node_names:
                node_errors.append(f"Duplicate node name")
            node_names.add(node.name)
            
            # Validate node type
            if node.type not in ["question", "buttons", "list", "sendMessage", "condition"]:
                node_errors.append(f"Invalid node type '{node.type}'. Must be one of: question, buttons, list, sendMessage, condition")
            
            # Validate required fields based on node type
            if node.type == "question":
                if not node.data.text:
                    node_errors.append("Question node must have text")
                # Options are optional for question nodes - can be null or empty array
            
            elif node.type == "buttons":
                if not node.data.buttons:
                    node_errors.append("Buttons node must have buttons")
                else:
                    # Validate maximum 3 buttons allowed
                    if len(node.data.buttons) > 3:
                        node_errors.append(f"Buttons node can have maximum 3 buttons, found {len(node.data.buttons)}")
                    
                    # Validate button structure
                    for i, btn in enumerate(node.data.buttons or []):
                        if not hasattr(btn, 'name') or not btn.name:
                            node_errors.append(f"Button {i+1} must have a 'name' field")
                        if not hasattr(btn, 'text') or not btn.text:
                            node_errors.append(f"Button {i+1} must have a 'text' field")
                        if not hasattr(btn, 'position') or btn.position is None:
                            node_errors.append(f"Button {i+1} must have a 'position' field")
            
            elif node.type == "list":
                # Validate list node structure
                if not hasattr(node.data, 'sections') or not node.data.sections:
                    node_errors.append("List node must have sections")
                else:
                    # Validate sections
                    sections = node.data.sections
                    if not isinstance(sections, list) or len(sections) == 0:
                        node_errors.append("List node must have at least one section")
                    else:
                        # Validate maximum 10 sections allowed
                        if len(sections) > 10:
                            node_errors.append(f"List node can have maximum 10 sections, found {len(sections)}")
                        
                        # Track total rows across all sections
                        total_rows = 0
                        
                        for i, section in enumerate(sections):
                            if not hasattr(section, 'rows') or not section.rows:
                                node_errors.append(f"Section {i+1} must have rows")
                            else:
                                # Validate rows in each section
                                rows = section.rows
                                if not isinstance(rows, list) or len(rows) == 0:
                                    node_errors.append(f"Section {i+1} must have at least one row")
                                else:
                                    # Count total rows
                                    total_rows += len(rows)
                                    
                                    for j, row in enumerate(rows):
                                        # Check if row has required fields (rows are dictionaries)
                                        if not isinstance(row, dict):
                                            node_errors.append(f"Row {j+1} in section {i+1} must be a dictionary")
                                        else:
                                            if not row.get('id'):
                                                node_errors.append(f"Row {j+1} in section {i+1} must have an 'id' field")
                                            if not row.get('text'):
                                                node_errors.append(f"Row {j+1} in section {i+1} must have a 'text' field")
                        
                        # Validate maximum 10 total rows across all sections
                        if total_rows > 10:
                            node_errors.append(f"List node can have maximum 10 rows in total across all sections, found {total_rows}")
            
            elif node.type == "sendMessage":
                # SendMessage nodes can have either text or options
                if not node.data.text and not node.data.options:
                    node_errors.append("SendMessage node must have either text or options")
            
            # Validate variable mapping structure
            if node.variableMapping:
                for i, var_map in enumerate(node.variableMapping):
                    if not isinstance(var_map, dict):
                        node_errors.append(f"Variable mapping {i+1} must be a dictionary")
                        continue
                    
                    # Check required fields
                    required_fields = ["componentType", "variable", "entity", "internalName", "fallbackValue", "fieldType"]
                    for field in required_fields:
                        if field not in var_map:
                            node_errors.append(f"Variable mapping {i+1} missing required field: {field}")
                    
                    # Validate componentType
                    if "componentType" in var_map and var_map["componentType"] not in ["BODY", "HEADER", "FOOTER"]:
                        node_errors.append(f"Variable mapping {i+1} has invalid componentType: {var_map['componentType']}. Must be one of: BODY, HEADER, FOOTER")
                    
                    # Validate fieldType
                    valid_field_types = ["TEXT_FIELD", "PARAGRAPH_TEXT", "PICK_LIST", "NUMBER", "DATE", "BOOLEAN", "EMAIL", "PHONE"]
                    if "fieldType" in var_map and var_map["fieldType"] not in valid_field_types:
                        node_errors.append(f"Variable mapping {i+1} has invalid fieldType: {var_map['fieldType']}. Must be one of: {', '.join(valid_field_types)}")
                    
                    # Validate entity
                    valid_entities = ["lead", "contact", "account", "opportunity", "deal"]
                    if "entity" in var_map and var_map["entity"] not in valid_entities:
                        node_errors.append(f"Variable mapping {i+1} has invalid entity: {var_map['entity']}. Must be one of: {', '.join(valid_entities)}")
                    
                    # Validate variable ID is numeric
                    if "variable" in var_map and (not isinstance(var_map["variable"], (int, str)) or (isinstance(var_map["variable"], str) and not var_map["variable"].isdigit())):
                        node_errors.append(f"Variable mapping {i+1} variable must be numeric")
                    
                    # ID field removed - no longer required for variable mappings
            
            # If there are errors for this node, add them to the main validation errors
            if node_errors:
                for error in node_errors:
                    validation_errors.append(f"Node '{node.name}' (ID: {node.id}, Type: {node.type}): {error}")
                    node_validation_errors.append({
                        "nodeId": node.id,
                        "nodeName": node.name,
                        "nodeType": node.type,
                        "error": error
                    })
        
        # Validate edges
        source_nodes = set()
        target_nodes = set()
        edge_validation_errors = []  # Track edge-specific errors
        
        for edge in flow_data.edges:
            edge_errors = []  # Errors specific to this edge
            edge_id = edge.id or f"edge-{edge.source}-{edge.target}"
            
            source_nodes.add(edge.source)
            target_nodes.add(edge.target)
            
            # Check if source and target nodes exist
            if edge.source not in node_names:
                edge_errors.append(f"References non-existent source node: {edge.source}")
            if edge.target not in node_names:
                edge_errors.append(f"References non-existent target node: {edge.target}")
            
            # Validate edge has required fields
            if not edge.source:
                edge_errors.append("Missing source node")
            if not edge.target:
                edge_errors.append("Missing target node")
            
            # If there are errors for this edge, add them to the main validation errors
            if edge_errors:
                for error in edge_errors:
                    validation_errors.append(f"Edge '{edge_id}' (Source: {edge.source}, Target: {edge.target}): {error}")
                    edge_validation_errors.append({
                        "edgeId": edge_id,
                        "sourceNode": edge.source,
                        "targetNode": edge.target,
                        "error": error
                    })
        
        # Check for orphaned nodes (nodes with no connections)
        orphaned_nodes = node_names - source_nodes - target_nodes
        if orphaned_nodes:
            warnings.append(f"Orphaned nodes found: {', '.join(orphaned_nodes)}")
        
        # Check for nodes with no outgoing edges (except sendMessage and condition nodes)
        nodes_with_no_outgoing = node_names - source_nodes
        for node in flow_data.nodes:
            if node.name in nodes_with_no_outgoing and node.type not in ["sendMessage", "condition"]:
                warnings.append(f"Node '{node.name}' has no outgoing edges")
        
        if validation_errors:
            # Raise custom exception with detailed error information
            details = {
                "validationErrors": validation_errors,
                "validationWarnings": warnings,
                "nodeErrors": node_validation_errors,
                "edgeErrors": edge_validation_errors,
                "summary": {
                    "totalErrors": len(validation_errors),
                    "nodeErrorCount": len(node_validation_errors),
                    "edgeErrorCount": len(edge_validation_errors)
                }
            }
            raise RuleBasedFlowValidationError(
                validation_errors=validation_errors,
                validation_warnings=warnings,
                message="Rule-based chatbot flow validation failed",
                details=details
            )
        
        return {
            "valid": True,
            "warnings": warnings,
            "summary": {
                "total_nodes": len(flow_data.nodes),
                "total_edges": len(flow_data.edges),
                "node_types": {node.type: len([n for n in flow_data.nodes if n.type == node.type]) for node in flow_data.nodes}
            }
        }
    
    def _delete_existing_flow(self, db: Session, chatbot_id: str):
        """Delete existing flow for a chatbot"""
        # Delete edges first
        db.query(ChatbotEdge).filter(
            ChatbotEdge.chatbot_id == chatbot_id
        ).delete()
        
        # Get node IDs before deleting nodes (needed for entity fields)
        node_ids = db.query(ChatbotNode.node_id).filter(
            ChatbotNode.chatbot_id == chatbot_id
        ).all()
        node_id_list = [node_id[0] for node_id in node_ids]
        
        # Delete entity fields for these nodes
        if node_id_list:
            db.query(ChatbotNodeEntityField).filter(
                ChatbotNodeEntityField.node_id.in_(node_id_list)
            ).delete()
        
        # Delete nodes
        db.query(ChatbotNode).filter(
            ChatbotNode.chatbot_id == chatbot_id
        ).delete()
    
    def _create_nodes(
        self,
        db: Session,
        chatbot_id: str,
        tenant_id: int,
        nodes_data: List[ChatbotNodeCreate],
        allowed_entities: Optional[List[str]] = None
    ) -> List[ChatbotNode]:
        """Create nodes for the flow"""
        created_nodes = []

        for node_data in nodes_data:
            # Log isFirstNode information for tracking
            ui_is_first_node = getattr(node_data, 'isFirstNode', False)
            self.logger.info(f"🏁 CREATING NODE - Node {node_data.id} ({node_data.name}) with UI isFirstNode: {ui_is_first_node}")

            # Extract entity fields from node data
            entity_fields = []
            if node_data.data.entityFields:
                for field in node_data.data.entityFields:
                    # Skip entity fields without required fields (entityType and fieldId are required)
                    if not field.get("entityType") or not field.get("fieldId"):
                        self.logger.warning(f"Skipping entity field with missing required fields: {field}")
                        continue

                    # Validate entity type against allowed entities
                    entity_type = field.get("entityType", "")
                    if allowed_entities is not None:
                        # If chatbot has entity restrictions, validate against them
                        if entity_type.upper() not in allowed_entities:
                            self.logger.warning(
                                f"🚫 ENTITY FILTER - Skipping entity field for entity '{entity_type}' "
                                f"as it's not in chatbot's allowed entities: {allowed_entities}"
                            )
                            continue

                    # Get name field, use empty string if not provided
                    name_value = field.get("name", "")
                    if not name_value:
                        self.logger.warning(f"Entity field missing name field, using empty string: {field}")

                    entity_field = ChatbotNodeEntityField(
                        entity_type=entity_type,
                        field_id=field["fieldId"],
                        name=name_value,
                        display_name=field["displayName"],
                        standard=field.get("standard", False)
                    )
                    entity_fields.append(entity_field)
            
            # Filter variable mappings based on allowed entities
            filtered_variable_mapping = []
            if node_data.variableMapping:
                for var_map in node_data.variableMapping:
                    entity_type = var_map.get("entity", "")
                    if allowed_entities is not None:
                        # If chatbot has entity restrictions, validate against them
                        if entity_type.upper() not in allowed_entities:
                            self.logger.warning(
                                f"🚫 ENTITY FILTER - Skipping variable mapping for entity '{entity_type}' "
                                f"as it's not in chatbot's allowed entities: {allowed_entities}"
                            )
                            continue
                    filtered_variable_mapping.append(var_map)

            # Create node data including isFirstNode for reference (stored in JSON data field)
            node_data_dict = node_data.data.dict()
            node_data_dict['ui_isFirstNode'] = ui_is_first_node

            # Normalize list node sections to ensure id and position fields are present
            if node_data.type == "list":
                node_data_dict = self._normalize_list_node_sections(node_data_dict)

            # Log entityFields to verify they're being saved
            if node_data.type in ["question", "buttons", "list"]:
                entity_fields_in_data = node_data_dict.get('entityFields')
                logger.info(f"🔍 NODE CREATION DEBUG: Node {node_data.id} type={node_data.type}, entityFields in data: {entity_fields_in_data}")  # Store UI-provided value for reference

            node = ChatbotNode(
                chatbot_id=chatbot_id,
                tenant_id=tenant_id,
                node_id=node_data.id,
                name=node_data.name,
                type=node_data.type,
                position_x=node_data.position.x,
                position_y=node_data.position.y,
                data=node_data_dict,
                variable_mapping=filtered_variable_mapping  # Use filtered variable mappings
            )
            
            db.add(node)
            db.flush()  # Flush to get the node ID
            
            # Add entity fields
            for entity_field in entity_fields:
                entity_field.node_id = node.node_id  # Use node_id (frontend ID), not primary key id
                db.add(entity_field)
            
            created_nodes.append(node)
        
        return created_nodes
    
    def _validate_first_node_consistency(
        self,
        db: Session,
        chatbot_id: str,
        tenant_id: int,
        nodes_data: List[ChatbotNodeCreate],
        created_edges: List[ChatbotEdge]
    ) -> None:
        """
        Validate that UI-provided isFirstNode values match calculated first node logic
        """
        try:
            # Get all target nodes from edges (nodes that have incoming edges)
            target_node_ids = {edge.target_node for edge in created_edges if edge.target_node}
            
            # Check each node's isFirstNode value against calculated value
            for node_data in nodes_data:
                ui_is_first_node = getattr(node_data, 'isFirstNode', False)
                calculated_is_first_node = node_data.id not in target_node_ids
                
                if ui_is_first_node != calculated_is_first_node:
                    self.logger.warning(
                        f"🏁 FIRST NODE MISMATCH - Node {node_data.id} ({node_data.name}): "
                        f"UI says isFirstNode={ui_is_first_node}, calculated={calculated_is_first_node}"
                    )
                else:
                    self.logger.info(
                        f"🏁 FIRST NODE MATCH - Node {node_data.id} ({node_data.name}): "
                        f"UI and calculated both agree isFirstNode={ui_is_first_node}"
                    )
            
            # Count and log summary
            ui_first_nodes = [n for n in nodes_data if getattr(n, 'isFirstNode', False)]
            calculated_first_nodes = [n for n in nodes_data if n.id not in target_node_ids]
            
            self.logger.info(
                f"🏁 FIRST NODE SUMMARY - UI marked {len(ui_first_nodes)} as first, "
                f"calculated {len(calculated_first_nodes)} as first"
            )
            
        except Exception as e:
            self.logger.error(f"Error validating first node consistency: {str(e)}")
            # Don't raise the error, just log it as this is validation/logging only
    
    def _calculate_and_store_first_nodes(
        self,
        db: Session,
        chatbot_id: str,
        tenant_id: int,
        created_nodes: List[ChatbotNode],
        created_edges: List[ChatbotEdge]
    ) -> None:
        """
        Calculate which nodes are first nodes and store this information in the database.
        Respects UI-provided isFirstNode values and ensures only one node can be true.
        """
        try:
            # First, set all nodes to False
            for node in created_nodes:
                node.is_first_node = False
            
            # Find nodes that have UI-provided isFirstNode = True
            ui_first_nodes = []
            for node in created_nodes:
                # Check if the node's data contains ui_isFirstNode = True
                if node.data and node.data.get('ui_isFirstNode', False):
                    ui_first_nodes.append(node)
            
            # If multiple nodes have ui_isFirstNode = True, only keep the first one
            if len(ui_first_nodes) > 1:
                self.logger.warning(
                    f"🏁 MULTIPLE FIRST NODES - Found {len(ui_first_nodes)} nodes with isFirstNode=True. "
                    f"Only the first one will be set as first node."
                )
                # Keep only the first node as first node
                ui_first_nodes = [ui_first_nodes[0]]
            
            # Set the selected node(s) as first node
            for node in ui_first_nodes:
                node.is_first_node = True
                self.logger.info(
                    f"🏁 SETTING FIRST NODE - Node {node.node_id} ({node.name}): "
                    f"is_first_node=True (from UI)"
                )
            
            # If no UI first nodes, fall back to calculated logic (nodes with no incoming edges)
            if not ui_first_nodes:
                target_node_ids = {edge.target_node for edge in created_edges if edge.target_node}
                calculated_first_nodes = [node for node in created_nodes if node.node_id not in target_node_ids]
                
                if calculated_first_nodes:
                    # Set the first calculated node as first node
                    first_node = calculated_first_nodes[0]
                    first_node.is_first_node = True
                    self.logger.info(
                        f"🏁 SETTING CALCULATED FIRST NODE - Node {first_node.node_id} ({first_node.name}): "
                        f"is_first_node=True (calculated from edges)"
                    )
            
            # Flush to ensure database is updated
            db.flush()
            
            # Log summary
            first_nodes_count = sum(1 for node in created_nodes if node.is_first_node)
            self.logger.info(
                f"🏁 FIRST NODE STORAGE COMPLETE - {first_nodes_count} out of {len(created_nodes)} nodes marked as first"
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating and storing first nodes: {str(e)}")
            raise
    
    def _create_edges(
        self,
        db: Session,
        chatbot_id: str,
        tenant_id: int,
        edges_data: List[ChatbotEdgeCreate],
        node_name_to_id_map: Dict[str, str]
    ) -> List[ChatbotEdge]:
        """Create edges for the flow"""
        created_edges = []
        
        for edge_data in edges_data:
            # Map node names to node_id values for foreign key constraints
            source_node_id = node_name_to_id_map.get(edge_data.source)
            target_node_id = node_name_to_id_map.get(edge_data.target)
            
            if not source_node_id:
                raise RuleBasedEdgeValidationError(
                    edge_id=edge_data.id or f"edge-{edge_data.source}-{edge_data.target}",
                    validation_error=f"Source node '{edge_data.source}' not found in created nodes"
                )
            if not target_node_id:
                raise RuleBasedEdgeValidationError(
                    edge_id=edge_data.id or f"edge-{edge_data.source}-{edge_data.target}",
                    validation_error=f"Target node '{edge_data.target}' not found in created nodes"
                )
            
            edge = ChatbotEdge(
                chatbot_id=chatbot_id,
                tenant_id=tenant_id,
                edge_id=edge_data.id,
                source_node=source_node_id,  # Use node_id, not node name
                source_handle=edge_data.sourceHandle,
                target_node=target_node_id  # Use node_id, not node name
            )
            db.add(edge)
            created_edges.append(edge)
        
        return created_edges
    
    def _validate_no_loops(self, db: Session, chatbot_id: str, tenant_id: int, flow_data: RuleBasedChatbotFlow):
        """
        Validate that the chatbot flow contains no infinite loops.
        
        Args:
            db: Database session
            chatbot_id: Chatbot ID
            tenant_id: Tenant ID
            flow_data: Flow data to validate
            
        Raises:
            RuleBasedFlowValidationError: If loops are detected
        """
        try:
            from app.services.loop_detection_service import LoopDetectionService
            
            # Create temporary nodes and edges in memory for validation
            # We need to simulate the database structure for loop detection
            temp_nodes = []
            temp_edges = []
            
            # Create temporary node objects
            # Note: Edges reference nodes by NAME (not ID), so we use name as node_id
            # for consistency with edge source/target references
            for node_data in flow_data.nodes:
                temp_node = type('TempNode', (), {
                    'node_id': node_data.name,  # Use name because edges reference nodes by name
                    'name': node_data.name,
                    'type': node_data.type
                })()
                temp_nodes.append(temp_node)
            
            # Create temporary edge objects
            for edge_data in flow_data.edges:
                temp_edge = type('TempEdge', (), {
                    'source_node': edge_data.source,
                    'target_node': edge_data.target
                })()
                temp_edges.append(temp_edge)
            
            # Use loop detection service
            loop_service = LoopDetectionService()
            
            # Build adjacency list manually
            # Note: temp_node.node_id is set to node_data.name (see above)
            # to match edge references which use node names
            adjacency_list = {}
            for node in temp_nodes:
                adjacency_list[node.node_id] = []
            
            for edge in temp_edges:
                # Validate that both source and target nodes exist
                if edge.source_node not in adjacency_list:
                    self.logger.error(f"Edge references non-existent source node: {edge.source_node}")
                    raise RuleBasedFlowLoopDetectionError(
                        validation_errors=[f"Edge references non-existent source node: '{edge.source_node}'"],
                        message=f"Edge references non-existent source node: '{edge.source_node}'",
                        details={
                            "chatbot_id": chatbot_id,
                            "operation": "loop_detection",
                            "error": f"Source node '{edge.source_node}' not found in flow"
                        }
                    )
                if edge.target_node not in adjacency_list:
                    self.logger.error(f"Edge references non-existent target node: {edge.target_node}")
                    raise RuleBasedFlowLoopDetectionError(
                        validation_errors=[f"Edge references non-existent target node: '{edge.target_node}'"],
                        message=f"Edge references non-existent target node: '{edge.target_node}'",
                        details={
                            "chatbot_id": chatbot_id,
                            "operation": "loop_detection",
                            "error": f"Target node '{edge.target_node}' not found in flow"
                        }
                    )
                adjacency_list[edge.source_node].append(edge.target_node)
            
            # Run loop detection algorithms
            simple_cycles = loop_service._detect_simple_cycles(adjacency_list, temp_nodes)
            scc_loops = loop_service._detect_strongly_connected_components(adjacency_list, temp_nodes)
            chain_issues = loop_service._detect_long_chains(adjacency_list, temp_nodes)
            
            # Check for actual loops (cycles) - long chains are warnings, not errors
            has_actual_loops = len(simple_cycles) > 0 or len(scc_loops) > 0
            
            if has_actual_loops:
                warnings = loop_service._generate_warnings(simple_cycles + scc_loops, chain_issues)
                recommendations = loop_service._generate_recommendations(simple_cycles + scc_loops, chain_issues)
                
                error_message = "Infinite loops detected in chatbot flow:\n"
                error_message += "\n".join([f"• {warning}" for warning in warnings])
                error_message += "\n\nRecommendations:\n"
                error_message += "\n".join([f"• {rec}" for rec in recommendations])
                
                self.logger.error(f"Loop detection failed for chatbot {chatbot_id}: {error_message}")
                
                raise RuleBasedFlowLoopDetectionError(
                    validation_errors=warnings,
                    validation_warnings=recommendations,
                    message=error_message,
                    details={
                        "chatbot_id": chatbot_id,
                        "operation": "loop_detection",
                        "simple_cycles": simple_cycles,
                        "scc_loops": scc_loops,
                        "chain_issues": chain_issues,
                        "warnings": warnings,
                        "recommendations": recommendations
                    }
                )
            
            # Log long chains as warnings but don't block flow creation
            if len(chain_issues) > 0:
                chain_warnings = loop_service._generate_warnings([], chain_issues)
                chain_recommendations = loop_service._generate_recommendations([], chain_issues)
                
                warning_message = "Long chains detected in chatbot flow (non-blocking):\n"
                warning_message += "\n".join([f"• {warning}" for warning in chain_warnings])
                warning_message += "\n\nRecommendations:\n"
                warning_message += "\n".join([f"• {rec}" for rec in chain_recommendations])
                
                self.logger.warning(f"Long chains detected for chatbot {chatbot_id}: {warning_message}")
            
            self.logger.info(f"✅ Loop detection passed for chatbot {chatbot_id} - no infinite loops detected")
            
        except (RuleBasedFlowValidationError, RuleBasedFlowLoopDetectionError):
            # Re-raise validation and loop detection errors
            raise
        except Exception as e:
            self.logger.error(f"Error in loop detection validation for chatbot {chatbot_id}: {str(e)}")
            raise RuleBasedFlowLoopDetectionError(
                validation_errors=[f"Loop detection validation failed: {str(e)}"],
                message=f"Loop detection validation failed: {str(e)}",
                details={
                    "chatbot_id": chatbot_id,
                    "operation": "loop_detection",
                    "error": str(e)
                }
            )

    def _get_allowed_entities(self, db: Session, chatbot_id: str, tenant_id: int) -> Optional[List[str]]:
        """
        Get list of allowed entity types from chatbot configuration.

        Args:
            db: Database session
            chatbot_id: Chatbot ID
            tenant_id: Tenant ID

        Returns:
            List of allowed entity types (uppercase) or None if no restriction
        """
        try:
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                self.logger.warning(f"⚠️ ENTITY FILTER - Chatbot {chatbot_id} not found, allowing all entities")
                return None

            if not chatbot.entities:
                self.logger.info(f"🔓 ENTITY FILTER - No entities configured for chatbot {chatbot_id}, allowing all entities")
                return None

            # Extract entity types from entities configuration
            allowed_entities = [entity.get('entity', '').upper() for entity in chatbot.entities if entity.get('entity')]

            self.logger.info(f"🔒 ENTITY FILTER - Loaded {len(allowed_entities)} allowed entities for chatbot {chatbot_id}: {allowed_entities}")
            return allowed_entities

        except Exception as e:
            self.logger.error(f"Error getting allowed entities for chatbot {chatbot_id}: {str(e)}")
            return None

    def _filter_flow_by_entities(
        self,
        flow_data: RuleBasedChatbotFlow,
        allowed_entities: Optional[List[str]]
    ) -> RuleBasedChatbotFlow:
        """
        Filter flow data to remove variable mappings and entity fields for entities
        not configured in the chatbot.

        Args:
            flow_data: Original flow data
            allowed_entities: List of allowed entity types (uppercase) or None for no filtering

        Returns:
            Filtered flow data with only allowed entities
        """
        # If no entity restrictions, return original flow data
        if allowed_entities is None:
            self.logger.info("🔓 FLOW FILTER - No entity restrictions, using original flow data")
            return flow_data

        self.logger.info(f"🔒 FLOW FILTER - Filtering flow data for allowed entities: {allowed_entities}")

        # Create a deep copy of nodes to avoid modifying original data
        from copy import deepcopy
        filtered_nodes = []

        for node in flow_data.nodes:
            # Create a copy of the node
            node_dict = node.model_dump() if hasattr(node, 'model_dump') else node.dict()

            # Filter variable mappings
            if node_dict.get('variableMapping'):
                original_count = len(node_dict['variableMapping'])
                node_dict['variableMapping'] = [
                    vm for vm in node_dict['variableMapping']
                    if vm.get('entity', '').upper() in allowed_entities
                ]
                filtered_count = original_count - len(node_dict['variableMapping'])
                if filtered_count > 0:
                    self.logger.info(
                        f"🚫 FLOW FILTER - Node '{node.name}' ({node.id}): "
                        f"Filtered {filtered_count} variable mapping(s) not in allowed entities"
                    )

            # Filter entity fields in node data
            if node_dict.get('data') and node_dict['data'].get('entityFields'):
                original_count = len(node_dict['data']['entityFields'])
                node_dict['data']['entityFields'] = [
                    ef for ef in node_dict['data']['entityFields']
                    if ef.get('entityType', '').upper() in allowed_entities
                ]
                filtered_count = original_count - len(node_dict['data']['entityFields'])
                if filtered_count > 0:
                    self.logger.info(
                        f"🚫 FLOW FILTER - Node '{node.name}' ({node.id}): "
                        f"Filtered {filtered_count} entity field(s) not in allowed entities"
                    )

            # Recreate the node object with filtered data
            filtered_node = ChatbotNodeCreate(**node_dict)
            filtered_nodes.append(filtered_node)

        # Create new flow data with filtered nodes
        filtered_flow = RuleBasedChatbotFlow(
            nodes=filtered_nodes,
            edges=flow_data.edges  # Edges don't need filtering
        )

        self.logger.info(f"✅ FLOW FILTER - Flow data filtered successfully")
        return filtered_flow
