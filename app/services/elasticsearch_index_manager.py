import os
import logging
from typing import Dict, <PERSON>, Optional, <PERSON><PERSON>
from elasticsearch import Elasticsearch
from sqlalchemy.orm import Session
from app.models import TenantIndexMapping
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

class ElasticsearchIndexManager:
    """
    Manages Elasticsearch master indices and tenant aliases.
    
    Strategy:
    - Master indices: whatsapp-chatbot-1, whatsapp-chatbot-2, etc.
    - Each master index can hold up to 100 tenant aliases
    - Tenant aliases: tenant-{tenant_id} pointing to appropriate master index
    - Documents are stored in tenant aliases for isolation
    """
    
    def __init__(self):
        load_dotenv()
        
        # Initialize Elasticsearch client
        elasticsearch_url = "http://elasticsearch-vector-master:9200"
        self.es_client = Elasticsearch(elasticsearch_url)
        
        # Configuration
        self.master_index_prefix = "whatsapp-chatbot"
        self.tenant_alias_prefix = "tenant"
        self.max_tenants_per_index = 100
        
        # Index mapping configuration
        self.index_mapping = {
            "mappings": {
                "properties": {
                    "content": {"type": "text"},
                    "document_id": {"type": "keyword"},
                    "chatbot_id": {"type": "keyword"},
                    "chunk_id": {"type": "keyword"},
                    "chunk_index": {"type": "integer"},
                    "tenant_id": {"type": "long"},  # Changed from keyword to long for integer tenant IDs
                    "tenantId": {"type": "long"},   # Also support camelCase version as long
                    "embedding": {
                        "type": "dense_vector",
                        "dims": 1536,
                        "index": True,
                        "similarity": "cosine"
                    }
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "index.max_result_window": 50000
            }
        }
    
    def get_tenant_alias(self, tenant_id: str) -> str:
        """Get the alias name for a tenant."""
        return f"{self.tenant_alias_prefix}-{tenant_id}"
    
    def get_master_index_name(self, index_number: int) -> str:
        """Get the master index name for a given index number."""
        return f"{self.master_index_prefix}-{index_number}"
    
    def get_or_create_tenant_alias(self, tenant_id: str, db: Session) -> str:
        """
        Get or create a tenant alias and ensure it points to the correct master index.
        
        Args:
            tenant_id: The tenant ID
            db: Database session
            
        Returns:
            The tenant alias name
        """
        try:
            alias_name = self.get_tenant_alias(tenant_id)
            
            # Check if tenant already has an index mapping in database
            existing_mapping = db.query(TenantIndexMapping).filter(
                TenantIndexMapping.tenant_id == tenant_id
            ).first()
            
            if existing_mapping:
                # Verify the alias exists and points to the correct index
                master_index = self.get_master_index_name(existing_mapping.master_index_number)
                
                if self.es_client.indices.exists_alias(name=alias_name):
                    # Alias exists, verify it points to the correct index
                    alias_info = self.es_client.indices.get_alias(name=alias_name)
                    if master_index in alias_info:
                        logger.info(f"Tenant alias {alias_name} already exists and points to {master_index}")
                        return alias_name
                    else:
                        # Alias points to wrong index, update it
                        logger.warning(f"Tenant alias {alias_name} points to wrong index, updating...")
                        self._update_tenant_alias(alias_name, master_index)
                        return alias_name
                else:
                    # Alias doesn't exist, create it
                    self._create_tenant_alias(alias_name, master_index)
                    return alias_name
            
            # No existing mapping, find or create appropriate master index
            master_index_number = self._find_available_master_index(db)
            master_index = self.get_master_index_name(master_index_number)
            
            # Ensure master index exists
            self._ensure_master_index_exists(master_index)
            
            # Create tenant alias
            self._create_tenant_alias(alias_name, master_index)
            
            # Save mapping to database
            mapping = TenantIndexMapping(
                tenant_id=tenant_id,
                master_index_number=master_index_number,
                alias_name=alias_name
            )
            db.add(mapping)
            db.commit()
            
            logger.info(f"Created tenant alias {alias_name} pointing to {master_index}")
            return alias_name
            
        except Exception as e:
            logger.error(f"Error creating tenant alias for {tenant_id}: {str(e)}")
            db.rollback()
            raise
    
    def _find_available_master_index(self, db: Session) -> int:
        """
        Find an available master index that has space for more tenants.
        
        Args:
            db: Database session
            
        Returns:
            The master index number to use
        """
        # Get tenant count per master index
        from sqlalchemy import func
        
        index_counts = db.query(
            TenantIndexMapping.master_index_number,
            func.count(TenantIndexMapping.tenant_id).label('tenant_count')
        ).group_by(TenantIndexMapping.master_index_number).all()
        
        # Find an index with available space
        for index_number, tenant_count in index_counts:
            if tenant_count < self.max_tenants_per_index:
                return index_number
        
        # No available space in existing indices, create new one
        if index_counts:
            # Get the highest index number and increment
            max_index = max(count[0] for count in index_counts)
            return max_index + 1
        else:
            # No indices exist yet, start with 1
            return 1
    
    def _ensure_master_index_exists(self, master_index: str):
        """Ensure a master index exists with proper configuration."""
        if not self.es_client.indices.exists(index=master_index):
            self.es_client.indices.create(
                index=master_index,
                body=self.index_mapping
            )
            logger.info(f"Created master index: {master_index}")
    
    def _create_tenant_alias(self, alias_name: str, master_index: str):
        """Create a tenant alias pointing to a master index with tenant filtering."""
        # Extract tenant_id from alias name (format: tenant-{tenant_id})
        tenant_id_str = alias_name.replace(f"{self.tenant_alias_prefix}-", "")

        # Convert to integer for filtering
        try:
            tenant_id = int(tenant_id_str)
        except ValueError:
            logger.error(f"Invalid tenant_id format: {tenant_id_str}")
            raise ValueError(f"Tenant ID must be numeric: {tenant_id_str}")

        # Create alias with tenant filter using tenantId field (camelCase) as integer
        alias_body = {
            "filter": {
                "term": {
                    "tenantId": tenant_id
                }
            }
        }

        self.es_client.indices.put_alias(
            index=master_index,
            name=alias_name,
            body=alias_body
        )
        logger.info(f"Created alias {alias_name} pointing to {master_index} with tenant filter for tenantId: {tenant_id}")
    
    def _update_tenant_alias(self, alias_name: str, new_master_index: str):
        """Update a tenant alias to point to a different master index with tenant filtering."""
        # Extract tenant_id from alias name (format: tenant-{tenant_id})
        tenant_id = alias_name.replace(f"{self.tenant_alias_prefix}-", "")

        # Get current alias info
        current_alias_info = self.es_client.indices.get_alias(name=alias_name)

        # Remove alias from old indices and add to new index with filter
        actions = []
        for old_index in current_alias_info.keys():
            actions.append({"remove": {"index": old_index, "alias": alias_name}})

        # Add alias to new index with tenant filter using tenantId field (camelCase) as integer
        try:
            tenant_id_int = int(tenant_id)
        except ValueError:
            logger.error(f"Invalid tenant_id format: {tenant_id}")
            raise ValueError(f"Tenant ID must be numeric: {tenant_id}")

        actions.append({
            "add": {
                "index": new_master_index,
                "alias": alias_name,
                "filter": {
                    "term": {
                        "tenantId": tenant_id_int
                    }
                }
            }
        })

        self.es_client.indices.update_aliases(body={"actions": actions})
        logger.info(f"Updated alias {alias_name} to point to {new_master_index} with tenant filter for tenantId: {tenant_id}")
    
    def get_tenant_statistics(self, db: Session) -> Dict:
        """Get statistics about tenant distribution across master indices."""
        from sqlalchemy import func
        
        stats = db.query(
            TenantIndexMapping.master_index_number,
            func.count(TenantIndexMapping.tenant_id).label('tenant_count')
        ).group_by(TenantIndexMapping.master_index_number).all()
        
        result = {
            "total_master_indices": len(stats),
            "total_tenants": sum(stat[1] for stat in stats),
            "indices": {}
        }
        
        for index_number, tenant_count in stats:
            master_index = self.get_master_index_name(index_number)
            result["indices"][master_index] = {
                "tenant_count": tenant_count,
                "available_slots": self.max_tenants_per_index - tenant_count,
                "utilization_percentage": (tenant_count / self.max_tenants_per_index) * 100
            }
        
        return result

    def update_existing_aliases_with_filters(self, db: Session) -> Dict:
        """
        Update existing tenant aliases to include tenant filtering.
        This is useful for migrating aliases created before filter support.
        """
        update_results = {
            "updated": [],
            "errors": [],
            "skipped": []
        }

        try:
            # Get all tenant mappings from database
            tenant_mappings = db.query(TenantIndexMapping).all()

            for mapping in tenant_mappings:
                try:
                    alias_name = mapping.alias_name
                    master_index = self.get_master_index_name(mapping.master_index_number)
                    tenant_id = mapping.tenant_id

                    # Check if alias exists
                    if not self.es_client.indices.exists_alias(name=alias_name):
                        update_results["skipped"].append({
                            "alias": alias_name,
                            "reason": "Alias does not exist"
                        })
                        continue

                    # Get current alias info
                    current_alias_info = self.es_client.indices.get_alias(name=alias_name)

                    # Check if alias already has a filter
                    has_filter = False
                    for index_name, alias_details in current_alias_info.items():
                        if alias_name in alias_details.get('aliases', {}):
                            alias_config = alias_details['aliases'][alias_name]
                            if 'filter' in alias_config:
                                has_filter = True
                                break

                    if has_filter:
                        update_results["skipped"].append({
                            "alias": alias_name,
                            "reason": "Filter already exists"
                        })
                        continue

                    # Update alias to include filter
                    self._update_tenant_alias(alias_name, master_index)

                    update_results["updated"].append({
                        "alias": alias_name,
                        "tenant_id": tenant_id,
                        "master_index": master_index
                    })

                    logger.info(f"Updated alias {alias_name} to include tenant filter")

                except Exception as e:
                    error_msg = f"Failed to update alias {mapping.alias_name}: {str(e)}"
                    logger.error(error_msg)
                    update_results["errors"].append({
                        "alias": mapping.alias_name,
                        "error": error_msg
                    })

            logger.info(f"Alias filter update completed. Updated: {len(update_results['updated'])}, "
                       f"Errors: {len(update_results['errors'])}, Skipped: {len(update_results['skipped'])}")

        except Exception as e:
            logger.error(f"Error during alias filter update: {str(e)}")
            raise

        return update_results
    
    def migrate_existing_tenant_indices(self, db: Session) -> Dict:
        """
        Migrate existing tenant-specific indices to the new alias system.
        This is a one-time migration function.
        """
        migration_results = {
            "migrated": [],
            "errors": [],
            "skipped": []
        }
        
        try:
            # Get all existing indices that match the old pattern
            all_indices = self.es_client.indices.get("*")
            old_tenant_indices = [
                index for index in all_indices.keys() 
                if index.startswith("kb-") and not index.startswith(self.master_index_prefix)
            ]
            
            logger.info(f"Found {len(old_tenant_indices)} old tenant indices to migrate")
            
            for old_index in old_tenant_indices:
                try:
                    # Extract tenant_id from old index name (assuming format: kb-{tenant_id})
                    tenant_id = old_index.replace("kb-", "")
                    
                    # Get or create new tenant alias
                    alias_name = self.get_or_create_tenant_alias(tenant_id, db)
                    
                    # Reindex data from old index to new alias
                    reindex_body = {
                        "source": {"index": old_index},
                        "dest": {"index": alias_name}
                    }
                    
                    reindex_result = self.es_client.reindex(body=reindex_body, wait_for_completion=True)
                    
                    if reindex_result.get("failures"):
                        migration_results["errors"].append({
                            "tenant_id": tenant_id,
                            "old_index": old_index,
                            "error": "Reindex failures",
                            "details": reindex_result["failures"]
                        })
                    else:
                        migration_results["migrated"].append({
                            "tenant_id": tenant_id,
                            "old_index": old_index,
                            "new_alias": alias_name,
                            "documents_migrated": reindex_result.get("total", 0)
                        })
                        
                        # Optionally delete old index after successful migration
                        # self.es_client.indices.delete(index=old_index)
                        
                except Exception as e:
                    migration_results["errors"].append({
                        "tenant_id": tenant_id if 'tenant_id' in locals() else "unknown",
                        "old_index": old_index,
                        "error": str(e)
                    })
            
            return migration_results
            
        except Exception as e:
            logger.error(f"Error during migration: {str(e)}")
            raise
