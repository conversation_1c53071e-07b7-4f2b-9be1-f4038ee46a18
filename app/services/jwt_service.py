"""
JWT Service for internal token generation and IAM integration.

This service handles creating JWT tokens for internal use and fetching user data from IAM.
"""

from jose import jwt
import datetime
from datetime import timezone
import os
import requests
import logging
from typing import Tu<PERSON>, Dict, Any

from app.exceptions import (
    IAMServiceException,
    IAMUserNotFoundException,
    IAMPermissionException,
    JWTTokenCreationException,
    EnvironmentVariableError
)

logger = logging.getLogger(__name__)


class JWTService:
    """Service for JWT token generation and IAM integration"""
    
    def __init__(self):
        self.secret = os.getenv("SECRET_KEY", "test")
        self.iam_base_path = os.getenv("IAM_BASE_PATH", "http://sd-iam")
    
    def build_jwt_token_for_analysis(self, user_id: str, tenant_id: str) -> Tuple[str, int]:
        """
        Build a JWT token with tenant user permissions for internal use.
        
        Args:
            user_id: The user ID to create token for (will be used in final token)
            tenant_id: The tenant ID to create token for
            
        Returns:
            Tuple of (jwt_token, user_id_as_long)
            
        Raises:
            EnvironmentVariableError: If IAM_BASE_PATH is not configured
            IAMServiceException: If IAM service request fails
            IAMUserNotFoundException: If user is not found in IAM
            IAMPermissionException: If user has no permissions
            JWTTokenCreationException: If JWT token creation fails
        """
        now = datetime.datetime.now(timezone.utc)
        expiry = now + datetime.timedelta(minutes=5)  # 5-minute expiry

        # Full permission list for temporary token
        temp_permissions = [
            {
                "id": 1,
                "name": "user",
                "description": "has access to user resource",
                "action": {
                    "read": True, "readAll": True, "write": True,
                    "update": True, "updateAll": True
                }
            }
        ]

        # CoreAccessToken structure for temporary token
        temp_core_access_token = {
            "accessToken": "internal-token",
            "expiresIn": 5 * 60 * 1000,  # 5 minutes in milliseconds
            "permissions": temp_permissions,
            "expiry": int(expiry.timestamp() * 1000),  # Milliseconds
            "tenantId": str(tenant_id),
            "userId": str(user_id),
            "tokenType": "Bearer",
            "meta": {
                "rate-limit": 5,
                "pid": 2
            }
        }

        payload = {
            "iss": "sell",
            "data": temp_core_access_token,
            "exp": expiry
        }

        try:
            temp_token = jwt.encode(payload, self.secret, algorithm="HS256")
        except Exception as e:
            logger.error(f"Failed to create temporary JWT token: {str(e)}")
            raise JWTTokenCreationException(
                reason="temporary_token_creation_failed",
                message=f"Failed to create temporary JWT token: {str(e)}"
            )

        # Get tenant user permissions from IAM
        try:
            iam_user = self.get_tenant_creator_from_iam(tenant_id, temp_token)
            logger.info(f"Note Analysis : iam_user: {iam_user}")
        except Exception as e:
            logger.error(f"Failed to get user data from IAM: {str(e)}")
            raise IAMServiceException(
                operation="get_tenant_creator",
                message=f"Failed to get user data from IAM: {str(e)}"
            )
        
        # Validate user data
        if not iam_user or not iam_user.get("id"):
            logger.error(f"User not found in IAM for tenant {tenant_id}")
            raise IAMUserNotFoundException(
                tenant_id=tenant_id,
                user_id=user_id,
                message=f"User not found in IAM for tenant {tenant_id}"
            )
        
        permissions = iam_user.get("permissions")
        if not permissions:
            logger.error(f"No permissions found for tenant user {iam_user.get('id')}")
            raise IAMPermissionException(
                user_id=str(iam_user.get("id")),
                required_permissions=["user", "task", "customField"],
                message="No permissions found for tenant user"
            )
        logger.info(f"Note Analysis : user_id: {user_id} , tenant permissions : {permissions}")
        
        # Convert original user ID to long integer (not IAM user ID)
        try:
            actual_user_id = int(user_id)
        except (ValueError, TypeError) as e:
            logger.error(f"Invalid user ID format: {user_id}")
            raise JWTTokenCreationException(
                reason="invalid_user_id_format",
                message=f"Invalid user ID format: {user_id}"
            )

        # Create final token with tenant user permissions but original user ID
        core_access_token = {
            "accessToken": "internal-token",
            "expiresIn": 5 * 60 * 1000,  # 5 minutes in milliseconds
            "permissions": permissions,  # Use tenant user permissions
            "expiry": int(expiry.timestamp() * 1000),  # Milliseconds
            "tenantId": str(tenant_id),
            "userId": str(actual_user_id),  # Use original user ID
            "tokenType": "Bearer",
            "meta": {
                "rate-limit": 5,
                "pid": 2
            }
        }

        payload = {
            "iss": "sell",
            "data": core_access_token,
            "exp": expiry
        }

        try:
            token = jwt.encode(payload, self.secret, algorithm="HS256")
            token = token if isinstance(token, str) else token.decode("utf-8")
            return token, actual_user_id
        except Exception as e:
            logger.error(f"Failed to create final JWT token: {str(e)}")
            raise JWTTokenCreationException(
                reason="final_token_creation_failed",
                message=f"Failed to create final JWT token: {str(e)}"
            )

    def get_tenant_creator_from_iam(self, tenant_id: str, token: str) -> Dict[str, Any]:
        """
        Get tenant creator information and permissions from IAM service.
        
        Args:
            tenant_id: The tenant ID to get creator for
            token: JWT token for authentication
            
        Returns:
            Dict containing user information and permissions from IAM
            
        Raises:
            EnvironmentVariableError: If IAM_BASE_PATH is not configured
            IAMServiceException: If request to IAM fails
        """
        if not self.iam_base_path:
            logger.error("IAM_BASE_PATH environment variable is not set")
            raise EnvironmentVariableError(
                env_var="IAM_BASE_PATH",
                message="IAM_BASE_PATH environment variable is missing"
            )

        full_url = f"{self.iam_base_path}/v1/tenants/{tenant_id}/creator"

        headers = {
            "Authorization": f"Bearer {token}"
        }

        try:
            response = requests.get(full_url, headers=headers)
            response.raise_for_status()

            user_data = response.json()
            logger.info(f"Note Analysis : User data fetched from IAM: {user_data}")

            extracted_data = {
                "id": user_data.get("id"),
                "name": f"{user_data.get('firstName', '')} {user_data.get('lastName', '')}".strip(),
                "tenant_id": user_data.get("tenantId"),
                "email": user_data.get("email"),
                "permissions": user_data.get("permissions"),
            }

            logger.info(f"Note Analysis : User data extracted from IAM: {extracted_data}")
            return extracted_data

        except requests.exceptions.RequestException as e:
            logger.error(f"Note Analysis : Request error in get_user_from_iam - Error details: {str(e)}")
            raise IAMServiceException(
                operation="get_tenant_creator",
                message=f"IAM service request failed: {str(e)}"
            )


# Global instance
jwt_service = JWTService()
