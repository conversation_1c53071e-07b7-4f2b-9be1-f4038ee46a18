import asyncio
import logging
import os
from typing import Dict, Any, Optional
from app.services.rabbitmq_service import rabbitmq_service
from app.services.event_listeners import event_listener_manager

logger = logging.getLogger(__name__)


class RabbitMQManager:
    """
    Manager for RabbitMQ services including connection, publishers, and consumers
    """
    
    def __init__(self):
        self.is_initialized = False
        self.is_running = False
        self.health_check_task: Optional[asyncio.Task] = None
        self.health_check_interval = int(os.getenv("RABBITMQ_HEALTH_CHECK_INTERVAL", "60"))
        self.auto_recovery = os.getenv("RABBITMQ_AUTO_RECOVERY", "true").lower() == "true"
        
    async def initialize(self) -> bool:
        """
        Initialize RabbitMQ services
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        if self.is_initialized:
            logger.warning("RabbitMQ manager is already initialized")
            return True
        
        try:
            logger.info("Initializing RabbitMQ manager...")
            
            # Connect to RabbitMQ
            connected = await rabbitmq_service.connect()
            if not connected:
                logger.error("Failed to connect to RabbitMQ")
                return False
            
            # Setup publishers
            await rabbitmq_service.setup_all_publishers()
            
            # Setup listeners
            await rabbitmq_service.setup_scheduler_listener()
            await rabbitmq_service.setup_message_listener()
            await rabbitmq_service.setup_workflow_listener()
            
            self.is_initialized = True
            logger.info("RabbitMQ manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize RabbitMQ manager: {str(e)}")
            return False
    
    async def start(self) -> bool:
        """
        Start RabbitMQ services
        
        Returns:
            bool: True if start successful, False otherwise
        """
        if not self.is_initialized:
            logger.warning("RabbitMQ manager not initialized, initializing now...")
            if not await self.initialize():
                return False
        
        if self.is_running:
            logger.warning("RabbitMQ manager is already running")
            return True
        
        try:
            logger.info("Starting RabbitMQ services...")
            
            # Start event listeners
            await event_listener_manager.start_all()
            
            # Start health monitoring if auto recovery is enabled
            if self.auto_recovery:
                self.health_check_task = asyncio.create_task(self._health_monitor_loop())
            
            self.is_running = True
            logger.info("RabbitMQ services started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start RabbitMQ services: {str(e)}")
            return False
    
    async def stop(self):
        """
        Stop RabbitMQ services
        """
        if not self.is_running:
            logger.warning("RabbitMQ manager is not running")
            return
        
        try:
            logger.info("Stopping RabbitMQ services...")
            
            # Stop health monitoring
            if self.health_check_task and not self.health_check_task.done():
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass
            
            # Stop event listeners
            await event_listener_manager.stop_all()
            
            # Disconnect from RabbitMQ
            await rabbitmq_service.disconnect()
            
            self.is_running = False
            self.is_initialized = False
            logger.info("RabbitMQ services stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping RabbitMQ services: {str(e)}")
    
    async def restart(self) -> bool:
        """
        Restart RabbitMQ services
        
        Returns:
            bool: True if restart successful, False otherwise
        """
        logger.info("Restarting RabbitMQ services...")
        
        # Stop services
        await self.stop()
        
        # Wait a moment before restarting
        await asyncio.sleep(2)
        
        # Start services
        return await self.start()
    
    async def get_status(self) -> Dict[str, Any]:
        """
        Get comprehensive status of RabbitMQ services
        
        Returns:
            Dict containing status information
        """
        try:
            # Get RabbitMQ service status
            rabbitmq_status = rabbitmq_service.get_consumer_status()
            
            # Get health status
            is_healthy = await rabbitmq_service.is_healthy()
            
            # Get event listener status
            event_listener_status = {
                "scheduler_listener_running": event_listener_manager.scheduler_listener.is_running
            }
            
            return {
                "manager": {
                    "is_initialized": self.is_initialized,
                    "is_running": self.is_running,
                    "auto_recovery": self.auto_recovery,
                    "health_monitor_running": self.health_check_task and not self.health_check_task.done()
                },
                "rabbitmq": rabbitmq_status,
                "health": {
                    "is_healthy": is_healthy,
                    "last_check": "just_now"
                },
                "event_listeners": event_listener_status
            }
            
        except Exception as e:
            logger.error(f"Error getting RabbitMQ status: {str(e)}")
            return {
                "error": str(e),
                "manager": {
                    "is_initialized": self.is_initialized,
                    "is_running": self.is_running
                }
            }
    
    async def publish_event(self, event_name: str, payload: Any) -> bool:
        """
        Publish an event through RabbitMQ
        
        Args:
            event_name: Name of the event
            payload: Event payload
            
        Returns:
            bool: True if published successfully, False otherwise
        """
        try:
            if not self.is_running:
                logger.warning("⚠️ RABBITMQ MANAGER - Not running, cannot publish event")
                return False

            logger.info(f"🎯 RABBITMQ MANAGER - Publishing event: {event_name}")
            logger.info(f"🎯 RABBITMQ MANAGER - Event payload type: {type(payload)}")

            await rabbitmq_service.publish_event(event_name, payload)

            logger.info(f"✅ RABBITMQ MANAGER - Event published successfully: {event_name}")
            return True

        except Exception as e:
            logger.error(f"❌ RABBITMQ MANAGER - Failed to publish event '{event_name}': {str(e)}")
            return False
    
    async def publish_usage_data(self, routing_key: str, usage_data: Any) -> bool:
        """
        Publish usage data to the usage exchange
        
        Args:
            routing_key: Routing key for the usage data
            usage_data: Usage data payload
            
        Returns:
            bool: True if published successfully, False otherwise
        """
        try:
            if not self.is_running:
                logger.warning("⚠️ RABBITMQ MANAGER - Not running, cannot publish usage data")
                return False

            logger.info(f"📊 RABBITMQ MANAGER - Publishing usage data with routing key: {routing_key}")
            logger.info(f"📊 RABBITMQ MANAGER - Usage data type: {type(usage_data)}")
            if isinstance(usage_data, list):
                logger.info(f"📊 RABBITMQ MANAGER - Usage data count: {len(usage_data)} items")

            await rabbitmq_service.publish_message("ex.usage", routing_key, usage_data)

            logger.info(f"✅ RABBITMQ MANAGER - Usage data published successfully with routing key: {routing_key}")
            return True

        except Exception as e:
            logger.error(f"❌ RABBITMQ MANAGER - Failed to publish usage data with routing key '{routing_key}': {str(e)}")
            return False
    
    async def force_recovery(self, queue_name: str = None) -> bool:
        """
        Force recovery of consumers
        
        Args:
            queue_name: Specific queue to recover, or None for all queues
            
        Returns:
            bool: True if recovery successful, False otherwise
        """
        try:
            await rabbitmq_service.force_consumer_recovery(queue_name)
            return True
            
        except Exception as e:
            logger.error(f"Failed to force recovery: {str(e)}")
            return False
    
    async def _health_monitor_loop(self):
        """
        Continuous health monitoring loop
        """
        logger.info("Starting RabbitMQ health monitoring")
        
        while self.is_running:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                if not self.is_running:
                    break
                
                # Check health
                is_healthy = await rabbitmq_service.is_healthy()
                
                if not is_healthy:
                    logger.warning("RabbitMQ health check failed, attempting recovery")
                    
                    # Try to recover
                    recovery_success = await self._attempt_recovery()
                    
                    if recovery_success:
                        logger.info("RabbitMQ recovery successful")
                    else:
                        logger.error("RabbitMQ recovery failed")
                else:
                    logger.debug("RabbitMQ health check passed")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {str(e)}")
                await asyncio.sleep(60)  # Wait longer on error
        
        logger.info("RabbitMQ health monitoring stopped")
    
    async def _attempt_recovery(self) -> bool:
        """
        Attempt to recover RabbitMQ services
        
        Returns:
            bool: True if recovery successful, False otherwise
        """
        try:
            logger.info("Attempting RabbitMQ recovery...")
            
            # Try to reconnect
            connected = await rabbitmq_service.connect()
            if not connected:
                logger.error("Failed to reconnect to RabbitMQ")
                return False
            
            # Force consumer recovery
            await rabbitmq_service.force_consumer_recovery()
            
            # Verify health
            is_healthy = await rabbitmq_service.is_healthy()
            
            if is_healthy:
                logger.info("RabbitMQ recovery completed successfully")
                return True
            else:
                logger.error("RabbitMQ recovery failed - still unhealthy")
                return False
                
        except Exception as e:
            logger.error(f"Error during RabbitMQ recovery: {str(e)}")
            return False


# Global RabbitMQ manager instance
rabbitmq_manager = RabbitMQManager()
