"""
Loop Detection Service for Rule-Based Chatbots

This service provides comprehensive loop detection algorithms to prevent infinite loops
in rule-based chatbot flows during creation, updates, and runtime execution.
"""

import logging
from typing import List, Dict, Set, Optional, Tuple, Any
from sqlalchemy.orm import Session
from app.models import ChatbotNode, ChatbotEdge

logger = logging.getLogger(__name__)


class LoopDetectionService:
    """
    Service for detecting loops in rule-based chatbot flows.
    
    Provides multiple detection algorithms:
    1. Simple cycle detection (DFS-based)
    2. Strongly connected components detection
    3. Runtime loop detection with visited tracking
    4. Chain length validation
    """
    
    def __init__(self):
        self.max_chain_length = 10
        self.max_nodes_in_loop = 5
        # Node types that require user input (break infinite loops)
        self.user_input_node_types = {"question", "buttons", "list"}
        # Node types that auto-advance (can cause infinite loops)
        self.auto_advance_node_types = {"sendMessage"}
    
    def detect_loops_in_chatbot(
        self, 
        db: Session, 
        chatbot_id: str, 
        tenant_id: int
    ) -> Dict[str, Any]:
        """
        Comprehensive loop detection for a chatbot flow.
        
        Args:
            db: Database session
            chatbot_id: Chatbot ID
            tenant_id: Tenant ID
            
        Returns:
            Dict containing loop detection results
        """
        try:
            logger.info(f"🔍 LOOP DETECTION - Starting comprehensive analysis for chatbot {chatbot_id}")
            
            # Get all nodes and edges for the chatbot
            nodes = db.query(ChatbotNode).filter(
                ChatbotNode.chatbot_id == chatbot_id,
                ChatbotNode.tenant_id == tenant_id
            ).all()
            
            edges = db.query(ChatbotEdge).filter(
                ChatbotEdge.chatbot_id == chatbot_id,
                ChatbotEdge.tenant_id == tenant_id
            ).all()
            
            if not nodes:
                logger.warning(f"🔍 LOOP DETECTION - No nodes found for chatbot {chatbot_id}")
                return {
                    "has_loops": False,
                    "loops": [],
                    "warnings": [],
                    "recommendations": []
                }
            
            # Build adjacency list
            adjacency_list = self._build_adjacency_list(nodes, edges)
            
            # Run different detection algorithms
            simple_cycles = self._detect_simple_cycles(adjacency_list, nodes)
            scc_loops = self._detect_strongly_connected_components(adjacency_list, nodes)
            chain_issues = self._detect_long_chains(adjacency_list, nodes)
            
            # Combine results - only actual cycles are considered blocking errors
            all_loops = simple_cycles + scc_loops
            has_loops = len(all_loops) > 0  # Long chains are warnings, not blocking errors
            
            result = {
                "has_loops": has_loops,
                "loops": all_loops,
                "chain_issues": chain_issues,
                "warnings": self._generate_warnings(all_loops, chain_issues),
                "recommendations": self._generate_recommendations(all_loops, chain_issues),
                "statistics": {
                    "total_nodes": len(nodes),
                    "total_edges": len(edges),
                    "cycles_found": len(simple_cycles),
                    "scc_loops_found": len(scc_loops),
                    "chain_issues_found": len(chain_issues)
                }
            }
            
            logger.info(f"🔍 LOOP DETECTION - Analysis complete for chatbot {chatbot_id}")
            logger.info(f"🔍 LOOP DETECTION - Results: {result['statistics']}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in loop detection for chatbot {chatbot_id}: {str(e)}")
            return {
                "has_loops": False,
                "loops": [],
                "warnings": [f"Loop detection failed: {str(e)}"],
                "recommendations": ["Please check the chatbot configuration manually"],
                "error": str(e)
            }
    
    def _is_infinite_loop(self, loop_nodes: List[str], node_type_map: Dict[str, str]) -> bool:
        """
        Check if a loop is infinite (all nodes auto-advance).
        
        A loop is infinite if ALL nodes in it are auto-advancing types (sendMessage, condition, assignment).
        A loop is safe if it contains at least one user input node (question, buttons, list).
        
        Args:
            loop_nodes: List of node IDs in the loop
            node_type_map: Mapping of node_id to node type
            
        Returns:
            bool: True if loop is infinite (dangerous), False if safe (has user input)
        """
        has_user_input_node = False
        
        for node_id in loop_nodes:
            node_type = node_type_map.get(node_id, "").lower()
            if node_type in self.user_input_node_types:
                has_user_input_node = True
                break
        
        # If at least one node requires user input, the loop is NOT infinite
        return not has_user_input_node
    
    def _build_adjacency_list(
        self, 
        nodes: List[ChatbotNode], 
        edges: List[ChatbotEdge]
    ) -> Dict[str, List[str]]:
        """Build adjacency list from nodes and edges."""
        adjacency_list = {}
        
        # Initialize all nodes
        for node in nodes:
            adjacency_list[node.node_id] = []
        
        # Add edges
        for edge in edges:
            if edge.source_node in adjacency_list:
                adjacency_list[edge.source_node].append(edge.target_node)
        
        return adjacency_list
    
    def _detect_simple_cycles(
        self, 
        adjacency_list: Dict[str, List[str]], 
        nodes: List[ChatbotNode]
    ) -> List[Dict[str, Any]]:
        """
        Detect simple cycles using DFS.
        
        Only returns infinite loops (cycles with only sendMessage nodes).
        Cycles containing question/buttons/list nodes are safe (user input required).
        
        Returns list of infinite loop cycles found.
        """
        cycles = []
        visited = set()
        rec_stack = set()
        
        # Create node type lookup for efficiency
        node_type_map = {node.node_id: node.type for node in nodes}
        
        def dfs(node_id: str, path: List[str]) -> bool:
            if node_id in rec_stack:
                # Found a cycle
                try:
                    cycle_start = path.index(node_id)
                except ValueError:
                    # Node is in rec_stack but not in path - this shouldn't happen
                    # but if it does, we'll just use the current path
                    logger.warning(f"Node {node_id} is in recursion stack but not in path {path}. Using full path for cycle detection.")
                    cycle_start = 0
                cycle = path[cycle_start:] + [node_id]
                cycle_nodes = cycle[:-1]  # Remove duplicate end node
                
                # Check if this is an infinite loop (only sendMessage nodes)
                is_infinite = self._is_infinite_loop(cycle_nodes, node_type_map)
                
                if is_infinite:
                    cycles.append({
                        "type": "simple_cycle",
                        "cycle": cycle,
                        "length": len(cycle) - 1,
                        "nodes": cycle_nodes,
                        "is_infinite": True
                    })
                else:
                    logger.info(f"🔄 SAFE LOOP DETECTED - Cycle contains user input nodes: {' → '.join(cycle_nodes)}")
                
                return True
            
            if node_id in visited:
                return False
            
            visited.add(node_id)
            rec_stack.add(node_id)
            path.append(node_id)
            
            for neighbor in adjacency_list.get(node_id, []):
                dfs(neighbor, path.copy())  # Don't return early, continue checking all paths
            
            rec_stack.remove(node_id)
            return False
        
        for node in nodes:
            if node.node_id not in visited:
                dfs(node.node_id, [])
        
        return cycles
    
    def _detect_strongly_connected_components(
        self, 
        adjacency_list: Dict[str, List[str]], 
        nodes: List[ChatbotNode]
    ) -> List[Dict[str, Any]]:
        """
        Detect strongly connected components (SCCs) using Tarjan's algorithm.
        
        Only returns infinite loops (SCCs with only sendMessage nodes).
        SCCs containing question/buttons/list nodes are safe (user input required).
        """
        scc_loops = []
        index = 0
        stack = []
        indices = {}
        lowlinks = {}
        on_stack = set()
        
        # Create node type lookup for efficiency
        node_type_map = {node.node_id: node.type for node in nodes}
        
        def strongconnect(node_id: str):
            nonlocal index
            
            indices[node_id] = index
            lowlinks[node_id] = index
            index += 1
            stack.append(node_id)
            on_stack.add(node_id)
            
            for neighbor in adjacency_list.get(node_id, []):
                if neighbor not in indices:
                    strongconnect(neighbor)
                    lowlinks[node_id] = min(lowlinks[node_id], lowlinks[neighbor])
                elif neighbor in on_stack:
                    lowlinks[node_id] = min(lowlinks[node_id], indices[neighbor])
            
            if lowlinks[node_id] == indices[node_id]:
                scc = []
                while True:
                    w = stack.pop()
                    on_stack.remove(w)
                    scc.append(w)
                    if w == node_id:
                        break
                
                # If SCC has more than one node, check if it's an infinite loop
                if len(scc) > 1:
                    # Check if this is an infinite loop (only sendMessage nodes)
                    is_infinite = self._is_infinite_loop(scc, node_type_map)
                    
                    if is_infinite:
                        scc_loops.append({
                            "type": "strongly_connected_component",
                            "scc": scc,
                            "length": len(scc),
                            "nodes": scc,
                            "is_infinite": True
                        })
                    else:
                        logger.info(f"🔄 SAFE SCC DETECTED - Component contains user input nodes: {' → '.join(scc)}")
        
        for node in nodes:
            if node.node_id not in indices:
                strongconnect(node.node_id)
        
        return scc_loops
    
    def _detect_long_chains(
        self, 
        adjacency_list: Dict[str, List[str]], 
        nodes: List[ChatbotNode]
    ) -> List[Dict[str, Any]]:
        """
        Detect chains that are too long (potential infinite loops).
        
        Returns list of chains that exceed maximum length.
        """
        chain_issues = []
        
        def find_long_chains(start_node: str, path: List[str], visited: Set[str]) -> None:
            if len(path) > self.max_chain_length:
                chain_issues.append({
                    "type": "long_chain",
                    "chain": path.copy(),
                    "length": len(path),
                    "start_node": start_node,
                    "nodes": path
                })
                return
            
            if start_node in visited:
                return
            
            visited.add(start_node)
            path.append(start_node)
            
            for neighbor in adjacency_list.get(start_node, []):
                find_long_chains(neighbor, path, visited.copy())
            
            path.pop()
        
        for node in nodes:
            find_long_chains(node.node_id, [], set())
        
        return chain_issues
    
    def _generate_warnings(
        self, 
        loops: List[Dict[str, Any]], 
        chain_issues: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate warning messages for detected issues."""
        warnings = []
        
        for loop in loops:
            # Only warn about infinite loops (loops already filtered for infinite ones)
            if loop["type"] == "simple_cycle":
                warnings.append(
                    f"⚠️ INFINITE LOOP: {' → '.join(loop['nodes'])} → {loop['nodes'][0]} "
                    f"(length: {loop['length']}, only auto-advancing nodes)"
                )
            elif loop["type"] == "strongly_connected_component":
                warnings.append(
                    f"⚠️ INFINITE LOOP (SCC): {' → '.join(loop['nodes'])} "
                    f"(length: {loop['length']}, only auto-advancing nodes)"
                )
        
        for chain in chain_issues:
            warnings.append(
                f"Long chain detected: {' → '.join(chain['nodes'])} "
                f"(length: {chain['length']}, max allowed: {self.max_chain_length})"
            )
        
        return warnings
    
    def _generate_recommendations(
        self, 
        loops: List[Dict[str, Any]], 
        chain_issues: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate recommendations for fixing detected issues."""
        recommendations = []
        
        if loops:
            recommendations.append(
                "✅ Break infinite loops by adding user input nodes (question, buttons, or list) to the cycle"
            )
            recommendations.append(
                "✅ Add condition nodes that branch out of the loop"
            )
            recommendations.append(
                "✅ Consider adding 'end conversation' nodes to terminate flows"
            )
            recommendations.append(
                "ℹ️ Note: Loops containing question/buttons/list nodes are safe (user must respond to continue)"
            )
        
        if chain_issues:
            recommendations.append(
                f"Reduce chain lengths by breaking long sequences into smaller chunks"
            )
            recommendations.append(
                "Add intermediate question nodes to break up long sendMessage chains"
            )
        
        if not loops and not chain_issues:
            recommendations.append("✅ No infinite loops detected - chatbot flow is safe")
        
        return recommendations
    
    def validate_chatbot_before_save(
        self, 
        db: Session, 
        chatbot_id: str, 
        tenant_id: int
    ) -> Tuple[bool, List[str]]:
        """
        Validate chatbot for loops before saving.
        
        Returns:
            Tuple of (is_valid, error_messages)
        """
        try:
            result = self.detect_loops_in_chatbot(db, chatbot_id, tenant_id)
            
            if result["has_loops"]:
                error_messages = result["warnings"].copy()
                error_messages.extend([
                    "Chatbot contains loops that could cause infinite execution.",
                    "Please fix the detected issues before saving."
                ])
                return False, error_messages
            
            return True, []
            
        except Exception as e:
            logger.error(f"Error validating chatbot {chatbot_id}: {str(e)}")
            return False, [f"Validation failed: {str(e)}"]
    
    def get_loop_detection_summary(self, result: Dict[str, Any]) -> str:
        """Generate a human-readable summary of loop detection results."""
        if not result["has_loops"]:
            return "✅ No loops detected - chatbot flow is safe"
        
        summary_parts = ["❌ Loops detected in chatbot flow:"]
        
        for loop in result["loops"]:
            if loop["type"] == "simple_cycle":
                summary_parts.append(f"  • Cycle: {' → '.join(loop['nodes'])} → {loop['nodes'][0]}")
            elif loop["type"] == "strongly_connected_component":
                summary_parts.append(f"  • SCC: {' → '.join(loop['nodes'])}")
        
        for chain in result["chain_issues"]:
            summary_parts.append(f"  • Long chain: {' → '.join(chain['nodes'])} (length: {chain['length']})")
        
        return "\n".join(summary_parts)
