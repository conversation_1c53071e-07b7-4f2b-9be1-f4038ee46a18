"""
Workflow Event Listener Service

This service handles incoming workflow trigger events from the ex.workflow exchange
and processes them through the conversation flow.
"""

import json
import logging
import aio_pika
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from aio_pika.abc import AbstractIncomingMessage

from app.database import get_db
from app.services.rabbitmq_service import rabbitmq_service
from app.services.conversation_event_publisher import conversation_event_publisher
from app.services.redis_service import RedisService
from app.services.charge_calculator import charge_calculator
from app.services.chatbot_service import ChatbotService
from app.services.jwt_service import jwt_service
from app.models import ChatbotConversation, Chatbot
from app.utils.conversation_state_utils import (
    clean_conversation_state,
    update_conversation_in_db,
    store_conversation_turn,
    track_credit_usage,
    sanitize_state_for_db
)

logger = logging.getLogger(__name__)


class WorkflowEventListener:
    """
    Listener for incoming workflow trigger events in the WhatsApp chatbot system
    """
    
    def __init__(self):
        self.queue_name = "q.workflow.trigger.chatbot.chatbot"
        self.routing_key = "workflow.trigger.chatbot"
        self.exchange_name = "ex.workflow"
        self.is_running = False
        self.conversation_event_publisher = conversation_event_publisher
        self.redis_service = RedisService()
        self.chatbot_service = ChatbotService()
        self.charge_calculator = charge_calculator
    
    async def start(self):
        """
        Start the workflow event listener
        """
        if self.is_running:
            logger.warning("Workflow event listener is already running")
            return
        
        try:
            logger.info("Starting workflow event listener...")
            
            # Connect to RabbitMQ
            connected = await rabbitmq_service.connect()
            if not connected:
                raise RuntimeError("Failed to connect to RabbitMQ")
            
            # Setup the workflow listener
            await rabbitmq_service.setup_workflow_listener()

            # Setup publishers for sending conversation responses
            await rabbitmq_service.setup_whatsapp_chatbot_publisher()

            # Register our event handler with the RabbitMQ service
            rabbitmq_service.register_event_handler(
                self.routing_key,
                self.handle_workflow_trigger_event
            )

            # Start consuming messages
            await rabbitmq_service.start_consuming(self.queue_name)

            self.is_running = True
            logger.info("Workflow event listener started successfully")

        except Exception as e:
            logger.error(f"Failed to start workflow event listener: {str(e)}")
            self.is_running = False
            raise
    
    async def stop(self):
        """
        Stop the workflow event listener
        """
        if not self.is_running:
            logger.warning("Workflow event listener is not running")
            return
        
        try:
            logger.info("Stopping workflow event listener...")
            
            # Stop consuming messages
            await rabbitmq_service.stop_consuming(self.queue_name)
            
            self.is_running = False
            logger.info("Workflow event listener stopped successfully")

        except Exception as e:
            logger.error(f"Failed to stop workflow event listener: {str(e)}")
            raise
    
    async def handle_workflow_trigger_event(self, payload: Dict[str, Any], message: AbstractIncomingMessage):
        """
        Handle incoming workflow trigger events
        
        Args:
            payload: Event payload containing workflow trigger data
            message: RabbitMQ message object
        """
        # Extract reply headers for response publishing
        reply_to_exchange = message.headers.get("replyToExchange")
        reply_to_event = message.headers.get("replyToEvent")
        logger.info(f"Reply to exchange: {reply_to_exchange}, Reply to event: {reply_to_event}")

        try:
            start_time = datetime.now(timezone.utc)
            logger.info(f"🎯 HANDLING WORKFLOW TRIGGER EVENT - Started at: {start_time.isoformat()}")
            logger.info(f"🎯 EVENT HANDLER - Routing Key: {message.routing_key}")
            logger.info(f"🎯 EVENT HANDLER - Exchange: {message.exchange}")

            # Log payload details
            logger.info(f"🎯 EVENT PAYLOAD - Full Payload: {payload}")

            # Extract workflow data
            metadata = payload.get("metadata", {})
            message_conversation_id = payload.get("messageConversationId")
            message_conversation_status = payload.get("messageConversationStatus")
            connected_account = payload.get("connectedAccount", {})
            chatbot_data = payload.get("chatbot", {})
            entity_details = payload.get("entityDetails", [])
            event_id = metadata.get("eventId")

            logger.info(f"🎯 EXTRACTED DATA - Message Conversation ID: {message_conversation_id}")
            logger.info(f"🎯 EXTRACTED DATA - Message Conversation Status: {message_conversation_status}")
            logger.info(f"🎯 EXTRACTED DATA - Chatbot ID: {chatbot_data.get('id')}")
            logger.info(f"🎯 EXTRACTED DATA - Tenant ID: {metadata.get('tenantId')}")

            # Filter events based on messageConversationStatus
            
            logger.info(f"✅ EVENT ACCEPTED - messageConversationStatus: {message_conversation_status or 'Not provided (allowed)'}")

            # Validate required fields
            if not message_conversation_id:
                logger.error("❌ WORKFLOW TRIGGER - Missing messageConversationId")
                if reply_to_exchange and reply_to_event:
                    from app.services.rabbitmq_service import RabbitMQService
                    rabbitmq_service = RabbitMQService()
                    channel = await rabbitmq_service.get_channel()
                    if channel:
                        await self._publish_workflow_response(
                            status="FAILED",
                            status_code=400,
                            entity_id=None,
                            channel=channel,
                            reply_to_exchange=reply_to_exchange,
                            reply_to_event=reply_to_event,
                            event_id=event_id,
                            error_code="041081",
                            error_message="Missing messageConversationId"
                        )
                return

            if not chatbot_data.get('id'):
                logger.error("❌ WORKFLOW TRIGGER - Missing chatbot ID")
                if reply_to_exchange and reply_to_event:
                    from app.services.rabbitmq_service import RabbitMQService
                    rabbitmq_service = RabbitMQService()
                    channel = await rabbitmq_service.get_channel()
                    if channel:
                        await self._publish_workflow_response(
                            status="FAILED",
                            status_code=400,
                            entity_id=message_conversation_id,
                            channel=channel,
                            reply_to_exchange=reply_to_exchange,
                            reply_to_event=reply_to_event,
                            event_id=event_id,
                            error_code="041081",
                            error_message="Missing chatbot ID"
                        )
                return

            tenant_id = metadata.get('tenantId')
            if not tenant_id:
                logger.error("❌ WORKFLOW TRIGGER - Missing tenantId")
                if reply_to_exchange and reply_to_event:
                    from app.services.rabbitmq_service import RabbitMQService
                    rabbitmq_service = RabbitMQService()
                    channel = await rabbitmq_service.get_channel()
                    if channel:
                        await self._publish_workflow_response(
                            status="FAILED",
                            status_code=400,
                            entity_id=message_conversation_id,
                            channel=channel,
                            reply_to_exchange=reply_to_exchange,
                            reply_to_event=reply_to_event,
                            event_id=event_id,
                            error_code="041081",
                            error_message="Missing tenantId"
                        )
                return

            # Get database session
            db = next(get_db())
            
            # Acquire distributed lock to prevent race conditions across multiple pods
            lock_key = f"conversation_check:{message_conversation_id}:{tenant_id}"
            logger.info(f"🔒 ATTEMPTING TO ACQUIRE DISTRIBUTED LOCK - Lock Key: {lock_key}")
            logger.info(f"🔒 RACE CONDITION PREVENTION - Ensuring only one pod processes this trigger")
            
            lock_acquired = self.redis_service.acquire_lock(lock_key, timeout=30)
            
            if not lock_acquired:
                # Another pod is already processing this trigger
                logger.warning(f"❌ FAILED TO ACQUIRE LOCK - Another pod is processing this trigger")
                logger.warning(f"❌ RACE CONDITION DETECTED - Message Conversation ID: {message_conversation_id}")
                logger.info(f"⏳ WAITING FOR OTHER POD - Will check for existing conversation after delay")
                
                # Wait briefly and check if conversation was created by the other pod
                import asyncio
                await asyncio.sleep(3)  # Wait 3 seconds for other pod to create conversation
                
                # Check if conversation was created by the other pod
                logger.info(f"🔍 CHECKING AFTER LOCK FAILURE - Looking for conversation created by other pod")
                existing_conversation = await self._check_existing_conversation(message_conversation_id, tenant_id, db)
                
                if existing_conversation:
                    logger.info(f"✅ CONVERSATION FOUND AFTER LOCK FAILURE - Conversation ID: {existing_conversation.id}")
                    logger.info(f"✅ OTHER POD CREATED CONVERSATION - Rejecting duplicate trigger")
                    logger.info(f"📤 PUBLISHING 'ANOTHER CHATBOT IN PROGRESS' ERROR TO WORKFLOW")
                    
                    # Determine if it's the same chatbot or different chatbot
                    is_same_chatbot = (existing_conversation.chatbot_id == chatbot_data.get('id'))
                    
                    if is_same_chatbot:
                        error_msg = f"Conversation already in progress for messageConversationId {message_conversation_id}"
                        logger.info(f"📋 SAME CHATBOT DETECTED - Chatbot ID: {existing_conversation.chatbot_id}")
                    else:
                        error_msg = f"Another chatbot is already in progress for this conversation. Please wait for the current chatbot to complete."
                        logger.info(f"📋 DIFFERENT CHATBOT DETECTED - Existing: {existing_conversation.chatbot_id}, Requested: {chatbot_data.get('id')}")
                    
                    # Publish error response
                    if reply_to_exchange and reply_to_event:
                        from app.services.rabbitmq_service import RabbitMQService
                        rabbitmq_service = RabbitMQService()
                        channel = await rabbitmq_service.get_channel()
                        if channel:
                            await self._publish_workflow_response(
                                status="FAILED",
                                status_code=409,
                                entity_id=message_conversation_id,
                                channel=channel,
                                reply_to_exchange=reply_to_exchange,
                                reply_to_event=reply_to_event,
                                event_id=event_id,
                                error_code="041082",
                                error_message=error_msg,
                                execution_details={
                                    "existingConversationId": existing_conversation.id,
                                    "existingChatbotId": existing_conversation.chatbot_id,
                                    "requestedChatbotId": chatbot_data.get('id'),
                                    "messageConversationId": message_conversation_id,
                                    "action": "rejected_conversation_in_progress",
                                    "reason": "lock_conflict_conversation_exists",
                                    "isSameChatbot": is_same_chatbot
                                }
                            )
                            logger.info(f"✅ PUBLISHED ERROR - Message: {error_msg}")
                    return
                else:
                    # No conversation found even after waiting - retry lock acquisition once more
                    logger.warning(f"⚠️ NO CONVERSATION FOUND AFTER LOCK FAILURE - Retrying lock acquisition")
                    logger.info(f"🔄 RETRY ATTEMPT - Trying to acquire lock one more time")
                    
                    await asyncio.sleep(1)  # Brief pause before retry
                    lock_acquired = self.redis_service.acquire_lock(lock_key, timeout=30)
                    
                    if not lock_acquired:
                        # Still can't acquire lock - check one final time for conversation
                        logger.error(f"❌ RETRY FAILED - Still cannot acquire lock")
                        logger.info(f"🔍 FINAL CHECK - Looking for conversation one last time")
                        
                        existing_conversation = await self._check_existing_conversation(message_conversation_id, tenant_id, db)
                        if existing_conversation:
                            logger.info(f"✅ CONVERSATION FOUND ON FINAL CHECK - ID: {existing_conversation.id}")
                            
                            # Determine error message
                            is_same_chatbot = (existing_conversation.chatbot_id == chatbot_data.get('id'))
                            if is_same_chatbot:
                                error_msg = f"Conversation already in progress for messageConversationId {message_conversation_id}"
                            else:
                                error_msg = f"Another chatbot is already in progress for this conversation. Please wait for the current chatbot to complete."
                            
                            # Publish error response
                            if reply_to_exchange and reply_to_event:
                                from app.services.rabbitmq_service import RabbitMQService
                                rabbitmq_service = RabbitMQService()
                                channel = await rabbitmq_service.get_channel()
                                if channel:
                                    await self._publish_workflow_response(
                                        status="FAILED",
                                        status_code=409,
                                        entity_id=message_conversation_id,
                                        channel=channel,
                                        reply_to_exchange=reply_to_exchange,
                                        reply_to_event=reply_to_event,
                                        event_id=event_id,
                                        error_code="041082",
                                        error_message=error_msg,
                                        execution_details={
                                            "existingConversationId": existing_conversation.id,
                                            "existingChatbotId": existing_conversation.chatbot_id,
                                            "requestedChatbotId": chatbot_data.get('id'),
                                            "messageConversationId": message_conversation_id,
                                            "action": "rejected_conversation_in_progress",
                                            "reason": "lock_retry_failed_conversation_exists",
                                            "isSameChatbot": is_same_chatbot
                                        }
                                    )
                            return
                        else:
                            # Very rare case - no lock and no conversation
                            # This could happen if other pod is still creating the conversation
                            logger.error(f"❌ CRITICAL - Cannot acquire lock and no conversation exists yet")
                            logger.error(f"❌ This could be Redis/DB sync delay or lock contention")
                            logger.info(f"⏳ FINAL WAIT - Giving one last chance for conversation creation")
                            
                            # Wait one more time and check
                            await asyncio.sleep(2)
                            existing_conversation = await self._check_existing_conversation(message_conversation_id, tenant_id, db)
                            
                            if existing_conversation:
                                # Conversation created during final wait
                                logger.info(f"✅ CONVERSATION FOUND ON FINAL WAIT - ID: {existing_conversation.id}")
                                
                                is_same_chatbot = (existing_conversation.chatbot_id == chatbot_data.get('id'))
                                if is_same_chatbot:
                                    error_msg = f"Conversation already in progress for messageConversationId {message_conversation_id}"
                                else:
                                    error_msg = f"Another chatbot is already in progress for this conversation. Please wait for the current chatbot to complete."
                                
                                if reply_to_exchange and reply_to_event:
                                    from app.services.rabbitmq_service import RabbitMQService
                                    rabbitmq_service = RabbitMQService()
                                    channel = await rabbitmq_service.get_channel()
                                    if channel:
                                        await self._publish_workflow_response(
                                            status="FAILED",
                                            status_code=409,
                                            entity_id=message_conversation_id,
                                            channel=channel,
                                            reply_to_exchange=reply_to_exchange,
                                            reply_to_event=reply_to_event,
                                            event_id=event_id,
                                            error_code="041082",
                                            error_message=error_msg,
                                            execution_details={
                                                "existingConversationId": existing_conversation.id,
                                                "existingChatbotId": existing_conversation.chatbot_id,
                                                "requestedChatbotId": chatbot_data.get('id'),
                                                "messageConversationId": message_conversation_id,
                                                "action": "rejected_conversation_in_progress",
                                                "reason": "final_wait_conversation_found",
                                                "isSameChatbot": is_same_chatbot
                                            }
                                        )
                                        logger.info(f"✅ PUBLISHED ERROR AFTER FINAL WAIT - Message: {error_msg}")
                                return
                            else:
                                # Still no conversation - this is truly exceptional
                                # Publish a service unavailable error to workflow
                                logger.error(f"❌ EXCEPTIONAL CASE - No lock, no conversation after all retries")
                                logger.info(f"📤 PUBLISHING SERVICE UNAVAILABLE ERROR TO WORKFLOW")
                                
                                if reply_to_exchange and reply_to_event:
                                    from app.services.rabbitmq_service import RabbitMQService
                                    rabbitmq_service = RabbitMQService()
                                    channel = await rabbitmq_service.get_channel()
                                    if channel:
                                        await self._publish_workflow_response(
                                            status="FAILED",
                                            status_code=503,  # Service Unavailable
                                            entity_id=message_conversation_id,
                                            channel=channel,
                                            reply_to_exchange=reply_to_exchange,
                                            reply_to_event=reply_to_event,
                                            event_id=event_id,
                                            error_code="041084",
                                            error_message=f"Service temporarily unavailable. Unable to process chatbot trigger due to system contention. Please retry.",
                                            execution_details={
                                                "messageConversationId": message_conversation_id,
                                                "requestedChatbotId": chatbot_data.get('id'),
                                                "action": "service_unavailable",
                                                "reason": "lock_contention_no_conversation"
                                            }
                                        )
                                        logger.info(f"✅ PUBLISHED SERVICE UNAVAILABLE ERROR")
                                return
                    else:
                        logger.info(f"✅ RETRY SUCCESS - Lock acquired on second attempt")
                        logger.info(f"✅ PROCEEDING WITH NORMAL FLOW")
            
            logger.info(f"✅ LOCK ACQUIRED SUCCESSFULLY - Proceeding with conversation check")
            logger.info(f"✅ DISTRIBUTED LOCK HELD - This pod has exclusive access to process trigger")
            
            try:
                # Check if conversation is already in progress
                logger.info(f"🔍 CHECKING FOR EXISTING CONVERSATION - Message Conversation ID: {message_conversation_id}, Tenant ID: {tenant_id}")
                logger.info(f"🔍 DUPLICATE PREVENTION CHECK - Starting validation to prevent multiple active chatbots")
                existing_conversation = await self._check_existing_conversation(message_conversation_id, tenant_id, db)
                
                if existing_conversation:
                    # Reject the workflow trigger - conversation is already in progress
                    logger.warning(f"❌ CONVERSATION ALREADY IN PROGRESS - Message Conversation ID: {message_conversation_id}, Existing Conversation ID: {existing_conversation.id}")
                    logger.warning(f"❌ CHATBOT TRIGGER REJECTED - Preventing duplicate chatbot trigger")
                    logger.info(f"📊 EXISTING CONVERSATION DETAILS:")
                    logger.info(f"   - Conversation ID: {existing_conversation.id}")
                    logger.info(f"   - Existing Chatbot ID: {existing_conversation.chatbot_id}")
                    logger.info(f"   - Requested Chatbot ID: {chatbot_data.get('id')}")
                    logger.info(f"   - Tenant ID: {existing_conversation.tenant_id}")
                    logger.info(f"   - Completed: {existing_conversation.completed}")
                    logger.info(f"   - Created At: {existing_conversation.created_at}")
                    
                    # Determine if it's the same chatbot or different chatbot
                    is_same_chatbot = (existing_conversation.chatbot_id == chatbot_data.get('id'))
                    
                    if is_same_chatbot:
                        error_msg = f"Conversation already in progress for messageConversationId {message_conversation_id}"
                        logger.info(f"📋 SAME CHATBOT DETECTED - Duplicate trigger for same chatbot: {existing_conversation.chatbot_id}")
                    else:
                        error_msg = f"Another chatbot is already in progress for this conversation. Please wait for the current chatbot to complete."
                        logger.info(f"📋 DIFFERENT CHATBOT DETECTED - Existing: {existing_conversation.chatbot_id}, Requested: {chatbot_data.get('id')}")
                    
                    # Publish error response with appropriate message
                    if reply_to_exchange and reply_to_event:
                        logger.info(f"📤 PUBLISHING CONVERSATION IN PROGRESS ERROR TO WORKFLOW")
                        logger.info(f"📤 ERROR RESPONSE DETAILS:")
                        logger.info(f"   - Status: FAILED")
                        logger.info(f"   - Status Code: 409 (Conflict)")
                        logger.info(f"   - Error Code: 041082")
                        logger.info(f"   - Message: {error_msg}")
                        logger.info(f"   - Existing Chatbot ID: {existing_conversation.chatbot_id}")
                        logger.info(f"   - Requested Chatbot ID: {chatbot_data.get('id')}")
                        logger.info(f"   - Same Chatbot: {is_same_chatbot}")
                        logger.info(f"   - Reply Exchange: {reply_to_exchange}")
                        logger.info(f"   - Reply Event: {reply_to_event}")
                        
                        from app.services.rabbitmq_service import RabbitMQService
                        rabbitmq_service = RabbitMQService()
                        channel = await rabbitmq_service.get_channel()
                        if channel:
                            logger.info(f"✅ RABBITMQ CHANNEL OBTAINED - Publishing error response")
                            await self._publish_workflow_response(
                                status="FAILED",
                                status_code=409,  # Conflict - resource already exists
                                entity_id=message_conversation_id,
                                channel=channel,
                                reply_to_exchange=reply_to_exchange,
                                reply_to_event=reply_to_event,
                                event_id=event_id,
                                error_code="041082",
                                error_message=error_msg,
                                execution_details={
                                    "existingConversationId": existing_conversation.id,
                                    "existingChatbotId": existing_conversation.chatbot_id,
                                    "requestedChatbotId": chatbot_data.get('id'),
                                    "messageConversationId": message_conversation_id,
                                    "action": "rejected_conversation_in_progress",
                                    "reason": "duplicate_trigger_blocked",
                                    "isSameChatbot": is_same_chatbot
                                }
                            )
                            logger.info(f"✅ PUBLISHED ERROR - Message: {error_msg}")
                            logger.info(f"✅ WORKFLOW NOTIFIED - Conversation in progress, trigger rejected")
                        else:
                            logger.error(f"❌ FAILED TO GET RABBITMQ CHANNEL - Could not publish error response")
                    else:
                        logger.warning(f"⚠️ NO REPLY HEADERS - Cannot publish error response to workflow")
                        logger.warning(f"⚠️ REPLY EXCHANGE: {reply_to_exchange}, REPLY EVENT: {reply_to_event}")
                    
                    logger.info(f"⛔ WORKFLOW TRIGGER BLOCKED - Exiting without starting new chatbot")
                    # Release lock before returning
                    logger.info(f"🔓 RELEASING DISTRIBUTED LOCK - Lock Key: {lock_key}")
                    self.redis_service.release_lock(lock_key)
                    logger.info(f"✅ LOCK RELEASED - Other pods can now process")
                    return
                else:
                    # Process new workflow trigger
                    logger.info(f"✅ NO EXISTING CONVERSATION FOUND - Proceeding with new chatbot trigger")
                    logger.info(f"🔄 PROCESSING NEW WORKFLOW TRIGGER - Message Conversation ID: {message_conversation_id}")
                    logger.info(f"🆕 CREATING NEW CHATBOT CONVERSATION - No conflicts detected")
                    conversation_id = await self._process_workflow_trigger(
                        message_conversation_id=message_conversation_id,
                        chatbot_id=chatbot_data.get('id'),
                        tenant_id=tenant_id,
                        user_id=metadata.get('userId'),
                        connected_account=connected_account,
                        entity_details=entity_details,
                        metadata=metadata,
                        reply_headers={
                            "replyToExchange": reply_to_exchange,
                            "replyToEvent": reply_to_event,
                            "eventId": event_id
                        }
                    )
                    
                    # Release lock after conversation is created
                    logger.info(f"🔓 RELEASING DISTRIBUTED LOCK - Conversation created successfully")
                    logger.info(f"🔓 RELEASING LOCK - Lock Key: {lock_key}")
                    self.redis_service.release_lock(lock_key)
                    logger.info(f"✅ LOCK RELEASED - Conversation {conversation_id} created and lock freed")

                end_time = datetime.now(timezone.utc)
                processing_time = (end_time - start_time).total_seconds()
                logger.info(f"✅ WORKFLOW TRIGGER EVENT PROCESSED SUCCESSFULLY - New conversation started, SUCCESS event published immediately")
                logger.info(f"✅ PROCESSING COMPLETED - Started: {start_time.isoformat()}, Ended: {end_time.isoformat()}, Duration: {processing_time:.3f}s")
            
            except Exception as inner_error:
                # Release lock on any error during conversation check/creation
                logger.error(f"❌ ERROR DURING CONVERSATION PROCESSING: {str(inner_error)}")
                logger.info(f"🔓 RELEASING LOCK DUE TO ERROR - Lock Key: {lock_key}")
                self.redis_service.release_lock(lock_key)
                logger.info(f"✅ LOCK RELEASED AFTER ERROR")
                raise
            
            finally:
                # Ensure lock is always released (safety net)
                try:
                    # Check if lock still exists before releasing
                    if self.redis_service.redis_client.exists(lock_key):
                        logger.info(f"🔓 FINALLY BLOCK - Ensuring lock is released: {lock_key}")
                        self.redis_service.release_lock(lock_key)
                except Exception as release_error:
                    logger.error(f"❌ ERROR RELEASING LOCK IN FINALLY BLOCK: {str(release_error)}")

        except Exception as e:
            logger.error(f"❌ WORKFLOW TRIGGER EVENT PROCESSING FAILED: {str(e)}")
            if reply_to_exchange and reply_to_event:
                from app.services.rabbitmq_service import RabbitMQService
                rabbitmq_service = RabbitMQService()
                channel = await rabbitmq_service.get_channel()
                if channel:
                    await self._publish_workflow_response(
                        status="FAILED",
                        status_code=500,
                        entity_id=message_conversation_id,
                        channel=channel,
                        reply_to_exchange=reply_to_exchange,
                        reply_to_event=reply_to_event,
                        event_id=event_id,
                        error_code="041200",
                        error_message="Internal server error"
                    )
            # Don't re-raise to avoid message redelivery issues
    async def _check_existing_conversation(self, message_conversation_id: int, tenant_id: int, db: Session):
        """
        Check if there's an existing conversation in progress for the given messageConversationId
        
        Args:
            message_conversation_id: The message conversation ID to check
            tenant_id: The tenant ID for the conversation
            db: Database session
            
        Returns:
            ChatbotConversation or None: The existing conversation if found, None otherwise
        """
        try:
            logger.info(f"🔍 DATABASE QUERY - Checking for active conversations")
            logger.info(f"🔍 QUERY PARAMETERS:")
            logger.info(f"   - Message Conversation ID: {message_conversation_id}")
            logger.info(f"   - Tenant ID: {tenant_id}")
            logger.info(f"   - Filter: completed = False (only active conversations)")
            
            # Check if there's an active conversation for this specific messageConversationId and tenant
            active_conversation = db.query(ChatbotConversation).filter(
                ChatbotConversation.tenant_id == tenant_id,
                ChatbotConversation.message_conversation_id == message_conversation_id,
                ChatbotConversation.completed == False  # Conversation is not completed
            ).first()
            
            if active_conversation:
                logger.info(f"🔴 ACTIVE CONVERSATION FOUND - Duplicate trigger detected")
                logger.info(f"🔴 EXISTING CONVERSATION DETAILS:")
                logger.info(f"   - Conversation ID: {active_conversation.id}")
                logger.info(f"   - Chatbot ID: {active_conversation.chatbot_id}")
                logger.info(f"   - Message Conversation ID: {message_conversation_id}")
                logger.info(f"   - Tenant ID: {tenant_id}")
                logger.info(f"   - Completed: {active_conversation.completed}")
                logger.info(f"   - User ID: {active_conversation.user_id}")
                logger.info(f"   - Created At: {active_conversation.created_at}")
                logger.info(f"✅ DUPLICATE CHECK RESULT - Found existing active conversation, will reject new trigger")
                return active_conversation
            else:
                logger.info(f"🟢 NO ACTIVE CONVERSATION FOUND - Safe to proceed")
                logger.info(f"🟢 DUPLICATE CHECK RESULT:")
                logger.info(f"   - Message Conversation ID: {message_conversation_id}")
                logger.info(f"   - Tenant ID: {tenant_id}")
                logger.info(f"   - Status: No conflicts detected")
                logger.info(f"✅ VALIDATION PASSED - New chatbot trigger allowed")
                return None
            
        except Exception as e:
            logger.error(f"❌ CONVERSATION CHECK FAILED - Database query error")
            logger.error(f"❌ ERROR DETAILS:")
            logger.error(f"   - Message Conversation ID: {message_conversation_id}")
            logger.error(f"   - Tenant ID: {tenant_id}")
            logger.error(f"   - Error: {str(e)}")
            logger.error(f"❌ FALLING BACK - Returning None to allow trigger (fail-open)")
            return None

    async def _continue_existing_conversation(
        self,
        existing_conversation,
        message_conversation_id: int,
        chatbot_id: str,
        tenant_id: int,
        user_id: Optional[int],
        connected_account: Dict[str, Any],
        entity_details: List[Dict[str, Any]],
        metadata: Dict[str, Any],
        reply_headers: Dict[str, Any],
        db: Session
    ) -> str:
        """
        Continue an existing conversation by treating the workflow trigger as a user message
        
        Args:
            existing_conversation: The existing ChatbotConversation object
            message_conversation_id: The message conversation ID
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID
            user_id: The user ID
            connected_account: Connected account details
            entity_details: Entity details for the conversation
            metadata: Additional metadata
            reply_headers: Reply headers for workflow response
            db: Database session
            
        Returns:
            str: The conversation ID
        """
        try:
            conversation_id = existing_conversation.id
            
            # Get conversation state from Redis
            from app.services.redis_service import RedisService
            redis_service = RedisService()
            conversation_state = redis_service.get_conversation_state(conversation_id)
            
            if not conversation_state:
                logger.error(f"❌ CONVERSATION STATE NOT FOUND - Conversation ID: {conversation_id}")
                # Fallback: create new conversation if state is missing
                return await self._process_workflow_trigger(
                    message_conversation_id=message_conversation_id,
                    chatbot_id=chatbot_id,
                    tenant_id=tenant_id,
                    user_id=user_id,
                    connected_account=connected_account,
                    entity_details=entity_details,
                    metadata=metadata,
                    reply_headers=reply_headers
                )
            
            # Update conversation state with workflow reply headers for completion tracking
            conversation_state["workflow_reply_headers"] = reply_headers
            
            # Ensure auth token is available for entity field processing
            # Use user_id from conversation state if available, otherwise fall back to passed user_id
            conversation_user_id = conversation_state.get("user_id") or user_id
            if not conversation_state.get("auth_token") and conversation_user_id and tenant_id:
                try:
                    auth_token, actual_user_id = jwt_service.build_jwt_token_for_analysis(str(conversation_user_id), str(tenant_id))
                    conversation_state["auth_token"] = auth_token
                    conversation_state["jwt_token"] = auth_token
                    logger.info(f"✅ JWT TOKEN ADDED TO EXISTING CONVERSATION - User ID: {actual_user_id}")
                except Exception as e:
                    logger.error(f"❌ JWT TOKEN CREATION FAILED FOR EXISTING CONVERSATION: {str(e)}")
            
            redis_service.store_conversation_state(conversation_id, conversation_state)
            
            # Get the chatbot to determine type
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()
            
            if not chatbot:
                logger.error(f"❌ CHATBOT NOT FOUND - Chatbot ID: {chatbot_id}, Tenant: {tenant_id}")
                return conversation_id
            
            # Check if conversation is already completed to avoid duplicate processing
            if conversation_state.get("completed", False):
                logger.info(f"✅ CONVERSATION ALREADY COMPLETED - Conversation ID: {conversation_id}, skipping duplicate processing")
                
                # Publish workflow success response since conversation is already done
                from app.services.rabbitmq_service import RabbitMQService
                rabbitmq_service = RabbitMQService()
                channel = await rabbitmq_service.get_channel()
                if channel:
                    await self._publish_workflow_response(
                        status="SUCCESS",
                        status_code=200,
                        entity_id=message_conversation_id,
                        channel=channel,
                        reply_to_exchange=reply_headers["replyToExchange"],
                        reply_to_event=reply_headers["replyToEvent"],
                        event_id=reply_headers.get("eventId"),
                        execution_details={
                            "conversationId": conversation_id,
                            "chatbotId": chatbot_id,
                            "action": "conversation_already_completed"
                        }
                    )
                return conversation_id
            
            # For RULE-based chatbots, ensure variable replacement happens without duplicate message publishing
            if chatbot.type == "RULE":
                # Check if we should continue processing
                current_node_id = conversation_state.get("rule_current_node_id")
                if current_node_id:
                    logger.info(f"🔄 CONTINUING RULE-BASED CONVERSATION - Current node: {current_node_id}")
                    
                    # Apply variable substitution to the current node without republishing messages
                    # This ensures that if the conversation state has variables that need updating,
                    # they get processed properly for subsequent interactions
                    try:
                        # Get entity field service for variable substitution
                        from app.services.entity_field_service import EntityFieldService
                        entity_field_service = EntityFieldService()
                        
                        # Get auth token from conversation state or create one
                        auth_token = conversation_state.get("auth_token") or conversation_state.get("jwt_token")
                        conversation_user_id = conversation_state.get("user_id")
                        if not auth_token and conversation_user_id and tenant_id:
                            try:
                                auth_token, _ = jwt_service.build_jwt_token_for_analysis(str(conversation_user_id), str(tenant_id))
                                # Update conversation state with the token
                                conversation_state["auth_token"] = auth_token
                                conversation_state["jwt_token"] = auth_token
                                logger.info(f"✅ AUTH TOKEN REFRESHED FOR VARIABLE REPLACEMENT")
                            except Exception as e:
                                logger.error(f"❌ AUTH TOKEN REFRESH FAILED: {str(e)}")
                        
                        # Update entity details in conversation state if provided
                        if entity_details:
                            conversation_state["entity_details"] = entity_details
                            logger.info(f"✅ ENTITY DETAILS UPDATED - Count: {len(entity_details)}")
                        
                        # Store updated conversation state with potentially new auth token and entity details
                        redis_service.store_conversation_state(conversation_id, conversation_state)
                        logger.info(f"✅ CONVERSATION STATE UPDATED WITH VARIABLE REPLACEMENT DATA")
                        
                    except Exception as e:
                        logger.error(f"❌ VARIABLE REPLACEMENT SETUP FAILED: {str(e)}")
                        # Continue without failing the whole process
                        
                    logger.info(f"✅ RULE-BASED CONVERSATION PREPARED FOR VARIABLE REPLACEMENT - Conversation ID: {conversation_id}")
                else:
                    logger.info(f"✅ RULE-BASED CONVERSATION ALREADY AT END - Conversation ID: {conversation_id}")
            else:
                # For AI chatbots, just log that we're continuing
                logger.info(f"🔄 CONTINUING AI CONVERSATION - Conversation ID: {conversation_id}")
            
            # Publish workflow success response without re-processing the conversation
            from app.services.rabbitmq_service import RabbitMQService
            rabbitmq_service = RabbitMQService()
            channel = await rabbitmq_service.get_channel()
            if not channel:
                logger.error("Failed to get RabbitMQ channel for workflow success event")
                return conversation_id
                
            await self._publish_workflow_response(
                status="SUCCESS",
                status_code=200,
                entity_id=message_conversation_id,
                channel=channel,
                reply_to_exchange=reply_headers["replyToExchange"],
                reply_to_event=reply_headers["replyToEvent"],
                event_id=reply_headers.get("eventId"),
                execution_details={
                    "conversationId": conversation_id,
                    "chatbotId": chatbot_id,
                    "action": "continued_existing_conversation"
                }
            )
            
            logger.info(f"✅ EXISTING CONVERSATION CONTINUED - Conversation ID: {conversation_id}")
            return conversation_id
            
        except Exception as e:
            logger.error(f"❌ FAILED TO CONTINUE EXISTING CONVERSATION: {str(e)}")
            # Fallback: process as new conversation
            return await self._process_workflow_trigger(
                message_conversation_id=message_conversation_id,
                chatbot_id=chatbot_id,
                tenant_id=tenant_id,
                user_id=user_id,
                connected_account=connected_account,
                entity_details=entity_details,
                metadata=metadata,
                reply_headers=reply_headers
            )
        finally:
            if 'db' in locals():
                db.close()
    
    async def _process_workflow_trigger(
        self,
        message_conversation_id: int,
        chatbot_id: str,
        tenant_id: int,
        user_id: Optional[str],
        connected_account: Dict[str, Any],
        entity_details: list,
        metadata: Dict[str, Any],
        reply_headers: Optional[Dict[str, Any]] = None
    ):
        """
        Process the workflow trigger and start a new conversation
        
        Args:
            message_conversation_id: The message conversation ID
            chatbot_id: The chatbot ID to trigger
            tenant_id: The tenant ID
            user_id: The user ID
            connected_account: Connected account information
            entity_details: List of entity details
            metadata: Workflow metadata
        """
        try:
            # Get database session
            db = next(get_db())
            
            # Create JWT token for this workflow processing
            jwt_token = None
            actual_user_id = user_id
            try:
                if user_id and tenant_id:
                    jwt_token, actual_user_id = jwt_service.build_jwt_token_for_analysis(str(user_id), str(tenant_id))
                    logger.info(f"✅ JWT TOKEN CREATED - User ID: {actual_user_id} (type: {type(actual_user_id)})")
                    logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Auth token length: {len(jwt_token) if jwt_token else 0}")
                    logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Entity details count: {len(entity_details) if entity_details else 0}")
                    if entity_details:
                        logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Entity details: {entity_details}")
                else:
                    logger.warning(f"⚠️ JWT TOKEN NOT CREATED - Missing user_id or tenant_id")
                    logger.warning(f"🔧 VARIABLE REPLACEMENT DEBUG - user_id: {user_id}, tenant_id: {tenant_id}")
            except Exception as e:
                logger.error(f"❌ JWT TOKEN CREATION FAILED: {str(e)}")
                # Continue processing without token - don't fail the whole workflow

            # Get the chatbot
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()
            
            if not chatbot:
                logger.error(f"❌ CHATBOT NOT FOUND - ID: {chatbot_id}, Tenant: {tenant_id}")
                # Publish error response to workflow
                await self._publish_workflow_error_response(
                    error_code="CHATBOT_NOT_FOUND",
                    error_message=f"Chatbot with ID {chatbot_id} not found for tenant {tenant_id}",
                    reply_headers=reply_headers
                )
                return
            
            # Check if chatbot is active
            if chatbot.status != "ACTIVE":
                logger.error(f"❌ CHATBOT NOT ACTIVE - ID: {chatbot_id}, Status: {chatbot.status}")
                # Publish error response to workflow
                await self._publish_workflow_error_response(
                    error_code="CHATBOT_NOT_ACTIVE",
                    error_message=f"Chatbot {chatbot.name} (ID: {chatbot_id}) is not active. Current status: {chatbot.status}",
                    reply_headers=reply_headers
                )
                return
            
            logger.info(f"✅ CHATBOT FOUND AND ACTIVE - Name: {chatbot.name}, Type: {chatbot.type}, Status: {chatbot.status}")
            
            # Validate entity configuration before processing
            is_valid, validation_error = self._validate_entity_configuration(chatbot, entity_details)
            if not is_valid:
                logger.error(f"❌ ENTITY VALIDATION FAILED - {validation_error}")
                # Publish error response to workflow
                await self._publish_workflow_error_response(
                    error_code="ENTITY_NOT_CONFIGURED",
                    error_message=validation_error,
                    reply_headers=reply_headers
                )
                return
            
            logger.info(f"✅ ENTITY VALIDATION PASSED - Proceeding with workflow trigger")
            
            # Generate a new conversation ID
            import uuid
            conversation_id = str(uuid.uuid4())
            
            # Create conversation state
            conversation_state = {
                "conversation_id": conversation_id,
                "chatbot_id": chatbot_id,
                "chatbot_name": chatbot.name,  # Add chatbot name to state
                "tenant_id": tenant_id,
                "user_id": actual_user_id,  # Use the actual user ID from IAM (now long integer)
                "original_user_id": user_id,  # Keep the original user ID from the request
                "chatbotType": chatbot.type.upper(),  # Use actual chatbot type from database
                "message_conversation_id": message_conversation_id,
                "connected_account": connected_account,
                "entity_details": entity_details,
                "workflow_metadata": metadata,
                "jwt_token": jwt_token,  # Store the created JWT token
                "auth_token": jwt_token,  # Also store as auth_token for message event listener compatibility
                "current_node": None,
                "conversation_turns": [],
                "current_charge": 0,
                "total_charge": 0,
                "completed": False,
                "started_at": None,
                "last_activity": None,
                "workflow_reply_headers": reply_headers  # Store reply headers for completion event
            }
            
            # Store conversation state in Redis
            self.redis_service.store_conversation_state(conversation_id, conversation_state)
            logger.info(f"✅ CONVERSATION STATE STORED - ID: {conversation_id}")
            
            # Create conversation record in database
            # Sanitize state before storing (remove JWT tokens)
            sanitized_state = sanitize_state_for_db(conversation_state)
            conversation = ChatbotConversation(
                id=conversation_id,
                chatbot_id=chatbot_id,
                tenant_id=tenant_id,
                user_id=str(actual_user_id),  # Convert long integer to string for database storage
                conversation_data=json.dumps(sanitized_state, default=str),
                completed=False,
                entity_details=entity_details,
                connected_account_id=connected_account.get('id'),
                connected_account_name=connected_account.get('name'),
                message_conversation_id=message_conversation_id
            )
            
            db.add(conversation)
            db.commit()
            logger.info(f"✅ CONVERSATION RECORD CREATED - ID: {conversation_id}")
            
            # For RULE-based chatbots, we need to process the first node and handle chaining
            # For AI chatbots, we just send the welcome message
            if chatbot.type == "RULE":
                logger.info(f"🔄 RULE-BASED CHATBOT - Processing first node for workflow trigger")
                await self._process_rule_based_workflow_trigger(
                    chatbot=chatbot,
                    conversation_id=conversation_id,
                    conversation_state=conversation_state,
                    tenant_id=tenant_id,
                    message_conversation_id=message_conversation_id,
                    connected_account=connected_account,
                    entity_details=entity_details,
                    db=db
                )
            else:
                # AI chatbot - need to properly initialize with questions
                logger.info(f"🤖 AI CHATBOT - Initializing conversation with all questions")
                
                # Get ALL chatbot questions
                from app.models import ChatbotQuestion, ChatbotQuestionEntityField
                questions = db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot_id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).order_by(ChatbotQuestion.position).all()
                
                if not questions:
                    logger.error(f"❌ NO QUESTIONS FOUND - Chatbot: {chatbot_id}")
                    return
                
                logger.info(f"📝 FOUND {len(questions)} QUESTIONS FOR CHATBOT")
                
                # Get entity types from entity_details
                entity_types = []
                for entity in entity_details:
                    entity_type = entity.get("entityType") or entity.get("entity")
                    if entity_type:
                        entity_types.append(entity_type.upper())
                
                logger.info(f"🏷️ ENTITY TYPES: {entity_types}")
                
                # Filter questions based on entity types
                relevant_questions = []
                for q in questions:
                    # Get entity fields for this question
                    entity_fields_db = db.query(ChatbotQuestionEntityField).filter(
                        ChatbotQuestionEntityField.question_id == q.id
                    ).all()
                    
                    # Convert to dict format
                    entity_fields = [{
                        "entity_type": ef.entity_type,
                        "field_id": ef.field_id,
                        "name": ef.name,
                        "display_name": ef.display_name,
                        "standard": ef.standard
                    } for ef in entity_fields_db]
                    
                    # Find matching entity fields
                    matching_entity_fields = []
                    for ef in entity_fields:
                        for entity_type in entity_types:
                            if ef["entity_type"].upper() == entity_type:
                                matching_entity_fields.append(ef)
                                break
                    
                    if matching_entity_fields:
                        relevant_question = {
                            "id": q.id,
                            "question_id": q.id,
                            "question": q.question,
                            "display_name": matching_entity_fields[0]["display_name"],
                            "standard": matching_entity_fields[0]["standard"],
                            "entity_fields": matching_entity_fields
                        }
                        relevant_questions.append(relevant_question)
                        logger.info(f"✅ ADDED QUESTION: '{q.question}' with {len(matching_entity_fields)} entity fields")
                
                if not relevant_questions:
                    logger.error(f"❌ NO RELEVANT QUESTIONS FOR ENTITY TYPES: {entity_types}")
                    return
                
                logger.info(f"✅ {len(relevant_questions)} RELEVANT QUESTIONS FOR AI CHATBOT")
                
                # Update conversation state with AI chatbot specific fields
                conversation_state["all_questions"] = relevant_questions
                conversation_state["remaining_questions"] = relevant_questions.copy()
                conversation_state["asked_questions"] = []
                conversation_state["answers"] = []
                conversation_state["history"] = [
                    {"role": "system", "content": f"You are a helpful assistant for {chatbot.name}. Your job is to collect information from users by asking questions one by one."}
                ]
                
                # Store updated state in Redis
                self.redis_service.store_conversation_state(conversation_id, conversation_state)
                logger.info(f"✅ AI CHATBOT STATE INITIALIZED - {len(relevant_questions)} questions ready")
                
                # Get the first question
                first_question = relevant_questions[0]
                
                # Use chatbot's welcome message
                welcome_message = chatbot.welcome_message or f"Welcome to {chatbot.name}!"
                logger.info(f"💬 WELCOME MESSAGE: {welcome_message}")
                
                # Extract first question text
                first_question_text = first_question.get("question", "")
                logger.info(f"💬 FIRST QUESTION: {first_question_text}")

                # Calculate charge for AI chatbots
                charge = self.charge_calculator.calculate_question_charge(
                    first_question, is_predefined=True, is_llm_generated=False
                )
                logger.info(f"💰 CHARGE CALCULATED: {charge} (AI type chatbot)")

                # IMPORTANT: Move first question from remaining_questions to asked_questions
                # This prevents the message_event_listener from asking it again when user responds
                if conversation_state["remaining_questions"]:
                    asked_question = conversation_state["remaining_questions"].pop(0)
                    conversation_state["asked_questions"].append(asked_question)
                    logger.info(f"✅ FIRST QUESTION MARKED AS ASKED - {len(conversation_state['remaining_questions'])} questions remaining")

                # Update conversation state with charge information
                conversation_state["current_charge"] = charge
                conversation_state["total_charge"] = charge
                self.redis_service.store_conversation_state(conversation_id, conversation_state)
                
                # Update database with the properly initialized state
                try:
                    conversation_record = db.query(ChatbotConversation).filter(
                        ChatbotConversation.id == conversation_id
                    ).first()
                    if conversation_record:
                        sanitized_state = sanitize_state_for_db(conversation_state)
                        conversation_record.conversation_data = json.dumps(sanitized_state, default=str)
                        db.commit()
                        logger.info(f"✅ DATABASE UPDATED WITH AI CHATBOT STATE")
                except Exception as e:
                    logger.error(f"❌ FAILED TO UPDATE DATABASE: {str(e)}")
                    # Don't fail the whole flow, continue with Redis state

                # Publish conversation response event with workflow metadata
                # For AI chatbots first message: welcomeMessage contains welcome text, message contains first question
                await self.conversation_event_publisher.publish_conversation_response(
                    chatbot_conversation_id=conversation_id,
                    message=first_question_text,  # First question in message field
                    completed=False,
                    charge=charge,
                    tenant_id=tenant_id,
                    message_conversation_id=message_conversation_id,
                    chatbot_id=chatbot.id,
                    chatbot_name=chatbot.name,
                    welcome_message=welcome_message,  # Welcome message in separate field
                    chatbot_type=chatbot.type.upper(),  # Chatbot type at top level
                    extra={
                        "chatbot": {
                            "id": chatbot.id,
                            "name": chatbot.name,
                            "type": chatbot.type
                        },
                        "connectedAccount": connected_account,
                        "entityDetails": entity_details,
                        "metadata": metadata,  # Include workflow metadata
                        "jwtToken": jwt_token,  # Include the created JWT token
                        "actualUserId": actual_user_id  # Include the actual user ID from IAM (long integer)
                    }
                )
            
            logger.info(f"✅ WORKFLOW TRIGGER PROCESSED - Conversation ID: {conversation_id}")
            
            # Publish SUCCESS event immediately after conversation starts
            logger.info(f"🔍 CHECKING REPLY HEADERS FOR SUCCESS EVENT - Headers: {reply_headers}")
            
            if reply_headers and reply_headers.get("replyToExchange") and reply_headers.get("replyToEvent"):
                logger.info(f"✅ REPLY HEADERS VALID - Exchange: {reply_headers.get('replyToExchange')}, Event: {reply_headers.get('replyToEvent')}")
                
                try:
                    logger.info("🔄 CREATING RABBITMQ SERVICE FOR SUCCESS EVENT")
                    from app.services.rabbitmq_service import RabbitMQService
                    rabbitmq_service = RabbitMQService()
                    
                    logger.info("🔄 GETTING RABBITMQ CHANNEL FOR SUCCESS EVENT")
                    channel = await rabbitmq_service.get_channel()
                    
                    if channel:
                        logger.info("✅ RABBITMQ CHANNEL OBTAINED - Publishing SUCCESS event")
                        logger.info(f"📤 SUCCESS EVENT DETAILS - Entity ID: {message_conversation_id}, Exchange: {reply_headers['replyToExchange']}, Event: {reply_headers['replyToEvent']}")
                        
                        await self._publish_workflow_response(
                            status="SUCCESS",
                            status_code=200,
                            entity_id=message_conversation_id,
                            channel=channel,
                            reply_to_exchange=reply_headers["replyToExchange"],
                            reply_to_event=reply_headers["replyToEvent"],
                            event_id=reply_headers.get("eventId"),
                            execution_details={
                                "conversationId": conversation_id,
                                "chatbotId": chatbot_id,
                                "tenantId": tenant_id,
                                "action": "conversation_started",
                                "startedAt": datetime.now(timezone.utc).isoformat()
                            }
                        )
                        logger.info(f"✅ PUBLISHED WORKFLOW SUCCESS - Conversation started: {conversation_id}")
                    else:
                        logger.error("❌ FAILED TO GET RABBITMQ CHANNEL FOR SUCCESS EVENT")
                except Exception as e:
                    logger.error(f"❌ FAILED TO PUBLISH WORKFLOW SUCCESS: {str(e)}")
                    import traceback
                    logger.error(f"❌ SUCCESS EVENT ERROR TRACEBACK: {traceback.format_exc()}")
                    # Don't fail the whole workflow if SUCCESS publishing fails
            else:
                logger.warning(f"⚠️ INVALID OR MISSING REPLY HEADERS - Cannot publish SUCCESS event")
                logger.warning(f"⚠️ REPLY HEADERS DEBUG - reply_headers: {reply_headers}")
                if reply_headers:
                    logger.warning(f"⚠️ REPLY HEADERS DEBUG - replyToExchange: {reply_headers.get('replyToExchange')}")
                    logger.warning(f"⚠️ REPLY HEADERS DEBUG - replyToEvent: {reply_headers.get('replyToEvent')}")
                else:
                    logger.warning(f"⚠️ REPLY HEADERS DEBUG - reply_headers is None or empty")
            
            # Return conversation_id for success response
            return conversation_id

        except Exception as e:
            logger.error(f"❌ WORKFLOW TRIGGER PROCESSING FAILED: {str(e)}")
            raise
        finally:
            db.close()

        return None

    async def _get_first_question(self, chatbot: Chatbot, db: Session) -> Optional[Dict[str, Any]]:
        """
        Get the first question for the chatbot
        
        Args:
            chatbot: The chatbot object
            db: Database session
            
        Returns:
            Dict containing the first question or None
        """
        try:
            if chatbot.type == "RULE":
                # For rule-based chatbots, get the first node
                from app.models import ChatbotNode
                first_node = db.query(ChatbotNode).filter(
                    ChatbotNode.chatbot_id == chatbot.id,
                    ChatbotNode.is_first_node == True
                ).first()
                
                if first_node and first_node.data:
                    return {
                        "id": first_node.node_id,
                        "question": first_node.data.get("text", ""),
                        "type": "rule_based"
                    }
            else:
                # For AI chatbots, get the first predefined question
                from app.models import ChatbotQuestion
                first_question = db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot.id,
                    ChatbotQuestion.position == 1
                ).first()
                
                if first_question:
                    return {
                        "id": first_question.id,
                        "question": first_question.question,
                        "type": "ai_based"
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ FAILED TO GET FIRST QUESTION: {str(e)}")
            return None

    async def _publish_workflow_response(
        self,
        status: str,
        status_code: int,
        entity_id: Optional[int],
        channel,
        reply_to_exchange: str,
        reply_to_event: str,
        event_id: Optional[int] = None,
        error_code: Optional[str] = None,
        error_message: Optional[str] = None,
        execution_details: Optional[Dict[str, Any]] = None,
    ):
        """
        Publishes a workflow response message to the given exchange and routing key.

        Args:
            status: Status of the workflow processing (SUCCESS/FAILED)
            status_code: HTTP status code
            entity_id: ID of the entity being processed
            channel: RabbitMQ channel
            reply_to_exchange: Exchange to publish response to
            reply_to_event: Routing key for the response
            event_id: Event ID from the original message
            error_code: Error code for failed operations
            error_message: Error message for failed operations
            execution_details: Additional execution details
        """
        try:
            response_obj = {
                "eventId": event_id,
                "status": status,
                "statusCode": status_code,
                "statusUpdatedAt": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                "errorCode": error_code,
                "errorMessage": error_message,
                "entityId": entity_id,
                "executionDetails": execution_details or {},
            }

            # Log the complete response object being published
            logger.info(f"📤 PUBLISHING WORKFLOW RESPONSE - Complete Payload:")
            logger.info(f"📤   - Event ID: {event_id}")
            logger.info(f"📤   - Status: {status}")
            logger.info(f"📤   - Status Code: {status_code}")
            logger.info(f"📤   - Entity ID: {entity_id}")
            logger.info(f"📤   - Error Code: {error_code}")
            logger.info(f"📤   - Error Message: {error_message}")
            logger.info(f"📤   - Execution Details: {execution_details}")
            logger.info(f"📤   - Exchange: {reply_to_exchange}")
            logger.info(f"📤   - Routing Key: {reply_to_event}")
            logger.info(f"📤   - Full JSON: {json.dumps(response_obj, indent=2)}")

            exchange = await channel.declare_exchange(reply_to_exchange, aio_pika.ExchangeType.TOPIC, durable=True)
            message = aio_pika.Message(
                body=json.dumps(response_obj).encode(),
                content_type="application/json",
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT
            )
            
            logger.info(f"📤 MESSAGE CREATED - Size: {len(message.body)} bytes, Content Type: {message.content_type}")
            
            await exchange.publish(message, routing_key=reply_to_event)
            
            logger.info(f"✅ WORKFLOW RESPONSE PUBLISHED SUCCESSFULLY")
            logger.info(f"✅   - Status: {status}")
            logger.info(f"✅   - Entity ID: {entity_id}")
            logger.info(f"✅   - Exchange: {reply_to_exchange}")
            logger.info(f"✅   - Event: {reply_to_event}")
            logger.info(f"✅   - Timestamp: {datetime.now(timezone.utc).isoformat()}")

        except Exception as e:
            logger.error(f"❌ FAILED TO PUBLISH WORKFLOW RESPONSE: {str(e)}")
            import traceback
            logger.error(f"❌ WORKFLOW RESPONSE ERROR TRACEBACK: {traceback.format_exc()}")
            # Don't re-raise to avoid breaking the main workflow

    async def publish_workflow_completion_success(
        self,
        conversation_id: str,
        message_conversation_id: int,
        chatbot_id: str,
        tenant_id: int,
        reply_headers: Dict[str, Any],
        execution_details: Optional[Dict[str, Any]] = None
    ):
        """
        Publish workflow success event when conversation completes successfully.
        This should be called from the conversation completion flow.

        Args:
            conversation_id: The chatbot conversation ID
            message_conversation_id: The message conversation ID
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID
            reply_headers: Reply headers stored in conversation state
            execution_details: Additional execution details
        """
        try:
            if not reply_headers or not reply_headers.get("replyToExchange") or not reply_headers.get("replyToEvent"):
                logger.warning(f"No reply headers found for conversation {conversation_id}, skipping workflow success event")
                return

            # Get RabbitMQ channel for publishing
            if not await rabbitmq_service.is_healthy():
                logger.error("RabbitMQ not connected, cannot publish workflow success event")
                return

            channel = await rabbitmq_service.get_channel()
            if not channel:
                logger.error("Failed to get RabbitMQ channel for workflow success event")
                return

            await self._publish_workflow_response(
                status="SUCCESS",
                status_code=200,
                entity_id=message_conversation_id,
                channel=channel,
                reply_to_exchange=reply_headers["replyToExchange"],
                reply_to_event=reply_headers["replyToEvent"],
                event_id=reply_headers.get("eventId"),
                execution_details={
                    "conversationId": conversation_id,
                    "chatbotId": chatbot_id,
                    "tenantId": tenant_id,
                    "completionTimestamp": datetime.now(timezone.utc).isoformat(),
                    **(execution_details or {})
                }
            )

            logger.info(f"✅ PUBLISHED WORKFLOW COMPLETION SUCCESS - Conversation: {conversation_id}")

        except Exception as e:
            logger.error(f"❌ FAILED TO PUBLISH WORKFLOW COMPLETION SUCCESS: {str(e)}")
            # Don't re-raise to avoid breaking the conversation completion

    async def _publish_workflow_error_response(
        self,
        error_code: str,
        error_message: str,
        reply_headers: Optional[Dict[str, Any]] = None
    ):
        """
        Publish an error response to the workflow when chatbot validation fails
        
        Args:
            error_code: Error code for the failure
            error_message: Error message describing the failure
            reply_headers: Headers for replying to the workflow
        """
        try:
            if not reply_headers:
                logger.warning("No reply headers provided, cannot publish workflow error response")
                return
            
            # Extract reply information from headers
            reply_to_exchange = reply_headers.get("replyToExchange")
            reply_to_event = reply_headers.get("replyToEvent")
            event_id = reply_headers.get("eventId")
            entity_id = reply_headers.get("entityId")
            
            if not reply_to_exchange or not reply_to_event:
                logger.warning(f"Missing reply exchange or event in headers: {reply_headers}")
                return
            
            # Get RabbitMQ channel for publishing
            if not await rabbitmq_service.is_healthy():
                logger.error("RabbitMQ not connected, cannot publish workflow error response")
                return

            channel = await rabbitmq_service.get_channel()
            if not channel:
                logger.error("Failed to get RabbitMQ channel for workflow error response")
                return
                
            # Publish the error response
            await self._publish_workflow_response(
                status="FAILED",
                status_code=400,
                entity_id=entity_id,
                channel=channel,
                reply_to_exchange=reply_to_exchange,
                reply_to_event=reply_to_event,
                event_id=event_id,
                error_code=error_code,
                error_message=error_message
            )
            
            logger.info(f"✅ WORKFLOW ERROR RESPONSE PUBLISHED - Error: {error_code}, Message: {error_message}")
                
        except Exception as e:
            logger.error(f"❌ FAILED TO PUBLISH WORKFLOW ERROR RESPONSE: {str(e)}")

    async def publish_workflow_completion_failure(
        self,
        conversation_id: str,
        message_conversation_id: int,
        error_code: str,
        error_message: str,
        reply_headers: Dict[str, Any]
    ):
        """
        Publish workflow failure event when conversation fails.

        Args:
            conversation_id: The chatbot conversation ID
            message_conversation_id: The message conversation ID
            error_code: Error code for the failure
            error_message: Error message for the failure
            reply_headers: Reply headers stored in conversation state
        """
        try:
            if not reply_headers or not reply_headers.get("replyToExchange") or not reply_headers.get("replyToEvent"):
                logger.warning(f"No reply headers found for conversation {conversation_id}, skipping workflow failure event")
                return

            # Get RabbitMQ channel for publishing
            if not await rabbitmq_service.is_healthy():
                logger.error("RabbitMQ not connected, cannot publish workflow failure event")
                return

            channel = await rabbitmq_service.get_channel()
            if not channel:
                logger.error("Failed to get RabbitMQ channel for workflow failure event")
                return

            await self._publish_workflow_response(
                status="FAILED",
                status_code=500,
                entity_id=message_conversation_id,
                channel=channel,
                reply_to_exchange=reply_headers["replyToExchange"],
                reply_to_event=reply_headers["replyToEvent"],
                event_id=reply_headers.get("eventId"),
                error_code=error_code,
                error_message=error_message
            )

            logger.info(f"❌ PUBLISHED WORKFLOW COMPLETION FAILURE - Conversation: {conversation_id}, Error: {error_code}")

        except Exception as e:
            logger.error(f"❌ FAILED TO PUBLISH WORKFLOW COMPLETION FAILURE: {str(e)}")
            # Don't re-raise to avoid breaking the conversation completion


    def _validate_entity_configuration(
        self,
        chatbot,
        entity_details: List[Dict[str, Any]]
    ) -> tuple[bool, Optional[str]]:
        """
        Validate if any entity in entity_details is configured on the chatbot.
        
        Args:
            chatbot: The chatbot object
            entity_details: List of entity details from the workflow trigger
            
        Returns:
            Tuple of (is_valid, error_message)
            - (True, None) if validation passes
            - (False, error_message) if validation fails
        """
        try:
            # If chatbot has no entity configuration, allow all entities (backward compatibility)
            if not chatbot.entities or len(chatbot.entities) == 0:
                logger.info(f"✅ ENTITY VALIDATION - No entity filter configured on chatbot {chatbot.id}, allowing all entities")
                return (True, None)
            
            # Get configured entities from chatbot
            configured_entities = [
                entity.get('entity', '').upper() 
                for entity in chatbot.entities 
                if entity.get('entity')
            ]
            
            logger.info(f"🔍 ENTITY VALIDATION - Chatbot {chatbot.id} configured entities: {configured_entities}")
            
            # If no entity_details provided, this is invalid
            if not entity_details or len(entity_details) == 0:
                error_msg = f"Unable to trigger chatbot because no entity details provided in the workflow trigger"
                logger.warning(f"❌ ENTITY VALIDATION FAILED - {error_msg}")
                return (False, error_msg)
            
            # Extract entity types from entity_details
            trigger_entities = []
            for entity in entity_details:
                # Handle both 'entityType' and 'entity' keys
                entity_type = (entity.get('entityType') or entity.get('entity', '')).upper()
                if entity_type:
                    trigger_entities.append(entity_type)
            
            logger.info(f"🔍 ENTITY VALIDATION - Workflow trigger entities: {trigger_entities}")
            
            # Check if any trigger entity is in the configured entities
            matching_entities = [e for e in trigger_entities if e in configured_entities]
            
            if matching_entities:
                logger.info(f"✅ ENTITY VALIDATION PASSED - Found matching entities: {matching_entities}")
                return (True, None)
            else:
                error_msg = (
                    f"Unable to trigger chatbot because no matching entity is configured. "
                    f"Configured entities: {', '.join(configured_entities)}. "
                    f"Received entities: {', '.join(trigger_entities)}"
                )
                logger.warning(f"❌ ENTITY VALIDATION FAILED - {error_msg}")
                return (False, error_msg)
                
        except Exception as e:
            logger.error(f"❌ ENTITY VALIDATION ERROR: {str(e)}")
            # On error, allow the trigger to proceed (fail-safe)
            return (True, None)

    async def _process_rule_based_workflow_trigger(
        self,
        chatbot,
        conversation_id: str,
        conversation_state: Dict[str, Any],
        tenant_id: int,
        message_conversation_id: int,
        connected_account: Dict[str, Any],
        entity_details: list,
        db: Session
    ):
        """
        Process RULE-based chatbot workflow trigger by processing the first node and any chained nodes.
        This method leverages the existing chatbot service logic for start conversation.

        Args:
            chatbot: The chatbot object
            conversation_id: The conversation ID
            conversation_state: The conversation state
            tenant_id: The tenant ID
            message_conversation_id: The message conversation ID
            connected_account: Connected account information
            entity_details: List of entity details
            db: Database session
        """
        try:
            logger.info(f"🔄 PROCESSING RULE-BASED WORKFLOW - Chatbot: {chatbot.id}")

            # Get the first question/node for the chatbot
            first_question = await self._get_first_question(chatbot, db)

            if not first_question:
                logger.error(f"❌ NO FIRST NODE FOUND - Chatbot: {chatbot.id}")
                return

            # Use chatbot's welcome message
            welcome_message = chatbot.welcome_message or f"Welcome to {chatbot.name}!"
            logger.info(f"💬 WELCOME MESSAGE: {welcome_message}")

            # Calculate charge for RULE-based chatbots
            charge = 0.25
            logger.info(f"💰 CHARGE CALCULATED: {charge} (RULE type chatbot)")

            # Update conversation state with charge information
            conversation_state["current_charge"] = charge
            conversation_state["total_charge"] = charge

            # Set the current node in the state to the first node
            conversation_state["rule_current_node_id"] = first_question["id"]

            # Store updated conversation state
            self.redis_service.store_conversation_state(conversation_id, conversation_state)

            # Use the chatbot service to publish the start conversation event with proper node processing
            from app.services.chatbot_service import ChatbotService
            chatbot_service = ChatbotService()
            
            # Generate proper JWT token for entity field processing and variable substitution
            auth_token = None
            try:
                # Get user_id from conversation state
                user_id = conversation_state.get("user_id")
                if user_id and tenant_id:
                    auth_token, actual_user_id = jwt_service.build_jwt_token_for_analysis(str(user_id), str(tenant_id))
                    logger.info(f"✅ JWT TOKEN CREATED FOR CONVERSATION CONTINUATION - User ID: {actual_user_id}")
                    logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Auth token length: {len(auth_token) if auth_token else 0}")
                    logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Entity details count: {len(entity_details) if entity_details else 0}")
                    if entity_details:
                        logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Entity details: {entity_details}")
                else:
                    logger.warning(f"⚠️ JWT TOKEN NOT CREATED - Missing user_id or tenant_id for conversation continuation")
                    logger.warning(f"🔧 VARIABLE REPLACEMENT DEBUG - user_id: {user_id}, tenant_id: {tenant_id}")
            except Exception as e:
                logger.error(f"❌ JWT TOKEN CREATION FAILED FOR CONVERSATION CONTINUATION: {str(e)}")
                # Continue processing without token - don't fail the whole workflow

            # Publish start conversation event which handles first node processing and chaining
            success = await chatbot_service.publish_start_conversation_event(
                chatbot=chatbot,
                tenant_id=tenant_id,
                conversation_id=conversation_id,
                welcome_message=welcome_message,
                first_question=first_question,
                charge=charge,
                message_conversation_id=message_conversation_id,
                db=db,
                entity_details=entity_details,
                auth_token=auth_token
            )

            if success:
                logger.info(f"✅ RULE-BASED WORKFLOW PROCESSED - Conversation ID: {conversation_id}")
            else:
                logger.error(f"❌ FAILED TO PROCESS RULE-BASED WORKFLOW - Conversation ID: {conversation_id}")

        except Exception as e:
            logger.error(f"❌ RULE-BASED WORKFLOW PROCESSING FAILED: {str(e)}")
            raise


# Global instance
workflow_event_listener = WorkflowEventListener()
