"""
Conversation Event Publisher Service

This service handles publishing conversation events to the message queue system.
It publishes chatbot conversation responses to the ex.whatsappChatbot exchange.
"""

import json
import logging
from typing import Optional, Dict, Any, List
from app.services.rabbitmq_service import rabbitmq_service

logger = logging.getLogger(__name__)


class ConversationEventPublisher:
    """
    Publisher for conversation events in the WhatsApp chatbot system
    """
    
    def __init__(self):
        self.exchange_name = "ex.whatsappChatbot"
        self.routing_key = "chatbot.conversation.response"
    
    async def publish_conversation_response(
        self,
        chatbot_conversation_id: str,
        message: str,
        completed: bool = False,
        charge: int = 0,
        tenant_id: Optional[int] = None,
        message_conversation_id: Optional[Any] = None,
        extra: Optional[Dict[str, Any]] = None,
        chatbot_id: Optional[str] = None,
        chatbot_name: Optional[str] = None,
        entity_details: Optional[List[Dict[str, Any]]] = None,
        welcome_message: Optional[str] = None,
        chatbot_type: Optional[str] = None
    ) -> bool:
        """
        Publish a conversation response event
        
        Args:
            chatbot_conversation_id: UUID of the conversation
            message: The response message from the chatbot
            completed: Whether the conversation is completed
            charge: Charge amount (1 for predefined questions, 2 for custom questions)
            tenant_id: Tenant ID for multi-tenancy support
            message_conversation_id: Message conversation ID for tracking
            extra: Additional metadata for the event
            chatbot_id: Chatbot ID for metadata
            chatbot_name: Chatbot name for metadata
            entity_details: List of entity details associated with the conversation
            welcome_message: Optional welcome message (for first AI chatbot message)
            chatbot_type: Optional chatbot type (AI, RULE, etc.)
            
        Returns:
            bool: True if event was published successfully, False otherwise
        """
        try:
            # Prepare event payload
            event_payload = {
                "chatbotConversationId": chatbot_conversation_id,
                "message": message,
                "completed": completed,
                "charge": charge
            }
            
            # Add tenant ID if provided
            if tenant_id is not None:
                event_payload["tenantId"] = tenant_id

            # Include optional correlation/message id and extra fields if provided
            if message_conversation_id is not None:
                event_payload["messageConversationId"] = message_conversation_id
            
            # Add entity details if provided (explicit parameter takes priority)
            if entity_details:
                event_payload["entityDetails"] = entity_details
            elif extra and isinstance(extra, dict) and "entityDetails" in extra:
                event_payload["entityDetails"] = extra["entityDetails"]
            
            # Merge other extra fields (excluding entityDetails if already added)
            if extra and isinstance(extra, dict):
                extra_copy = {k: v for k, v in extra.items() if k != "entityDetails" or "entityDetails" not in event_payload}
                event_payload.update(extra_copy)
            
            # Add chatbot metadata - prioritize parameters over extra data
            if chatbot_id:
                event_payload["chatbotId"] = chatbot_id
            elif extra and isinstance(extra, dict) and "chatbot" in extra and "id" in extra["chatbot"]:
                event_payload["chatbotId"] = extra["chatbot"]["id"]
            
            if chatbot_name:
                event_payload["chatbotName"] = chatbot_name
            elif extra and isinstance(extra, dict) and "chatbot" in extra and "name" in extra["chatbot"]:
                event_payload["chatbotName"] = extra["chatbot"]["name"]
            
            # Add welcome message if provided (for first AI chatbot message)
            if welcome_message:
                event_payload["welcomeMessage"] = welcome_message
            
            # Add chatbot type at top level if provided
            if chatbot_type:
                event_payload["chatbotType"] = chatbot_type

            # Log the exact payload being sent
            try:
                safe_payload = {k: v for k, v in event_payload.items() if k not in ['token', 'password', 'secret']}
                logger.info(f"📦 CONVERSATION PUBLISHER - Outgoing payload: {json.dumps(safe_payload, default=str)}")
            except Exception as e:
                logger.warning(f"📦 CONVERSATION PUBLISHER - Could not serialize payload for logging: {str(e)}")
            
            # Publish the event
            success = await self._publish_event(
                routing_key=self.routing_key,
                payload=event_payload,
                event_type="conversation_response"
            )
            
            if success:
                logger.info(f"Successfully published conversation response event for conversation {chatbot_conversation_id}")
            else:
                logger.error(f"Failed to publish conversation response event for conversation {chatbot_conversation_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error publishing conversation response event: {str(e)}")
            return False
    

    
    async def publish_conversation_completion(
        self,
        chatbot_conversation_id: str,
        completion_message: str,
        charge: int = 0,
        chatbot_type: str = "RULE",
        tenant_id: Optional[int] = None,
        entity_details: Optional[List[Dict[str, Any]]] = None,
        node_details: Optional[Dict[str, Any]] = None,
        message_conversation_id: Optional[int] = None,
        chatbot_id: Optional[str] = None,
        chatbot_name: Optional[str] = None
    ) -> bool:
        """
        Publish a conversation completion event with new payload format
        
        Args:
            chatbot_conversation_id: UUID of the conversation
            completion_message: Final message to the user
            charge: Charge amount for the completion message
            chatbot_type: Type of chatbot (RULE, AI, etc.)
            tenant_id: Tenant ID for multi-tenancy support
            entity_details: List of entity details with ownerId
            node_details: Node details for the completion
            message_conversation_id: Message conversation ID for tracking
            chatbot_id: Chatbot ID for metadata
            chatbot_name: Chatbot name for metadata
            
        Returns:
            bool: True if event was published successfully, False otherwise
        """
        try:
            # Prepare new payload format
            event_payload = {
                "chatbotConversationId": chatbot_conversation_id,
                "message": completion_message,
                "completed": True,
                "charge": charge,
                "chatbotType": chatbot_type
            }
            
            # Add tenant ID if provided
            if tenant_id is not None:
                event_payload["tenantId"] = tenant_id
            
            # Add entity details if provided
            if entity_details:
                event_payload["entityDetails"] = entity_details
            
            # Add node details if provided
            if node_details:
                event_payload["nodeDetails"] = node_details
            
            # Add message conversation ID if provided
            if message_conversation_id is not None:
                event_payload["messageConversationId"] = message_conversation_id
            
            # Add chatbot metadata - prioritize parameters over extra data
            if chatbot_id:
                event_payload["chatbotId"] = chatbot_id
            if chatbot_name:
                event_payload["chatbotName"] = chatbot_name
            
            # Log the exact payload being sent
            try:
                safe_payload = {k: v for k, v in event_payload.items() if k not in ['token', 'password', 'secret']}
                logger.info(f"📦 CONVERSATION COMPLETION PUBLISHER - Outgoing payload: {json.dumps(safe_payload, default=str)}")
            except Exception as e:
                logger.warning(f"📦 CONVERSATION COMPLETION PUBLISHER - Could not serialize payload for logging: {str(e)}")
            
            # Publish the event
            success = await self._publish_event(
                routing_key=self.routing_key,
                payload=event_payload,
                event_type="conversation_completion"
            )
            
            if success:
                logger.info(f"Successfully published conversation completion event for conversation {chatbot_conversation_id}")
            else:
                logger.error(f"Failed to publish conversation completion event for conversation {chatbot_conversation_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error publishing conversation completion event: {str(e)}")
            return False
    
    async def _publish_event(
        self,
        routing_key: str,
        payload: Dict[str, Any],
        event_type: str
    ) -> bool:
        """
        Internal method to publish events to RabbitMQ with retry logic

        Args:
            routing_key: Routing key for the message
            payload: Event payload
            event_type: Type of event for logging

        Returns:
            bool: True if published successfully, False otherwise
        """
        try:
            logger.info(f"💬 PUBLISHING CONVERSATION EVENT - Type: {event_type}")
            logger.info(f"💬 CONVERSATION EVENT - Routing Key: {routing_key}")
            logger.info(f"💬 CONVERSATION EVENT - Exchange: {self.exchange_name}")

            # Log payload details
            logger.info(f"💬 CONVERSATION EVENT - Payload Keys: {list(payload.keys()) if isinstance(payload, dict) else 'Not a dict'}")
            if isinstance(payload, dict):
                # Log important fields without exposing sensitive data
                safe_payload = {k: v for k, v in payload.items() if k not in ['token', 'password', 'secret']}
                logger.info(f"💬 CONVERSATION EVENT - Payload: {safe_payload}")
            else:
                logger.info(f"💬 CONVERSATION EVENT - Payload: {payload}")

            # Convert payload to JSON string
            message_body = json.dumps(payload, default=str)
            logger.info(f"💬 CONVERSATION EVENT - Serialized Size: {len(message_body)} bytes")

            # Publish the message (rabbitmq_service.publish_message now handles reconnection)
            await rabbitmq_service.publish_message(
                exchange=self.exchange_name,
                routing_key=routing_key,
                message=message_body,
                durable=True
            )

            logger.info(f"✅ CONVERSATION EVENT PUBLISHED - Type: {event_type}, Routing Key: {routing_key}")

            return True

        except Exception as e:
            logger.error(f"❌ CONVERSATION EVENT PUBLISH FAILED - Type: {event_type}, Error: {str(e)}")
            return False


# Global instance
conversation_event_publisher = ConversationEventPublisher()
