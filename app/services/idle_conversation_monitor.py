"""
Idle Conversation Monitor Service

This service monitors conversations for inactivity and automatically handles cleanup
when conversations are idle for 45 minutes. It clears Redis state, stores data in DB,
marks conversations as completed, and publishes entity update and conversation completion events.
"""

import asyncio
import logging
import json
from datetime import datetime, timezone
from typing import Dict, Any, List
from sqlalchemy.orm import Session

from app.database import get_db
from app.models import ChatbotConversation
from app.services.redis_service import RedisService
from app.services.chatbot_service import ChatbotService
from app.services.conversation_event_publisher import ConversationEventPublisher
from app.services.chatbot_event_publisher import ChatbotEventPublisher
from app.services.workflow_event_listener import workflow_event_listener
from app.utils.conversation_state_utils import update_conversation_in_db

logger = logging.getLogger(__name__)


class IdleConversationMonitor:
    """
    Monitors conversations for inactivity and handles cleanup
    """
    
    def __init__(self):
        self.redis_service = RedisService()
        self.chatbot_service = ChatbotService()
        self.conversation_event_publisher = ConversationEventPublisher()
        self.chatbot_event_publisher = ChatbotEventPublisher()
        self.is_monitoring = False
        self.monitor_task = None
        self.idle_threshold_seconds = 2700  # 45 minutes
        
    async def start_monitoring(self):
        """Start monitoring for idle conversations"""
        if self.is_monitoring:
            logger.warning("Idle conversation monitor is already running")
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("Started idle conversation monitor")
    
    async def stop_monitoring(self):
        """Stop monitoring for idle conversations"""
        if not self.is_monitoring:
            logger.warning("Idle conversation monitor is not running")
            return
        
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped idle conversation monitor")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self._check_idle_conversations()
                # Check every 30 seconds
                await asyncio.sleep(30)
            except asyncio.CancelledError:
                logger.info("Idle conversation monitor loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in idle conversation monitor loop: {str(e)}")
                await asyncio.sleep(30)  # Continue monitoring even if there's an error
    
    async def _check_idle_conversations(self):
        """Check for idle conversations and handle cleanup"""
        try:
            # Get idle conversations (idle for 45 minutes)
            idle_conversations = self.redis_service.get_idle_conversations(
                idle_threshold_seconds=self.idle_threshold_seconds
            )
            
            if not idle_conversations:
                return
            
            logger.info(f"Found {len(idle_conversations)} idle conversations")
            
            for idle_info in idle_conversations:
                conversation_id = idle_info['conversation_id']
                idle_time = idle_info['idle_time_seconds']
                
                logger.info(f"Processing idle conversation {conversation_id} (idle for {idle_time} seconds)")
                
                # Try to acquire lock to prevent race conditions
                if not self.redis_service.acquire_idle_lock(conversation_id):
                    logger.info(f"Could not acquire lock for conversation {conversation_id}, skipping")
                    continue
                
                try:
                    await self._handle_idle_conversation(conversation_id)
                except Exception as e:
                    logger.error(f"Error handling idle conversation {conversation_id}: {str(e)}")
                finally:
                    # Always release the lock
                    self.redis_service.release_idle_lock(conversation_id)
                    
        except Exception as e:
            logger.error(f"Error checking idle conversations: {str(e)}")
    
    async def _handle_idle_conversation(self, conversation_id: str):
        """Handle cleanup for an idle conversation"""
        try:
            # Get conversation state from Redis
            state = self.redis_service.get_conversation_state(conversation_id)
            
            if not state:
                logger.warning(f"No state found for idle conversation {conversation_id}")
                return
            
            logger.info(f"Processing idle conversation cleanup for {conversation_id}")
            
            # Get database session
            db_gen = get_db()
            db: Session = next(db_gen)
            
            try:
                # Update conversation state to mark as completed
                state["ended"] = True
                state["auto_completed"] = True
                state["completion_reason"] = "idle_timeout"
                state["completed_at"] = datetime.now(timezone.utc).isoformat()
                
                # Store final state in database
                success = update_conversation_in_db(
                    db, conversation_id, state, 
                    completed=True, ended=True
                )
                
                if not success:
                    logger.error(f"Failed to update conversation {conversation_id} in database")
                    return
                
                # Handle entity updates if there are answers
                if state.get("answers") and state.get("entity_details"):
                    await self._handle_entity_updates(state, conversation_id)
                
                # Clear Redis state
                self._clear_redis_state(conversation_id)
                
                # Publish conversation completion event
                await self._publish_completion_event(conversation_id, state)
                
                # Publish workflow failure event if this conversation was started by a workflow
                await self._publish_workflow_failure_event(conversation_id, state, "041202", "Conversation timed out due to inactivity")
                
                logger.info(f"Successfully completed idle conversation cleanup for {conversation_id}")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error handling idle conversation {conversation_id}: {str(e)}")
            raise
    
    async def _handle_entity_updates(self, state: Dict[str, Any], conversation_id: str):
        """Handle entity updates for the idle conversation"""
        try:
            answers = state.get("answers", [])
            entity_details = state.get("entity_details", [])
            tenant_id = state.get("tenant_id")
            user_id = state.get("user_id")
            chatbot_id = state.get("chatbot_id")
            
            if not answers or not entity_details or not tenant_id:
                logger.info(f"No entity updates needed for conversation {conversation_id}")
                return
            
            logger.info(f"Processing entity updates for idle conversation {conversation_id}")
            
            # Update entities using the chatbot service
            await self.chatbot_service.update_entities_after_conversation(
                entity_details=entity_details,
                collected_answers=answers,
                tenant_id=int(tenant_id),
                user_id=user_id,
                chatbot_id=chatbot_id,
                conversation_state=state
            )
            
            logger.info(f"Completed entity updates for idle conversation {conversation_id}")
            
        except Exception as e:
            logger.error(f"Error handling entity updates for conversation {conversation_id}: {str(e)}")
    
    def _clear_redis_state(self, conversation_id: str):
        """Clear Redis state for the conversation"""
        try:
            # Delete conversation state
            key = f"conversation:{conversation_id}"
            self.redis_service.redis_client.delete(key)
            
            # Delete last activity timestamp
            activity_key = f"conversation_last_activity:{conversation_id}"
            self.redis_service.redis_client.delete(activity_key)
            
            logger.info(f"Cleared Redis state for conversation {conversation_id}")
            
        except Exception as e:
            logger.error(f"Error clearing Redis state for conversation {conversation_id}: {str(e)}")
    
    async def _publish_completion_event(self, conversation_id: str, state: Dict[str, Any]):
        """Publish conversation completion event"""
        try:
            completion_message = "Your conversation has been automatically completed due to inactivity. Thank you for using our service!"
            
            # Publish conversation completion event
            await self.conversation_event_publisher.publish_conversation_completion(
                chatbot_conversation_id=conversation_id,
                completion_message=completion_message,
                charge=0,
                tenant_id=state.get("tenant_id"),
                chatbot_id=state.get("chatbot_id"),
                chatbot_name=state.get("chatbot_name")
            )
            
            logger.info(f"Published completion event for idle conversation {conversation_id}")
            
        except Exception as e:
            logger.error(f"Error publishing completion event for conversation {conversation_id}: {str(e)}")

    async def _publish_workflow_failure_event(self, conversation_id: str, state: Dict[str, Any], error_code: str, error_message: str):
        """Publish workflow failure event if this conversation was started by a workflow"""
        try:
            # Check if this conversation has workflow reply headers
            workflow_reply_headers = state.get("workflow_reply_headers")
            if not workflow_reply_headers:
                logger.info(f"No workflow reply headers found for conversation {conversation_id}, skipping workflow failure event")
                return
            
            from app.services.workflow_event_listener import workflow_event_listener
            
            await workflow_event_listener.publish_workflow_completion_failure(
                conversation_id=conversation_id,
                message_conversation_id=state.get("message_conversation_id"),
                error_code=error_code,
                error_message=error_message,
                reply_headers=workflow_reply_headers
            )
            
            logger.info(f"Published workflow failure event for conversation {conversation_id}")
            
        except Exception as e:
            logger.error(f"Error publishing workflow failure event for conversation {conversation_id}: {str(e)}")
            # Don't re-raise to avoid breaking the idle conversation cleanup


# Global instance
idle_monitor = IdleConversationMonitor() 