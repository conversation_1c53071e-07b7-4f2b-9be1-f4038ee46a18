import logging
import uuid
import json
import httpx
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func, case, desc
from fastapi import HTTPException, UploadFile
from io import BytesIO
import PyPDF2
from app.database import get_db
from app.models import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ChatbotQuestionEntityField,
    ChatbotKnowledgebase,
    ChatbotConversation,
    Document,
    ChatbotCreate,
    Chat<PERSON><PERSON><PERSON>date,
    Question<PERSON><PERSON>,
    QuestionUpdate,
    QuestionCreateLegacy,
    QuestionUpdateLegacy,
    KnowledgebaseDocument,
    KnowledgebaseUpdateRequest,
    ChatbotNode,
    Cha<PERSON>botEdge,
    ChatbotNodeEntityField
)
from app.services.rabbitmq_service import rabbitmq_service
from app.services.elasticsearch_service import ElasticsearchService
from app.services.s3_service import S3Service
from app.services.user_service import user_service
from app.utils.permission_validator import validate_chatbot_permissions
from app.services.conversation_event_publisher import conversation_event_publisher
from app.services.entity_field_service import Enti<PERSON><PERSON>ieldService
from app.models import Cha<PERSON><PERSON><PERSON><PERSON>, Cha<PERSON><PERSON><PERSON><PERSON>
from app.services.chatbot_event_publisher import Chatbot<PERSON>ventPublisher
from app.exceptions import ChatbotLimitExceededError, EntityConfigurationError

logger = logging.getLogger(__name__)

# File upload validation constants
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB in bytes
MAX_FILE_COUNT = 2  # Maximum number of files per upload


class ChatbotService:
    """
    Service class for chatbot-related operations including usage collection
    """
    
    def __init__(self):
        """
        Initialize the ChatbotService
        """
        logger.info("Initialized ChatbotService")
    
    def _get_allowed_entities(self, chatbot_id: str, tenant_id: int) -> Optional[List[str]]:
        """
        Get list of allowed entity types for a chatbot
        
        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID
            
        Returns:
            Optional[List[str]]: List of allowed entity type IDs (uppercased), or None if no restrictions
        """
        db = None
        try:
            db = next(get_db())
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()
            
            if not chatbot:
                logger.warning(f"⚠️ ENTITY FILTER - Chatbot {chatbot_id} not found, allowing all entities")
                return None
                
            if not chatbot.entities:
                logger.info(f"🔓 ENTITY FILTER - No entities configured for chatbot {chatbot_id}, allowing all entities")
                return None
            
            # Extract entity types from entities configuration
            allowed_entities = [entity.get('entity', '').upper() for entity in chatbot.entities if entity.get('entity')]
            
            logger.info(f"🔒 ENTITY FILTER - Loaded {len(allowed_entities)} allowed entities for chatbot {chatbot_id}: {allowed_entities}")
            return allowed_entities
        except Exception as e:
            logger.error(f"❌ ENTITY FILTER - Error loading chatbot entities: {str(e)}, allowing all entities")
            return None
        finally:
            if db:
                db.close()
    
    async def collect_and_publish_chatbot_usage(self):
        """
        Collect chatbot usage data and publish it to RabbitMQ
        
        This method:
        1. Queries the database for non-DRAFT chatbots grouped by tenant
        2. Formats the data for usage reporting
        3. Publishes the usage data to the RabbitMQ exchange
        """
        db = None
        try:
            logger.info("Starting chatbot usage collection...")
            
            db = next(get_db())
            
            # Get total non-Draft status chatbots by tenantId
            usage_data = (
                db.query(Chatbot.tenant_id, func.count(Chatbot.id))
                .filter(Chatbot.status != "DRAFT")
                .group_by(Chatbot.tenant_id)
                .all()
            )
            
            # Format the payload for usage reporting
            payload = [
                {
                    "tenantId": tenant_id,
                    "usageEntity": "CHATBOT",
                    "count": count
                }
                for tenant_id, count in usage_data
            ]
            
            # Publish the usage data if there's any data to publish
            if payload:
                await rabbitmq_service.publish_message(
                    "ex.usage",
                    "usage.collect.response",
                    payload
                )
                logger.info(f"Published chatbot usage data for {len(payload)} tenants: {payload}")
            else:
                logger.info("No chatbot usage data to publish.")
            
            logger.info("Chatbot usage collection completed successfully")
            
        except Exception as e:
            logger.error(f"Error collecting chatbot usage: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_count_by_tenant(self, tenant_id: int, include_draft: bool = False) -> int:
        """
        Get the count of chatbots for a specific tenant
        
        Args:
            tenant_id: The tenant ID to get chatbot count for
            include_draft: Whether to include DRAFT status chatbots in the count
            
        Returns:
            int: Number of chatbots for the tenant
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(func.count(Chatbot.id)).filter(Chatbot.tenant_id == tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            count = query.scalar()
            
            logger.info(f"Chatbot count for tenant {tenant_id}: {count} (include_draft: {include_draft})")
            return count
            
        except Exception as e:
            logger.error(f"Error getting chatbot count for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_all_tenant_chatbot_counts(self, include_draft: bool = False) -> List[Dict[str, Any]]:
        """
        Get chatbot counts for all tenants
        
        Args:
            include_draft: Whether to include DRAFT status chatbots in the count
            
        Returns:
            List[Dict]: List of dictionaries containing tenant_id and count
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(Chatbot.tenant_id, func.count(Chatbot.id)).group_by(Chatbot.tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            results = query.all()
            
            tenant_counts = [
                {
                    "tenant_id": tenant_id,
                    "count": count
                }
                for tenant_id, count in results
            ]
            
            logger.info(f"Retrieved chatbot counts for {len(tenant_counts)} tenants (include_draft: {include_draft})")
            return tenant_counts
            
        except Exception as e:
            logger.error(f"Error getting chatbot counts for all tenants: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_by_id(self, chatbot_id: str, tenant_id: int) -> Optional[Chatbot]:
        """
        Get a chatbot by ID and tenant ID
        
        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID
            
        Returns:
            Optional[Chatbot]: The chatbot if found, None otherwise
        """
        db = None
        try:
            db = next(get_db())
            
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()
            
            if chatbot:
                logger.info(f"Found chatbot {chatbot_id} for tenant {tenant_id}")
            else:
                logger.warning(f"Chatbot {chatbot_id} not found for tenant {tenant_id}")
            
            return chatbot
            
        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id} for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbots_by_tenant(self, tenant_id: int, include_draft: bool = True) -> List[Chatbot]:
        """
        Get all chatbots for a specific tenant
        
        Args:
            tenant_id: The tenant ID
            include_draft: Whether to include DRAFT status chatbots
            
        Returns:
            List[Chatbot]: List of chatbots for the tenant
        """
        db = None
        try:
            db = next(get_db())
            
            query = db.query(Chatbot).filter(Chatbot.tenant_id == tenant_id)
            
            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")
            
            chatbots = query.all()
            
            logger.info(f"Found {len(chatbots)} chatbots for tenant {tenant_id} (include_draft: {include_draft})")
            return chatbots
            
        except Exception as e:
            logger.error(f"Error getting chatbots for tenant {tenant_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()
    
    def get_chatbot_statistics(self, tenant_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Get comprehensive chatbot statistics
        
        Args:
            tenant_id: Optional tenant ID to filter statistics for a specific tenant
            
        Returns:
            Dict[str, Any]: Dictionary containing various chatbot statistics
        """
        db = None
        try:
            db = next(get_db())
            
            stats = {}
            
            if tenant_id:
                # Statistics for a specific tenant
                total_query = db.query(func.count(Chatbot.id)).filter(Chatbot.tenant_id == tenant_id)
                active_query = db.query(func.count(Chatbot.id)).filter(
                    Chatbot.tenant_id == tenant_id,
                    Chatbot.status == "ACTIVE"
                )
                draft_query = db.query(func.count(Chatbot.id)).filter(
                    Chatbot.tenant_id == tenant_id,
                    Chatbot.status == "DRAFT"
                )
                
                stats = {
                    "tenant_id": tenant_id,
                    "total_chatbots": total_query.scalar(),
                    "active_chatbots": active_query.scalar(),
                    "draft_chatbots": draft_query.scalar()
                }
            else:
                # Global statistics
                total_chatbots = db.query(func.count(Chatbot.id)).scalar()
                active_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "ACTIVE").scalar()
                draft_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "DRAFT").scalar()
                inactive_chatbots = db.query(func.count(Chatbot.id)).filter(Chatbot.status == "INACTIVE").scalar()
                
                # Count by tenant
                tenant_counts = db.query(Chatbot.tenant_id, func.count(Chatbot.id)).group_by(Chatbot.tenant_id).all()
                
                stats = {
                    "total_chatbots": total_chatbots,
                    "active_chatbots": active_chatbots,
                    "draft_chatbots": draft_chatbots,
                    "inactive_chatbots": inactive_chatbots,
                    "total_tenants": len(tenant_counts),
                    "tenant_breakdown": [
                        {"tenant_id": tenant_id, "count": count}
                        for tenant_id, count in tenant_counts
                    ]
                }
            
            logger.info(f"Generated chatbot statistics: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error getting chatbot statistics: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    # CRUD Operations

    async def create_chatbot(self, chatbot_data: ChatbotCreate, tenant_id: int, user_id: str, token: str, auth_context=None) -> Dict[str, Any]:
        """
        Create a new chatbot

        Args:
            chatbot_data: ChatbotCreate model with chatbot information
            tenant_id: The tenant ID
            user_id: The user ID creating the chatbot
            token: Authorization token for user validation
            auth_context: Authentication context with permissions

        Returns:
            Dict[str, Any]: Created chatbot information
        """
        db = None
        try:
            db = next(get_db())

            # Validate user permissions for chatbot creation
            if auth_context:
                validate_chatbot_permissions(auth_context)

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Validate chatbot limit (max 25 chatbots per tenant)
            MAX_CHATBOTS_PER_TENANT = 25
            current_chatbot_count = self.get_chatbot_count_by_tenant(tenant_id, include_draft=True)
            
            if current_chatbot_count >= MAX_CHATBOTS_PER_TENANT:
                raise ChatbotLimitExceededError(
                    current_count=current_chatbot_count,
                    max_allowed=MAX_CHATBOTS_PER_TENANT,
                    tenant_id=tenant_id
                )
            
            logger.info(f"Chatbot count validation passed. Current count: {current_chatbot_count}, Max allowed: {MAX_CHATBOTS_PER_TENANT}")

            # Validate type value
            chatbot_type = chatbot_data.type.upper()
            if chatbot_type not in ["AI", "RULE"]:
                raise HTTPException(status_code=400, detail="Chatbot type must be either 'AI' or 'RULE'")

            # Generate unique chatbot ID
            chatbot_id = str(uuid.uuid4())

            # Extract connected account information (account-based approach)
            connected_account_display_name = None
            connected_account_id = None

            if chatbot_data.connectedAccount:
                connected_account_display_name = chatbot_data.connectedAccount.displayName
                connected_account_id = chatbot_data.connectedAccount.accountId

            # Validate that connected account is provided for account-based chatbots
            if not connected_account_id:
                raise HTTPException(status_code=400, detail="Connected account ID is required for chatbot creation")

            # Validate that the connected account exists and is active
            validated_account = await self.validate_connected_account(connected_account_id, token)
            logger.info(f"Connected account {connected_account_id} validated successfully: {validated_account.get('displayName')}")

            # Note: Multiple active chatbots per account are now supported
            # The workflow event listener determines which chatbot to trigger based on the event payload

            # Validate trigger value if provided
            trigger = None
            if chatbot_data.trigger:
                if chatbot_data.trigger not in ["NEW_ENTITY", "EXISTING_ENTITY"]:
                    raise HTTPException(status_code=400, detail="Trigger must be either 'NEW_ENTITY' or 'EXISTING_ENTITY'")
                trigger = chatbot_data.trigger

            # Process entities field if provided
            entities_json = None
            if chatbot_data.entities:
                # Convert EntityConfig objects to dict for JSON storage
                entities_json = [entity.model_dump() for entity in chatbot_data.entities]
                logger.info(f"Storing entities configuration: {entities_json}")
            else:
                # Validate that at least one entity is configured
                raise EntityConfigurationError(
                    message="At least one entity must be configured for the chatbot",
                    error_code="041126"
                )

            # Create chatbot with account-based approach (no entity type but keep trigger)
            chatbot = Chatbot(
                id=chatbot_id,
                tenant_id=tenant_id,
                name=chatbot_data.name,
                type=chatbot_type,
                description=chatbot_data.description or "",
                welcome_message=chatbot_data.welcomeMessage,
                thank_you_message=chatbot_data.thankYouMessage,
                connected_account_display_name=connected_account_display_name,
                connected_account_id=connected_account_id,
                trigger=trigger,  # Keep trigger field for chatbot behavior
                entities=entities_json,  # Store entities configuration
                created_by=user_id,  # Track who created the chatbot
                updated_by=user_id,  # Initially same as created_by
                status="DRAFT"  # Initial status is DRAFT until fully configured
            )

            # Add chatbot to database
            db.add(chatbot)
            db.commit()
            db.refresh(chatbot)

            logger.info(f"Created chatbot {chatbot_id} for tenant {tenant_id}")

            # Note: No status.updated event is published when chatbot is created
            # Events will only be published when status is explicitly changed via update_chatbot_status

            # Build connected account response (account-based approach)
            connected_account = None
            if (chatbot.connected_account_display_name or chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "accountId": chatbot.connected_account_id
                    # Removed entityType - not needed for account-based chatbots
                }

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "type": chatbot.type,
                "description": chatbot.description,
                "welcomeMessage": chatbot.welcome_message,
                "thankYouMessage": chatbot.thank_you_message,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,  # Keep trigger field in response
                "entities": chatbot.entities,  # Include entities configuration
                "status": chatbot.status,
                "created_at": chatbot.created_at
            }

        except (HTTPException, ChatbotLimitExceededError, EntityConfigurationError):
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error creating chatbot: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error creating chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    async def update_chatbot(self, chatbot_id: str, chatbot_data: ChatbotUpdate, tenant_id: int, user_id: str, token: str, auth_context=None) -> Dict[str, Any]:
        """
        Update an existing chatbot

        Args:
            chatbot_id: The chatbot ID to update
            chatbot_data: ChatbotUpdate model with updated information
            tenant_id: The tenant ID
            user_id: The user ID updating the chatbot
            token: Authorization token for user validation
            auth_context: Authentication context with permissions

        Returns:
            Dict[str, Any]: Updated chatbot information
        """
        db = None
        try:
            db = next(get_db())

            # Validate user permissions for chatbot update
            if auth_context:
                validate_chatbot_permissions(auth_context)

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Validate knowledgebase IDs if provided
            if chatbot_data.knowledgebase_ids is not None:
                for kb_id in chatbot_data.knowledgebase_ids:
                    kb = db.query(Document).filter(
                        Document.id == kb_id,
                        Document.tenant_id == tenant_id
                    ).first()
                    if not kb:
                        raise HTTPException(status_code=404, detail=f"Knowledgebase with ID {kb_id} not found")

            # Update chatbot fields if provided
            if chatbot_data.name is not None:
                chatbot.name = chatbot_data.name

            if chatbot_data.description is not None:
                chatbot.description = chatbot_data.description

            if chatbot_data.welcomeMessage is not None:
                chatbot.welcome_message = chatbot_data.welcomeMessage

            if chatbot_data.thankYouMessage is not None:
                chatbot.thank_you_message = chatbot_data.thankYouMessage

            if chatbot_data.connectedAccount is not None:
                new_account_id = chatbot_data.connectedAccount.accountId

                # Validate that the connected account exists and is active
                validated_account = await self.validate_connected_account(new_account_id, token)
                logger.info(f"Connected account {new_account_id} validated successfully: {validated_account.get('displayName')}")

                # Note: Multiple active chatbots per account are now supported
                # The workflow event listener determines which chatbot to trigger based on the event payload

                chatbot.connected_account_display_name = chatbot_data.connectedAccount.displayName
                chatbot.connected_account_id = new_account_id
                # No longer setting entity_type - not needed for account-based chatbots

            if chatbot_data.trigger is not None:
                if chatbot_data.trigger not in ["NEW_ENTITY", "EXISTING_ENTITY"]:
                    raise HTTPException(status_code=400, detail="Trigger must be either 'NEW_ENTITY' or 'EXISTING_ENTITY'")
                chatbot.trigger = chatbot_data.trigger

            # Update entities if provided
            if chatbot_data.entities is not None:
                # Validate that at least one entity is configured
                if not chatbot_data.entities or len(chatbot_data.entities) == 0:
                    raise EntityConfigurationError(
                        message="At least one entity must be configured for the chatbot",
                        error_code="041126"
                    )
                # Convert EntityConfig objects to dict for JSON storage
                entities_json = [entity.model_dump() for entity in chatbot_data.entities]
                chatbot.entities = entities_json
                logger.info(f"Updated entities configuration: {entities_json}")

            # Update questions if provided
            if chatbot_data.questions is not None:
                # Delete existing questions and their entity fields (handle foreign key constraints)
                # First, get all existing question IDs for this chatbot
                existing_questions = db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot_id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).all()

                existing_question_ids = [q.id for q in existing_questions]

                # Delete entity fields first (to avoid foreign key constraint violation)
                if existing_question_ids:
                    db.query(ChatbotQuestionEntityField).filter(
                        ChatbotQuestionEntityField.question_id.in_(existing_question_ids)
                    ).delete(synchronize_session=False)

                # Now delete the questions
                db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot_id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).delete()

                # Add new questions
                for q_data in chatbot_data.questions:
                    question = ChatbotQuestion(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        tenant_id=tenant_id,
                        question=q_data.question,
                        field_id=q_data.fieldId,
                        display_name=q_data.displayName,
                        entity_type=q_data.entityType,
                        name=q_data.name,
                        standard=q_data.standard
                    )
                    db.add(question)

            # Update knowledgebase associations if provided
            if chatbot_data.knowledgebase_ids is not None:
                # Delete existing associations
                db.query(ChatbotKnowledgebase).filter(
                    ChatbotKnowledgebase.chatbot_id == chatbot_id,
                    ChatbotKnowledgebase.tenant_id == tenant_id
                ).delete()

                # Add new associations
                for kb_id in chatbot_data.knowledgebase_ids:
                    kb_assoc = ChatbotKnowledgebase(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        document_id=kb_id,
                        tenant_id=tenant_id
                    )
                    db.add(kb_assoc)

            # Update nodes and edges if provided (for rule-based chatbots)
            if hasattr(chatbot_data, 'nodes') and chatbot_data.nodes is not None:
                # Delete existing nodes and their entity fields first
                existing_nodes = db.query(ChatbotNode).filter(
                    ChatbotNode.chatbot_id == chatbot_id,
                    ChatbotNode.tenant_id == tenant_id
                ).all()
                
                existing_node_ids = [node.node_id for node in existing_nodes]
                
                # Delete entity fields first (to avoid foreign key constraint violation)
                if existing_node_ids:
                    db.query(ChatbotNodeEntityField).filter(
                        ChatbotNodeEntityField.node_id.in_(existing_node_ids)
                    ).delete(synchronize_session=False)
                
                # Delete existing nodes
                db.query(ChatbotNode).filter(
                    ChatbotNode.chatbot_id == chatbot_id,
                    ChatbotNode.tenant_id == tenant_id
                ).delete()
                
                # Delete existing edges
                db.query(ChatbotEdge).filter(
                    ChatbotEdge.chatbot_id == chatbot_id,
                    ChatbotEdge.tenant_id == tenant_id
                ).delete()
                
                # Get allowed entities for this chatbot
                allowed_entities = self._get_allowed_entities(chatbot_id, tenant_id)
                
                # Add new nodes
                for node_data in chatbot_data.nodes:
                    # Extract variable mappings from the node data
                    variable_mapping = node_data.get('variableMapping', [])
                    
                    # Filter variable mappings to only include allowed entities
                    if allowed_entities is not None and variable_mapping:
                        filtered_variable_mapping = []
                        for vm in variable_mapping:
                            if isinstance(vm, dict):
                                entity_type = vm.get('entityType', '').upper()
                                if entity_type in allowed_entities:
                                    filtered_variable_mapping.append(vm)
                                else:
                                    logger.warning(
                                        f"🚫 ENTITY FILTER - Skipping variable mapping for entity '{entity_type}' "
                                        f"as it's not in chatbot's allowed entities: {allowed_entities}"
                                    )
                        variable_mapping = filtered_variable_mapping
                    
                    # Extract position data
                    position = node_data.get('position', {})
                    position_x = position.get('x', 0.0) if isinstance(position, dict) else 0.0
                    position_y = position.get('y', 0.0) if isinstance(position, dict) else 0.0
                    
                    # Get node data and normalize list node sections
                    node_data_dict = node_data.get('data', {})
                    if node_data.get('type') == 'list' and node_data_dict.get('sections'):
                        for i, section in enumerate(node_data_dict['sections']):
                            if isinstance(section, dict):
                                # Ensure section has id field
                                if 'id' not in section or not section['id']:
                                    section['id'] = f"section-{i}"
                                # Ensure section has position field
                                if 'position' not in section:
                                    section['position'] = i
                    
                    node = ChatbotNode(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        tenant_id=tenant_id,
                        node_id=node_data.get('id'),
                        name=node_data.get('name'),
                        type=node_data.get('type'),
                        position_x=position_x,
                        position_y=position_y,
                        data=node_data_dict,
                        variable_mapping=variable_mapping,
                        is_first_node=node_data.get('isFirstNode', False)
                    )
                    db.add(node)
                    
                    # Add entity fields for this node if they exist
                    entity_fields = node_data.get('entityFields', [])
                    if entity_fields:
                        for ef_data in entity_fields:
                            entity_type = ef_data.get('entityType', '').upper()
                            
                            # Validate entity type against allowed entities
                            if allowed_entities is not None and entity_type not in allowed_entities:
                                logger.warning(
                                    f"🚫 ENTITY FILTER - Skipping entity field for entity '{entity_type}' "
                                    f"as it's not in chatbot's allowed entities: {allowed_entities}"
                                )
                                continue
                            
                            entity_field = ChatbotNodeEntityField(
                                id=str(uuid.uuid4()),
                                node_id=node_data.get('id'),
                                entity_type=ef_data.get('entityType'),
                                field_id=ef_data.get('fieldId'),
                                name=ef_data.get('name'),
                                display_name=ef_data.get('displayName'),
                                standard=ef_data.get('standard', False)
                            )
                            db.add(entity_field)
                
                # Add new edges
                if chatbot_data.edges is not None:
                    for edge_data in chatbot_data.edges:
                        edge = ChatbotEdge(
                            id=str(uuid.uuid4()),
                            chatbot_id=chatbot_id,
                            tenant_id=tenant_id,
                            edge_id=edge_data.get('id'),
                            source_node=edge_data.get('source'),
                            source_handle=edge_data.get('sourceHandle'),
                            target_node=edge_data.get('target'),
                            target_handle=edge_data.get('targetHandle')
                        )
                        db.add(edge)

            # Update the updated_by field to track who made the changes
            chatbot.updated_by = user_id

            # Commit changes
            db.commit()
            db.refresh(chatbot)

            # Fetch questions for response
            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            # Fetch knowledgebase IDs for response
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            logger.info(f"Updated chatbot {chatbot_id} for tenant {tenant_id}")

            # Build connected account response (account-based approach)
            connected_account = None
            if (chatbot.connected_account_display_name or chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "accountId": chatbot.connected_account_id
                    # Removed entityType - not needed for account-based chatbots
                }

            # Format questions with their entity fields
            formatted_questions = []
            for question in questions:
                # Fetch entity fields for this question
                entity_fields = db.query(ChatbotQuestionEntityField).filter(
                    ChatbotQuestionEntityField.question_id == question.id
                ).all()

                formatted_questions.append({
                    "id": question.id,
                    "question": question.question,
                    "entityFields": [{
                        "entityType": ef.entity_type,
                        "fieldId": ef.field_id,
                        "name": ef.name,
                        "displayName": ef.display_name,
                        "standard": ef.standard
                    } for ef in entity_fields]
                })

            # Publish chatbot name updated event if name was changed
            if chatbot_data.name is not None:
                try:
                    chatbot_event_publisher = ChatbotEventPublisher()
                    await chatbot_event_publisher.publish_chatbot_name_updated(
                        chatbot_id=chatbot_id,
                        chatbot_name=chatbot.name,
                        tenant_id=tenant_id
                    )
                except Exception as e:
                    logger.error(f"Failed to publish chatbot name updated event: {str(e)}")
                    # Don't fail the update if event publishing fails

            return {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "description": chatbot.description,
                "welcomeMessage": chatbot.welcome_message,
                "thankYouMessage": chatbot.thank_you_message,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,  # Keep trigger field in response
                "entities": chatbot.entities,  # Include entities configuration
                "questions": formatted_questions,
                "knowledgebase_ids": [kb.document_id for kb in kb_assocs],
                "updated_at": chatbot.updated_at
            }

        except (HTTPException, EntityConfigurationError):
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error updating chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def delete_chatbot(self, chatbot_id: str, tenant_id: int) -> Dict[str, Any]:
        """
        Delete a chatbot and all its associated data

        Args:
            chatbot_id: The chatbot ID to delete
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Deletion confirmation message
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Delete questions and their entity fields (handle foreign key constraints)
            # First, get all existing question IDs for this chatbot
            existing_questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            existing_question_ids = [q.id for q in existing_questions]

            # Delete entity fields first (to avoid foreign key constraint violation)
            if existing_question_ids:
                db.query(ChatbotQuestionEntityField).filter(
                    ChatbotQuestionEntityField.question_id.in_(existing_question_ids)
                ).delete(synchronize_session=False)

            # Now delete the questions
            db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).delete()

            # Delete knowledgebase associations
            db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).delete()

            # Delete chatbot
            db.delete(chatbot)
            db.commit()

            logger.info(f"Deleted chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Chatbot deleted successfully",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error deleting chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error deleting chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    def get_chatbot_with_details(self, chatbot_id: str, tenant_id: int) -> Dict[str, Any]:
        """
        Get a chatbot with all its details including questions and knowledgebase associations

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Chatbot details
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Fetch questions for response
            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            # Fetch knowledgebase IDs for response
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            logger.info(f"Retrieved chatbot {chatbot_id} for tenant {tenant_id}")

            # Build connected account response
            connected_account = None
            if (chatbot.connected_account_display_name or
                chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "accountId": chatbot.connected_account_id
                }

            # Format questions with their entity fields
            formatted_questions = []
            for question in questions:
                # Fetch entity fields for this question
                entity_fields = db.query(ChatbotQuestionEntityField).filter(
                    ChatbotQuestionEntityField.question_id == question.id
                ).all()

                formatted_questions.append({
                    "id": question.id,
                    "question": question.question,
                    "position": question.position,  # Include position
                    "entityFields": [{
                        "entityType": ef.entity_type,
                        "fieldId": ef.field_id,
                        "name": ef.name,
                        "displayName": ef.display_name,
                        "standard": ef.standard
                    } for ef in entity_fields] if entity_fields else None,  # Handle null entity_fields
                    "createdAt": question.created_at.isoformat() if question.created_at else None,
                    "updatedAt": question.updated_at.isoformat() if question.updated_at else None
                })

            # Format knowledgebase documents with enhanced information
            knowledgebase_documents = [{
                "documentId": kb.document_id,
                "documentName": kb.document_name,
                "documentSize": kb.document_size,
                "documentType": kb.document_type
            } for kb in kb_assocs]

            # Initialize response data
            response_data = {
                "id": chatbot.id,
                "tenant_id": chatbot.tenant_id,
                "name": chatbot.name,
                "type": chatbot.type,
                "description": chatbot.description,
                "welcomeMessage": chatbot.welcome_message,
                "thankYouMessage": chatbot.thank_you_message,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,
                "entities": chatbot.entities,  # Include entities configuration
                "status": chatbot.status,
                "questions": formatted_questions,
                "knowledgebase": knowledgebase_documents,  # Enhanced knowledgebase information
                "knowledgebase_ids": [kb.document_id for kb in kb_assocs],  # Keep for backward compatibility
                "created_at": chatbot.created_at,
                "updated_at": chatbot.updated_at
            }

            # Add nodes and edges for rule-based chatbots
            if chatbot.type.upper() == "RULE":
                # Fetch nodes for rule-based chatbot
                nodes = db.query(ChatbotNode).filter(
                    ChatbotNode.chatbot_id == chatbot_id,
                    ChatbotNode.tenant_id == tenant_id
                ).all()

                # Fetch edges for rule-based chatbot
                edges = db.query(ChatbotEdge).filter(
                    ChatbotEdge.chatbot_id == chatbot_id,
                    ChatbotEdge.tenant_id == tenant_id
                ).all()

                # Format nodes with their entity fields
                formatted_nodes = []
                for node in nodes:
                    # Fetch entity fields for this node
                    node_entity_fields = db.query(ChatbotNodeEntityField).filter(
                        ChatbotNodeEntityField.node_id == node.node_id
                    ).all()

                    # Format entity fields
                    formatted_entity_fields = [{
                        "entityType": ef.entity_type,
                        "fieldId": ef.field_id,
                        "name": ef.name,
                        "standard": ef.standard,
                        "displayName": ef.display_name
                    } for ef in node_entity_fields] if node_entity_fields else []

                    # Get node data and add entity fields to it
                    node_data = node.data or {}
                    if formatted_entity_fields:
                        node_data["entityFields"] = formatted_entity_fields
                    
                    # Replace uiIsfirstnode with isFirstNode using the actual database field value
                    if "uiIsfirstnode" in node_data:
                        del node_data["uiIsfirstnode"]  # Remove the UI-prefixed field
                    node_data["isFirstNode"] = node.is_first_node  # Use the actual database field

                    # Add id field to buttons if they exist
                    if node.type == "buttons" and "buttons" in node_data and node_data["buttons"]:
                        for button in node_data["buttons"]:
                            if isinstance(button, dict) and "id" not in button:
                                # Use name as id if it exists, otherwise use text
                                button["id"] = button.get("name") or button.get("text", "")
                    
                    # Safety check: Ensure list node sections have id and position fields (for backward compatibility with old data)
                    if node.type == "list" and "sections" in node_data and node_data["sections"]:
                        for i, section in enumerate(node_data["sections"]):
                            if isinstance(section, dict):
                                # Add missing id field (backward compatibility)
                                if "id" not in section or not section["id"]:
                                    section["id"] = f"section-{i}"
                                    logger.warning(f"List node {node.node_id} section {i} missing id field, generated fallback")
                                # Add missing position field (backward compatibility)
                                if "position" not in section:
                                    section["position"] = i
                                    logger.warning(f"List node {node.node_id} section {i} missing position field, generated fallback")

                    formatted_nodes.append({
                        "id": node.node_id,  # Keep as string to match API contract
                        "name": node.name,
                        "type": node.type,
                        "position": {
                            "x": node.position_x,
                            "y": node.position_y
                        },
                        "data": node_data,
                        "variableMapping": node.variable_mapping or [],
                        "isFirstNode": node.is_first_node  # Add isFirstNode as top-level property
                    })

                # Format edges
                formatted_edges = []
                for edge in edges:
                    formatted_edges.append({
                        "source": edge.source_node,
                        "sourceHandle": edge.source_handle,
                        "target": edge.target_node,
                        "targetHandle": None,  # target_handle is not stored in database
                        "id": edge.edge_id
                    })

                # Add nodes and edges to response
                response_data["nodes"] = formatted_nodes
                response_data["edges"] = formatted_edges

            return response_data

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error getting chatbot: {str(e)}")
        finally:
            if db:
                db.close()

    async def publish_start_conversation_event(
        self,
        *,
        chatbot,
        tenant_id: int,
        conversation_id: str,
        welcome_message: str,
        first_question: Optional[Dict[str, Any]],
        charge: int,
        message_conversation_id: Optional[Any],
        db: Session,
        precomputed_node_details: Optional[Dict[str, Any]] = None,
        entity_details: Optional[List[Dict[str, Any]]] = None,
        auth_token: Optional[str] = None
    ) -> bool:
        """Compose and publish the start-conversation event per contract.

        For AI chatbots: include message (first question) and omit nodeDetails.
        For RULE chatbots: message is null and include nodeDetails of the start node.
        """
        try:
            chatbot_type_contract = "RULE" if (getattr(chatbot, "type", "AI") or "AI").upper() == "RULE" else "AI"

            # Initialize field values for variable substitution
            field_values = {}

            extra_payload: Dict[str, Any] = {
                "chatbotType": chatbot_type_contract,
            }
            
            # Only include welcome message for AI chatbots, not for RULE chatbots
            # Rule-based chatbots get their content from nodes, not from welcome message
            if chatbot_type_contract == "AI":
                extra_payload["welcomeMessage"] = welcome_message

            event_message = (first_question or {}).get("question") if chatbot_type_contract == "AI" else None
            event_charge = 0 if chatbot_type_contract == "RULE" else charge

            # nodeDetails for RULE_BASED
            node_details = precomputed_node_details
            if chatbot_type_contract == "RULE" and node_details is None:
                # Much more efficient: directly query for the first node using stored flag
                start_node = db.query(ChatbotNode).filter(
                    ChatbotNode.chatbot_id == chatbot.id,
                    ChatbotNode.tenant_id == int(tenant_id),
                    ChatbotNode.is_first_node == True
                ).first()
                
                # Fallback: if no first node found, get any node (shouldn't happen with proper data)
                if not start_node:
                    start_node = db.query(ChatbotNode).filter(
                        ChatbotNode.chatbot_id == chatbot.id,
                        ChatbotNode.tenant_id == int(tenant_id)
                    ).first()
                    if start_node:
                        logger.warning(f"🏁 START CONVERSATION FALLBACK - No first node found, using: {start_node.node_id}")
                else:
                    logger.info(f"🏁 START CONVERSATION - Using stored first node: {start_node.node_id}")

                if start_node is not None:
                    # Keep node_id as string to maintain consistency
                    node_id_value: Any = start_node.node_id

                    node_details = {
                        "id": node_id_value,
                        "name": start_node.name,
                        "type": start_node.type,
                        "isFirstNode": True  # This is always the first node in start conversation
                    }
                    logger.info(f"🏁 START NODE DETAILS - Built for start node {node_id_value} with isFirstNode: True")
                    data_obj = start_node.data or {}
                    
                    # Retrieve variable mappings from the start node if entity details are provided
                    entity_field_service = None
                    if entity_details and auth_token and hasattr(start_node, 'variable_mapping') and start_node.variable_mapping:
                        entity_field_service = EntityFieldService()
                        field_values = entity_field_service.get_entity_field_values_from_mappings(
                            entity_details, start_node.variable_mapping, auth_token
                        )
                        logger.info(f"Retrieved field values for start conversation: {field_values}")
                    
                    # Apply variable substitution to node data
                    if field_values:
                        if entity_field_service is None:
                            entity_field_service = EntityFieldService()
                        data_obj = entity_field_service.substitute_node_variables(data_obj, field_values)
                    
                    if start_node.type == "question":
                        # Send a single text field combining question + numbered options
                        base_text = data_obj.get("text") or ""
                        options = data_obj.get("options") or []
                        if options:
                            lines = [base_text] if base_text else []
                            for idx, opt in enumerate(options, start=1):
                                label = opt.get("text") or opt.get("name") or str(idx)
                                lines.append(f"{idx}. {label}")
                            node_details["data"] = {"text": "\n".join(lines)}
                        else:
                            node_details["data"] = {"text": base_text}
                    elif start_node.type == "sendMessage":
                        blocks: list[Dict[str, Any]] = []
                        for opt in (data_obj.get("options") or []):
                            if opt.get("type") == "text":
                                blocks.append({"type": "text", "text": opt.get("text")})
                            elif opt.get("type") == "media":
                                blocks.append({"type": "media", "mediaFile": opt.get("mediaFile")})
                        node_details["data"] = blocks
                    elif start_node.type == "buttons":
                        # Build button data for start conversation
                        logger.info(f"🔧 BUTTON NODE DEBUG - Building button node {start_node.node_id} in start conversation")
                        logger.info(f"🔧 BUTTON NODE DEBUG - data_obj: {data_obj}")

                        header = data_obj.get("header")
                        body = data_obj.get("body") or ""
                        footer = data_obj.get("footer") or ""
                        buttons = data_obj.get("buttons") or []

                        logger.info(f"🔧 BUTTON NODE DEBUG - header: {header}")
                        logger.info(f"🔧 BUTTON NODE DEBUG - body: {body}")
                        logger.info(f"🔧 BUTTON NODE DEBUG - footer: {footer}")
                        logger.info(f"🔧 BUTTON NODE DEBUG - buttons: {buttons}")

                        # Handle header - convert string to object format if needed
                        if isinstance(header, str):
                            header = {
                                "format": "text",
                                "text": header,
                                "mediaFile": None
                            }
                        elif not header:
                            header = {
                                "format": "text",
                                "text": "",
                                "mediaFile": None
                            }
                        elif isinstance(header, dict):
                            # Ensure mediaFile is present (can be None)
                            if "mediaFile" not in header:
                                header["mediaFile"] = None

                        # Format buttons to ensure they have required fields
                        formatted_buttons = []
                        for btn in buttons:
                            button_id = btn.get("id") or btn.get("name") or btn.get("text", "")
                            formatted_btn = {
                                "id": button_id,
                                "name": button_id,
                                "text": btn.get("text", ""),
                                "position": btn.get("position", 0)
                            }
                            formatted_buttons.append(formatted_btn)

                        node_details["data"] = {
                            "header": header,
                            "body": body,
                            "footer": footer,
                            "buttons": formatted_buttons
                        }

                        logger.info(f"🔧 BUTTON NODE DEBUG - Final node_details: {node_details}")
                        logger.info(f"🔧 BUTTON NODE DEBUG - Data field present: {'data' in node_details}")
                        if 'data' in node_details:
                            logger.info(f"🔧 BUTTON NODE DEBUG - Data content: {node_details['data']}")
                    
                    elif start_node.type == "list":
                        # Build list data for WhatsApp list messages
                        header = data_obj.get("header") or ""
                        body = data_obj.get("body") or ""
                        footer = data_obj.get("footer") or ""
                        menu_button = data_obj.get("menuButton") or "View All"
                        sections = data_obj.get("sections") or []

                        # Build sections for WhatsApp list format - preserve all fields
                        list_sections = []
                        for section in sections:
                            section_data = {
                                "title": section.get("title", ""),
                                "rows": section.get("rows", [])
                            }
                            # Preserve id and position if present
                            if "id" in section:
                                section_data["id"] = section["id"]
                            if "position" in section:
                                section_data["position"] = section["position"]
                            
                            if section_data["rows"]:  # Only add section if it has rows
                                list_sections.append(section_data)

                        node_details["data"] = {
                            "header": header,
                            "body": body,
                            "footer": footer,
                            "menuButton": menu_button,
                            "sections": list_sections
                        }

                        logger.info(f"🔧 LIST NODE DEBUG - Final node_details: {node_details}")
                        logger.info(f"🔧 LIST NODE DEBUG - Data field present: {'data' in node_details}")
                        if 'data' in node_details:
                            logger.info(f"🔧 LIST NODE DEBUG - Data content: {node_details['data']}")

            # Apply variable substitution if entity_details and auth_token are provided
            if entity_details and auth_token and node_details:
                entity_field_service = EntityFieldService()
                
                # Get variable mappings from the start node
                variable_mappings = []
                if chatbot_type_contract == "RULE":
                    # Get the start node to access its variable mappings
                    start_node = None
                    if precomputed_node_details:
                        # If we have precomputed node details, we need to get the actual node
                        # to access its variable mappings
                        nodes = db.query(ChatbotNode).filter(
                            ChatbotNode.chatbot_id == chatbot.id,
                            ChatbotNode.tenant_id == int(tenant_id)
                        ).all()
                        edges = db.query(ChatbotEdge).filter(
                            ChatbotEdge.chatbot_id == chatbot.id,
                            ChatbotEdge.tenant_id == int(tenant_id)
                        ).all()
                        target_ids = {e.target_node for e in edges if e.target_node}
                        for n in nodes:
                            if n.node_id not in target_ids:
                                start_node = n
                                break
                        if not start_node and nodes:
                            start_node = nodes[0]
                    else:
                        # Find the start node from the database
                        nodes = db.query(ChatbotNode).filter(
                            ChatbotNode.chatbot_id == chatbot.id,
                            ChatbotNode.tenant_id == int(tenant_id)
                        ).all()
                        edges = db.query(ChatbotEdge).filter(
                            ChatbotEdge.chatbot_id == chatbot.id,
                            ChatbotEdge.tenant_id == int(tenant_id)
                        ).all()
                        target_ids = {e.target_node for e in edges if e.target_node}
                        for n in nodes:
                            if n.node_id not in target_ids:
                                start_node = n
                                break
                        if not start_node and nodes:
                            start_node = nodes[0]
                    
                    if start_node and hasattr(start_node, 'variable_mapping') and start_node.variable_mapping:
                        variable_mappings = start_node.variable_mapping
                
                if variable_mappings:
                    field_values = entity_field_service.get_entity_field_values_from_mappings(
                        entity_details, variable_mappings, auth_token
                    )
                    logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Retrieved field values for start conversation: {field_values}")
                    logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Variable mappings count: {len(variable_mappings)}")
                    logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Variable mappings: {variable_mappings}")
                else:
                    logger.warning(f"🔧 VARIABLE REPLACEMENT DEBUG - No variable mappings found for start conversation")

                # Apply variable substitution to node_details.data only
                if field_values and isinstance(node_details, dict):
                    data_obj = node_details.get("data")
                    if isinstance(data_obj, dict):
                        # For question nodes - data is a dict
                        node_details["data"] = entity_field_service.substitute_node_variables(data_obj, field_values)
                        logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Applied variable substitution to node_details.data (dict)")
                    elif isinstance(data_obj, list):
                        # For sendMessage nodes - data is a list of blocks
                        substituted_blocks = []
                        for block in data_obj:
                            if isinstance(block, dict):
                                substituted_block = entity_field_service.substitute_node_variables(block, field_values)
                                substituted_blocks.append(substituted_block)
                            else:
                                substituted_blocks.append(block)
                        node_details["data"] = substituted_blocks
                        logger.info(f"🔧 VARIABLE REPLACEMENT DEBUG - Applied variable substitution to node_details.data (list)")
                else:
                    logger.warning("🔧 VARIABLE REPLACEMENT DEBUG - No field values found for variable substitution")

            extra_payload["nodeDetails"] = [node_details] if node_details else []

            # Publish via event publisher
            # Handle sendMessage chaining for rule-based chatbots
            # For RULE chatbots, we need to get the start node for chaining logic
            chaining_start_node = None
            if chatbot_type_contract == "RULE":
                # Get the start node for chaining (ensure we have the right one)
                chaining_start_node = db.query(ChatbotNode).filter(
                    ChatbotNode.chatbot_id == chatbot.id,
                    ChatbotNode.tenant_id == int(tenant_id),
                    ChatbotNode.is_first_node == True
                ).first()
                
                # Fallback: if no first node found via flag, find start node by checking edges
                if not chaining_start_node:
                    nodes = db.query(ChatbotNode).filter(
                        ChatbotNode.chatbot_id == chatbot.id,
                        ChatbotNode.tenant_id == int(tenant_id)
                    ).all()
                    edges = db.query(ChatbotEdge).filter(
                        ChatbotEdge.chatbot_id == chatbot.id,
                        ChatbotEdge.tenant_id == int(tenant_id)
                    ).all()
                    target_ids = {e.target_node for e in edges if e.target_node}
                    for n in nodes:
                        if n.node_id not in target_ids:
                            chaining_start_node = n
                            break
                    if not chaining_start_node and nodes:
                        chaining_start_node = nodes[0]
            
            if chatbot_type_contract == "RULE" and chaining_start_node and chaining_start_node.type == "sendMessage":
                logger.info(f"🔗 START CONVERSATION CHAINING - First node {chaining_start_node.node_id} is sendMessage, checking for chaining")
                # Ensure entity_field_service is available for chaining
                if entity_field_service is None:
                    entity_field_service = EntityFieldService()
                chained_nodes, chain_ends_at_final_node = await self._collect_start_conversation_chained_nodes(
                    db, chatbot, tenant_id, chaining_start_node, field_values, entity_field_service, entity_details, auth_token
                )
                
                # Create a single event with all nodes (start + chained)
                all_nodes = []
                if node_details:
                    all_nodes.append(node_details)
                if chained_nodes:
                    all_nodes.extend(chained_nodes)
                
                # Check if the conversation ends here (start node has no outgoing edges OR chain ends at final node)
                start_node_edges = db.query(ChatbotEdge).filter(
                    ChatbotEdge.chatbot_id == chatbot.id,
                    ChatbotEdge.tenant_id == int(tenant_id),
                    ChatbotEdge.source_node == chaining_start_node.node_id
                ).count()
                
                # For sendMessage nodes, check if they appear to be asking for user input
                # If a sendMessage node has no outgoing edges but contains question indicators,
                # it should still wait for user response (publish response event, not completion)
                appears_to_expect_response = False
                if start_node_edges == 0 and all_nodes:
                    # Check the last node in the chain for question indicators
                    last_node = all_nodes[-1]
                    logger.info(f"🔍 QUESTION DETECTION - Checking last node: {last_node.get('type')} with data: {last_node.get('data')}")
                    if last_node.get("type") == "sendMessage":
                        last_node_data = last_node.get("data", [])
                        logger.info(f"🔍 QUESTION DETECTION - Node data type: {type(last_node_data)}, content: {last_node_data}")
                        if isinstance(last_node_data, list):
                            for item in last_node_data:
                                if isinstance(item, dict):
                                    text = item.get("text", "").lower()
                                    logger.info(f"🔍 QUESTION DETECTION - Checking text: '{text}'")
                                    # Look for question indicators
                                    if any(indicator in text for indicator in ["?", "select", "choose", "option", "help you", "can i help"]):
                                        appears_to_expect_response = True
                                        logger.info(f"🤔 QUESTION DETECTED - SendMessage node appears to ask for user input: '{text[:50]}...'")
                                        break
                                    else:
                                        logger.info(f"🔍 QUESTION DETECTION - No question indicators found in: '{text}'")
                
                # Determine if this chatbot has only one node
                total_nodes_count = db.query(ChatbotNode).filter(
                    ChatbotNode.chatbot_id == chatbot.id,
                    ChatbotNode.tenant_id == int(tenant_id)
                ).count()
                is_single_node_chatbot = (total_nodes_count == 1)
                
                # Only end conversation if there are no outgoing edges AND it doesn't appear to expect a response
                # OR if the chain ends at a final node AND it doesn't appear to expect a response
                conversation_ends_here = (start_node_edges == 0 and not appears_to_expect_response) or (chain_ends_at_final_node and not appears_to_expect_response)
                
                # Product rule overrides:
                # 1) If the chatbot has only one node and it's a sendMessage, end immediately
                if is_single_node_chatbot and chaining_start_node and chaining_start_node.type == "sendMessage":
                    conversation_ends_here = True
                    logger.info("✅ RULE OVERRIDE - Single-node sendMessage chatbot: forcing completion")
                
                # 2) If the start node is a question and it has no outgoing edges, end conversation
                if chaining_start_node and chaining_start_node.type == "question" and start_node_edges == 0:
                    conversation_ends_here = True
                    logger.info("✅ RULE OVERRIDE - Question start node without outgoing edges: forcing completion")
                
                # 3) If the start node is buttons/list and has no outgoing edges, end conversation
                if chaining_start_node and chaining_start_node.type in ["buttons", "list"] and start_node_edges == 0:
                    conversation_ends_here = True
                    logger.info(f"✅ RULE OVERRIDE - {chaining_start_node.type.capitalize()} start node without outgoing edges: forcing completion")
                
                logger.info(f"🔍 START CONVERSATION END CHECK - start_node_edges: {start_node_edges}, chain_ends_at_final_node: {chain_ends_at_final_node}, appears_to_expect_response: {appears_to_expect_response}, total_nodes_count: {total_nodes_count}, conversation_ends_here: {conversation_ends_here}")
                
                # Always update conversation state to point to the last node in the chain
                # This ensures proper tracking even for conversations that end immediately
                if all_nodes:
                    last_node_id = all_nodes[-1]["id"]
                    logger.info(f"🔗 START CONVERSATION STATE UPDATE - Setting rule_current_node_id to: {last_node_id}")
                    
                    # Update the conversation state in Redis
                    from app.services.redis_service import RedisService
                    redis_service = RedisService()
                    
                    # Get current state and update it
                    current_state = redis_service.get_conversation_state(conversation_id)
                    if current_state:
                        current_state["rule_current_node_id"] = last_node_id
                        redis_service.store_conversation_state(conversation_id, current_state)
                        logger.info(f"🔗 START CONVERSATION STATE UPDATED - rule_current_node_id set to: {last_node_id}")
                    else:
                        logger.warning(f"🔗 START CONVERSATION STATE NOT FOUND - Could not update rule_current_node_id")
                
                # Decide whether to publish completion or response event
                if conversation_ends_here:
                    # Normalize and enrich entity details to match expected format
                    normalized_entity_details = []
                    if entity_details and auth_token:
                        logger.info(f"📊 START CONVERSATION COMPLETION - Normalizing and enriching {len(entity_details)} entities")
                        if entity_field_service is None:
                            entity_field_service = EntityFieldService()
                        
                        for i, entity in enumerate(entity_details, 1):
                            # Support both old and new formats
                            entity_id = entity.get('id') or entity.get('entityId')
                            entity_type = entity.get('entityType') or entity.get('entity')
                            owner_id = entity.get('ownerId')
                            entity_name = entity.get('entityName') or entity.get('name')
                            
                            # Normalize entity type to uppercase
                            if entity_type:
                                entity_type = entity_type.upper()
                            
                            # If ownerId or entityName is missing, fetch from API
                            if (not owner_id or not entity_name) and entity_id and entity_type:
                                logger.info(f"🔍 FETCHING ENTITY DETAILS - Entity {i}: ID={entity_id}, Type={entity_type}")
                                try:
                                    entity_info = entity_field_service.get_entity_details(str(entity_id), entity_type, auth_token)
                                    fetched_owner_id = entity_info.get("ownerId")
                                    fetched_entity_name = entity_info.get("entityName")
                                    
                                    if fetched_owner_id:
                                        owner_id = fetched_owner_id
                                        logger.info(f"✅ OWNER ID FETCHED - Entity {i}: OwnerId={fetched_owner_id}")
                                    else:
                                        logger.warning(f"⚠️ OWNER ID NOT FOUND - Entity {i}: No ownerId returned from API")
                                    
                                    if fetched_entity_name:
                                        entity_name = fetched_entity_name
                                        logger.info(f"✅ ENTITY NAME FETCHED - Entity {i}: EntityName={fetched_entity_name}")
                                    else:
                                        logger.warning(f"⚠️ ENTITY NAME NOT FOUND - Entity {i}: No entityName returned from API")
                                        
                                except Exception as e:
                                    logger.error(f"❌ ERROR FETCHING ENTITY DETAILS - Entity {i}: {str(e)}")
                            
                            # Always use the new normalized format
                            normalized_entity = {
                                "entityId": int(entity_id) if entity_id else None,
                                "entityType": entity_type,
                                "ownerId": int(owner_id) if owner_id else None,
                                "entityName": entity_name
                            }
                            normalized_entity_details.append(normalized_entity)
                            logger.info(f"   Entity {i}: EntityId={normalized_entity['entityId']}, Type={entity_type}, OwnerId={normalized_entity['ownerId']}, EntityName={normalized_entity['entityName']}")
                    else:
                        # If no auth_token, just convert format without enriching
                        for entity in (entity_details or []):
                            entity_id = entity.get('id') or entity.get('entityId')
                            entity_type = entity.get('entityType') or entity.get('entity')
                            entity_name = entity.get('entityName') or entity.get('name')
                            if entity_type:
                                entity_type = entity_type.upper()
                            normalized_entity_details.append({
                                "entityId": int(entity_id) if entity_id else None,
                                "entityType": entity_type,
                                "ownerId": None,
                                "entityName": entity_name
                            })
                    
                    # Mark conversation as completed in database
                    from app.utils.conversation_state_utils import update_conversation_in_db
                    from app.services.redis_service import RedisService

                    # Get conversation state from Redis
                    redis_service = RedisService()
                    state = redis_service.get_conversation_state(conversation_id)

                    if state:
                        # Mark as completed and ended
                        state["completed"] = True
                        state["ended"] = True
                        redis_service.store_conversation_state(conversation_id, state)

                        # Update database
                        update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)
                        logger.info(f"💾 DATABASE UPDATED - Conversation {conversation_id} marked as completed=True, ended=True (start conversation completion)")
                    else:
                        logger.warning(f"⚠️ Could not retrieve conversation state from Redis for {conversation_id}")

                    # Publish completion event
                    logger.info(f"🏁 START CONVERSATION COMPLETION - Publishing completion event for {len(all_nodes)} nodes")
                    success = await conversation_event_publisher.publish_conversation_completion(
                        chatbot_conversation_id=conversation_id,
                        completion_message=None,  # For rule-based chatbots, message should be null
                        charge=event_charge,
                        chatbot_type=chatbot_type_contract,
                        tenant_id=int(tenant_id) if tenant_id else None,
                        entity_details=normalized_entity_details,  # Use normalized format
                        node_details=all_nodes,
                        message_conversation_id=message_conversation_id,
                        chatbot_id=chatbot.id,
                        chatbot_name=chatbot.name
                    )
                    logger.info(f"✅ START CONVERSATION COMPLETION PUBLISHED - For {len(all_nodes)} nodes")
                else:
                    # Publish response event (conversation continues)
                    success = await conversation_event_publisher.publish_conversation_response(
                        chatbot_conversation_id=conversation_id,
                        message=None,  # Use nodeDetails array instead of message
                        completed=False,
                        charge=event_charge,
                        message_conversation_id=message_conversation_id,
                        chatbot_id=chatbot.id,
                        chatbot_name=chatbot.name,
                        extra={
                            "chatbotType": chatbot_type_contract,
                            "nodeDetails": all_nodes  # Array of all nodes
                        }
                    )
                    logger.info(f"🔗 START BATCH PUBLISHING - Published {len(all_nodes)} nodes in single event")
            else:
                # For non-sendMessage nodes (buttons, list, question, message), check if single-node with no outgoing edges
                if chatbot_type_contract == "RULE":
                    # Get the start node
                    start_node = db.query(ChatbotNode).filter(
                        ChatbotNode.chatbot_id == chatbot.id,
                        ChatbotNode.tenant_id == int(tenant_id),
                        ChatbotNode.is_first_node == True
                    ).first()
                    
                    # Check if this is a single-node chatbot with no outgoing edges
                    total_nodes_count = db.query(ChatbotNode).filter(
                        ChatbotNode.chatbot_id == chatbot.id,
                        ChatbotNode.tenant_id == int(tenant_id)
                    ).count()
                    
                    start_node_edges = 0
                    if start_node:
                        start_node_edges = db.query(ChatbotEdge).filter(
                            ChatbotEdge.chatbot_id == chatbot.id,
                            ChatbotEdge.tenant_id == int(tenant_id),
                            ChatbotEdge.source_node == start_node.node_id
                        ).count()
                    
                    is_single_node_chatbot = (total_nodes_count == 1)
                    node_type = start_node.type if start_node else None
                    
                    # Check if this should complete immediately
                    should_complete_immediately = (
                        (is_single_node_chatbot and node_type in ["buttons", "list", "question", "sendMessage"]) or
                        (start_node_edges == 0 and node_type in ["buttons", "list", "question"])
                    )
                    
                    logger.info(f"🔍 START CONVERSATION SINGLE NODE CHECK - Node type: {node_type}, Total nodes: {total_nodes_count}, Outgoing edges: {start_node_edges}, Should complete: {should_complete_immediately}")
                    
                    if should_complete_immediately:
                        # Normalize and enrich entity details to match expected format
                        normalized_entity_details = []
                        if entity_details and auth_token:
                            logger.info(f"📊 START CONVERSATION COMPLETION (NON-CHAIN) - Normalizing and enriching {len(entity_details)} entities")
                            if entity_field_service is None:
                                entity_field_service = EntityFieldService()
                            
                            for i, entity in enumerate(entity_details, 1):
                                # Support both old and new formats
                                entity_id = entity.get('id') or entity.get('entityId')
                                entity_type = entity.get('entityType') or entity.get('entity')
                                owner_id = entity.get('ownerId')
                                entity_name = entity.get('entityName') or entity.get('name')
                                
                                # Normalize entity type to uppercase
                                if entity_type:
                                    entity_type = entity_type.upper()
                                
                                # If ownerId or entityName is missing, fetch from API
                                if (not owner_id or not entity_name) and entity_id and entity_type:
                                    logger.info(f"🔍 FETCHING ENTITY DETAILS - Entity {i}: ID={entity_id}, Type={entity_type}")
                                    try:
                                        entity_info = entity_field_service.get_entity_details(str(entity_id), entity_type, auth_token)
                                        fetched_owner_id = entity_info.get("ownerId")
                                        fetched_entity_name = entity_info.get("entityName")
                                        
                                        if fetched_owner_id:
                                            owner_id = fetched_owner_id
                                            logger.info(f"✅ OWNER ID FETCHED - Entity {i}: OwnerId={fetched_owner_id}")
                                        else:
                                            logger.warning(f"⚠️ OWNER ID NOT FOUND - Entity {i}: No ownerId returned from API")
                                        
                                        if fetched_entity_name:
                                            entity_name = fetched_entity_name
                                            logger.info(f"✅ ENTITY NAME FETCHED - Entity {i}: EntityName={fetched_entity_name}")
                                        else:
                                            logger.warning(f"⚠️ ENTITY NAME NOT FOUND - Entity {i}: No entityName returned from API")
                                            
                                    except Exception as e:
                                        logger.error(f"❌ ERROR FETCHING ENTITY DETAILS - Entity {i}: {str(e)}")
                                
                                # Always use the new normalized format
                                normalized_entity = {
                                    "entityId": int(entity_id) if entity_id else None,
                                    "entityType": entity_type,
                                    "ownerId": int(owner_id) if owner_id else None,
                                    "entityName": entity_name
                                }
                                normalized_entity_details.append(normalized_entity)
                                logger.info(f"   Entity {i}: EntityId={normalized_entity['entityId']}, Type={entity_type}, OwnerId={normalized_entity['ownerId']}, EntityName={normalized_entity['entityName']}")
                        else:
                            # If no auth_token, just convert format without enriching
                            for entity in (entity_details or []):
                                entity_id = entity.get('id') or entity.get('entityId')
                                entity_type = entity.get('entityType') or entity.get('entity')
                                entity_name = entity.get('entityName') or entity.get('name')
                                if entity_type:
                                    entity_type = entity_type.upper()
                                normalized_entity_details.append({
                                    "entityId": int(entity_id) if entity_id else None,
                                    "entityType": entity_type,
                                    "ownerId": None,
                                    "entityName": entity_name
                                })
                        
                        # Mark conversation as completed in database
                        from app.utils.conversation_state_utils import update_conversation_in_db
                        from app.services.redis_service import RedisService

                        # Get conversation state from Redis
                        redis_service = RedisService()
                        state = redis_service.get_conversation_state(conversation_id)

                        if state:
                            # Mark as completed and ended
                            state["completed"] = True
                            state["ended"] = True
                            redis_service.store_conversation_state(conversation_id, state)

                            # Update database
                            update_conversation_in_db(db, conversation_id, state, completed=True, ended=True)
                            logger.info(f"💾 DATABASE UPDATED - Conversation {conversation_id} marked as completed=True, ended=True (single-node non-chain completion)")
                        else:
                            logger.warning(f"⚠️ Could not retrieve conversation state from Redis for {conversation_id}")

                        # Publish completion event
                        logger.info(f"🏁 START CONVERSATION SINGLE NODE COMPLETION - Publishing completion event for node type: {node_type}")
                        success = await conversation_event_publisher.publish_conversation_completion(
                            chatbot_conversation_id=conversation_id,
                            completion_message=None,  # For rule-based chatbots, message should be null
                            charge=event_charge,
                            chatbot_type=chatbot_type_contract,
                            tenant_id=int(tenant_id) if tenant_id else None,
                            entity_details=normalized_entity_details,  # Use normalized format
                            node_details=extra_payload.get("nodeDetails", []),  # Use the node details from extra_payload
                            message_conversation_id=message_conversation_id,
                            chatbot_id=chatbot.id,
                            chatbot_name=chatbot.name
                        )
                        logger.info(f"✅ START CONVERSATION SINGLE NODE COMPLETION PUBLISHED - Node type: {node_type}")
                        return success
                
                # For non-single-node or nodes with outgoing edges, publish normally
                success = await conversation_event_publisher.publish_conversation_response(
                    chatbot_conversation_id=conversation_id,
                    message=event_message,
                    completed=False,
                    charge=event_charge,
                    message_conversation_id=message_conversation_id,
                    chatbot_id=chatbot.id,
                    chatbot_name=chatbot.name,
                    extra=extra_payload
                )
            
            return success
        except Exception as e:
            logger.exception("Failed to publish start conversation event")
            return False

    async def _collect_start_conversation_chained_nodes(
        self,
        db: Session,
        chatbot,
        tenant_id: int,
        current_node,
        field_values: Dict[str, Any],
        entity_field_service: EntityFieldService,
        entity_details: List[Dict[str, Any]],
        auth_token: str
    ) -> tuple[List[Dict[str, Any]], bool]:
        """
        Collect all chained nodes for start conversation that should be sent together.
        
        Args:
            db: Database session
            chatbot: Chatbot object
            tenant_id: Tenant ID
            current_node: The current node to start chaining from
            field_values: Field values for variable substitution
            entity_field_service: Entity field service instance
            entity_details: List of entity details from conversation
            auth_token: Authorization token
            
        Returns:
            tuple[List[Dict[str, Any]], bool]: (List of nodeDetails for all chained nodes, whether chain ends at final node)
        """
        chained_nodes = []
        current = current_node
        visited_nodes = set()  # Track visited nodes to detect loops
        max_chain_length = 10  # Maximum allowed chain length to prevent infinite loops
        chain_ends_at_final_node = False  # Track if chain ends with no outgoing edges
        
        try:
            chain_length = 0
            while current and chain_length < max_chain_length:
                chain_length += 1
                current_node_id = current.node_id
                
                logger.info(f"🔗 START COLLECTING CHAIN - Processing node {current_node_id} (chain length: {chain_length})")
                
                # Check for loop detection
                if current_node_id in visited_nodes:
                    logger.error(f"🔄 START LOOP DETECTED - Node {current_node_id} already visited in this chain! Breaking to prevent infinite loop.")
                    logger.error(f"🔄 START LOOP DETECTED - Visited nodes: {visited_nodes}")
                    logger.error(f"🔄 START LOOP DETECTED - Current chain: {[node['id'] for node in chained_nodes]}")
                    break
                
                # Add current node to visited set
                visited_nodes.add(current_node_id)
                
                # Get outgoing edges from the current node
                outgoing_edges = db.query(ChatbotEdge).filter(
                    ChatbotEdge.chatbot_id == chatbot.id,
                    ChatbotEdge.tenant_id == tenant_id,
                    ChatbotEdge.source_node == current.node_id
                ).all()
                
                if not outgoing_edges:
                    logger.info(f"🔗 START COLLECTING CHAIN - No outgoing edges from {current_node_id}, chain ends")
                    chain_ends_at_final_node = True
                    break
                
                # For chaining, we'll take the first outgoing edge (no user input needed)
                next_edge = outgoing_edges[0]
                next_node_id = next_edge.target_node
                
                logger.info(f"🔗 START COLLECTING CHAIN - Found next edge to node {next_node_id}")
                
                # Load the next node
                next_node = db.query(ChatbotNode).filter(
                    ChatbotNode.chatbot_id == chatbot.id,
                    ChatbotNode.tenant_id == tenant_id,
                    ChatbotNode.node_id == next_node_id
                ).first()
                
                if not next_node:
                    logger.warning(f"🔗 START COLLECTING CHAIN - Next node {next_node_id} not found")
                    break
                
                # Check if the next node can be chained
                if next_node.type not in ["sendMessage", "question", "buttons", "list"]:
                    logger.info(f"🔗 START COLLECTING CHAIN - Next node {next_node_id} is {next_node.type}, cannot chain")
                    break
                
                # Build nodeDetails for the chained node
                node_details = self._build_start_conversation_node_details(
                    next_node, field_values, entity_field_service, entity_details, auth_token
                )
                if node_details:
                    chained_nodes.append(node_details)
                    logger.info(f"🔗 START COLLECTING CHAIN - Added node {next_node.node_id} to chain")
                
                # Check if we should stop chaining at question, buttons, or list nodes
                if next_node.type in ["question", "buttons", "list"]:
                    # Check if this node has outgoing edges
                    final_node_edges = db.query(ChatbotEdge).filter(
                        ChatbotEdge.chatbot_id == chatbot.id,
                        ChatbotEdge.tenant_id == tenant_id,
                        ChatbotEdge.source_node == next_node_id
                    ).all()
                    
                    logger.info(f"🔍 START CHAIN DEBUG - Node {next_node_id} ({next_node.type}) has {len(final_node_edges)} outgoing edges")
                    
                    # Special handling for question nodes
                    should_break = True
                    if next_node.type == "question":
                        node_data = getattr(next_node, 'data', None) or {}
                        options = node_data.get('options') or []
                        logger.info(f"🔍 START CHAIN DEBUG - Question node {next_node_id} has {len(options)} options")
                        
                        # Question nodes ALWAYS require user input (either selecting option or typing free text)
                        # So we always stop the chain at question nodes (should_break = True)
                        if not options:
                            # Free-text question (no options)
                            if not final_node_edges:
                                # No options AND no edges → will complete after user provides answer
                                logger.info(f"🏁 START CHAIN COMPLETION - Question node {next_node_id} has NO OPTIONS and NO EDGES → Will complete after user provides free-text answer")
                                chain_ends_at_final_node = True
                            else:
                                # No options BUT has edges → will proceed after user provides free-text answer
                                logger.info(f"🔗 START CHAIN STOP - Question node {next_node_id} has NO OPTIONS but HAS {len(final_node_edges)} edges → Waiting for user's free-text answer before proceeding")
                        else:
                            # Multiple-choice question (has options)
                            if not final_node_edges:
                                logger.info(f"🏁 START CHAIN COMPLETION - Question node {next_node_id} has {len(options)} options but NO EDGES → Will complete after user selects option")
                                chain_ends_at_final_node = True
                            else:
                                logger.info(f"🔗 START CHAIN STOP - Question node {next_node_id} has {len(options)} options → Waiting for user to select option")
                        # Always stop chain at question nodes (user input required)
                        should_break = True
                    else:
                        # Buttons or list nodes always require user interaction
                        if not final_node_edges:
                            logger.info(f"🏁 START CHAIN COMPLETION - {next_node.type.title()} node {next_node_id} has NO EDGES → Will complete after user interaction")
                            chain_ends_at_final_node = True
                        else:
                            logger.info(f"🔗 START CHAIN STOP - {next_node.type.title()} node {next_node_id} requires user interaction → Stopping chain")
                        should_break = True
                    
                    if should_break:
                        break
                
                # Move to next node for further chaining (only for sendMessage nodes)
                current = next_node
            
            # Check if we hit the maximum chain length
            if chain_length >= max_chain_length:
                logger.error(f"🔄 START MAXIMUM CHAIN LENGTH REACHED - Chain length {chain_length} exceeds maximum {max_chain_length}. Breaking to prevent infinite loop.")
                logger.error(f"🔄 START MAXIMUM CHAIN LENGTH - Visited nodes: {visited_nodes}")
                logger.error(f"🔄 START MAXIMUM CHAIN LENGTH - Current chain: {[node['id'] for node in chained_nodes]}")
                    
        except Exception as e:
            logger.error(f"Error collecting start conversation chained nodes: {str(e)}")
            # Don't raise the exception to avoid breaking the main conversation flow
            logger.warning("Continuing conversation flow despite chaining error")
        
        logger.info(f"🔗 START COLLECTING CHAIN - Collected {len(chained_nodes)} chained nodes, chain_ends_at_final_node: {chain_ends_at_final_node}")
        return chained_nodes, chain_ends_at_final_node
    
    def _build_start_conversation_node_details(
        self, 
        node, 
        field_values: Dict[str, Any], 
        entity_field_service: EntityFieldService,
        entity_details: List[Dict[str, Any]],
        auth_token: str
    ) -> Dict[str, Any]:
        """
        Build nodeDetails for a given node in start conversation with variable substitution.
        
        Args:
            node: The node to build details for
            field_values: Field values for variable substitution
            entity_field_service: Entity field service instance
            entity_details: List of entity details from conversation
            auth_token: Authorization token
            
        Returns:
            Dict[str, Any]: NodeDetails dictionary
        """
        try:
            # Use stored isFirstNode value from database
            is_first_node = getattr(node, 'is_first_node', False)
            
            node_details = {
                "id": node.node_id,
                "name": getattr(node, "name", None),
                "type": node.type,
                "isFirstNode": is_first_node
            }
            
            data_obj = (getattr(node, "data", None) or {})
            
            # Apply variable substitution to node data
            if field_values:
                data_obj = entity_field_service.substitute_node_variables(data_obj, field_values)
            else:
                # If no field_values from start node, try to get them from this node's variable mappings
                variable_mappings = getattr(node, 'variable_mapping', None)
                if variable_mappings:
                    field_values = entity_field_service.get_entity_field_values_from_mappings(
                        entity_details, variable_mappings, auth_token
                    )
                    if field_values:
                        data_obj = entity_field_service.substitute_node_variables(data_obj, field_values)
            
            if node.type == "sendMessage":
                # Build blocks for sendMessage nodes
                blocks = []
                for opt in (data_obj.get("options") or []):
                    if opt.get("type") == "text":
                        blocks.append({"type": "text", "text": opt.get("text")})
                    elif opt.get("type") == "media":
                        blocks.append({"type": "media", "mediaFile": opt.get("mediaFile")})
                node_details["data"] = blocks
                
            elif node.type == "question":
                # Build question data
                question_text = data_obj.get("text") or ""
                options = data_obj.get("options") or []
                
                if options:
                    lines = [question_text] if question_text else []
                    for idx, opt in enumerate(options, start=1):
                        label = opt.get("text") or opt.get("name") or str(idx)
                        lines.append(f"{idx}. {label}")
                    message_text = "\n".join(lines)
                else:
                    message_text = question_text
                
                node_details["data"] = {
                    "text": message_text,
                    "options": options
                }
            
            elif node.type == "list":
                # Build list data for WhatsApp list messages
                header = data_obj.get("header") or ""
                body = data_obj.get("body") or ""
                footer = data_obj.get("footer") or ""
                menu_button = data_obj.get("menuButton") or "View All"
                sections = data_obj.get("sections") or []

                # Build sections for WhatsApp list format - preserve all fields
                list_sections = []
                for section in sections:
                    section_data = {
                        "title": section.get("title", ""),
                        "rows": section.get("rows", [])
                    }
                    # Preserve id and position if present
                    if "id" in section:
                        section_data["id"] = section["id"]
                    if "position" in section:
                        section_data["position"] = section["position"]
                    
                    if section_data["rows"]:  # Only add section if it has rows
                        list_sections.append(section_data)

                node_details["data"] = {
                    "header": header,
                    "body": body,
                    "footer": footer,
                    "menuButton": menu_button,
                    "sections": list_sections
                }

            elif node.type == "buttons":
                # Build button data
                logger.info(f"🔧 BUTTON NODE DEBUG - Building button node {node.node_id}")
                logger.info(f"🔧 BUTTON NODE DEBUG - data_obj: {data_obj}")

                header = data_obj.get("header")
                body = data_obj.get("body") or ""
                footer = data_obj.get("footer") or ""
                buttons = data_obj.get("buttons") or []

                logger.info(f"🔧 BUTTON NODE DEBUG - header: {header}")
                logger.info(f"🔧 BUTTON NODE DEBUG - body: {body}")
                logger.info(f"🔧 BUTTON NODE DEBUG - footer: {footer}")
                logger.info(f"🔧 BUTTON NODE DEBUG - buttons: {buttons}")

                # Handle header - convert string to object format if needed
                if isinstance(header, str):
                    header = {
                        "format": "text",
                        "text": header,
                        "mediaFile": None
                    }
                elif not header:
                    header = {
                        "format": "text",
                        "text": "",
                        "mediaFile": None
                    }
                elif isinstance(header, dict):
                    # Ensure mediaFile is present (can be None)
                    if "mediaFile" not in header:
                        header["mediaFile"] = None

                # Format buttons to ensure they have required fields
                formatted_buttons = []
                for btn in buttons:
                    button_id = btn.get("id") or btn.get("name") or btn.get("text", "")
                    formatted_btn = {
                        "id": button_id,
                        "name": button_id,
                        "text": btn.get("text", ""),
                        "position": btn.get("position", 0)
                    }
                    formatted_buttons.append(formatted_btn)

                node_details["data"] = {
                    "header": header,
                    "body": body,
                    "footer": footer,
                    "buttons": formatted_buttons
                }

                logger.info(f"🔧 BUTTON NODE DEBUG - Final node_details: {node_details}")
                logger.info(f"🔧 BUTTON NODE DEBUG - Data field present: {'data' in node_details}")
                if 'data' in node_details:
                    logger.info(f"🔧 BUTTON NODE DEBUG - Data content: {node_details['data']}")

            logger.info(f"🔗 START BUILDING NODE DETAILS - Built for node {node.node_id}")
            return node_details
            
        except Exception as e:
            logger.error(f"Error building nodeDetails for start conversation node {node.node_id}: {str(e)}")
            return None
    
    async def _publish_start_conversation_chained_nodes(
        self,
        conversation_id: str,
        chained_nodes: List[Dict[str, Any]],
        chatbot
    ):
        """
        Publish all chained nodes for start conversation in a single event with nodeDetails array.
        
        Args:
            conversation_id: Conversation ID
            chained_nodes: List of nodeDetails for chained nodes
        """
        try:
            if not chained_nodes:
                logger.info("🔗 START PUBLISHING CHAIN - No chained nodes to publish")
                return
            
            # Publish all chained nodes in a single event
            await conversation_event_publisher.publish_conversation_response(
                chatbot_conversation_id=conversation_id,
                message=None,  # Chained nodes use nodeDetails array, not message
                completed=False,
                charge=0,
                message_conversation_id=None,  # This is start conversation, no message conversation ID
                chatbot_id=chatbot.id,
                chatbot_name=chatbot.name,
                extra={
                    "chatbotType": "RULE",
                    "nodeDetails": chained_nodes  # Array of nodeDetails
                }
            )
            
            logger.info(f"🔗 START PUBLISHING CHAIN - Published {len(chained_nodes)} chained nodes in single event")
            
        except Exception as e:
            logger.error(f"Error publishing start conversation chained nodes: {str(e)}")
            # Don't raise the exception to avoid breaking the main conversation flow
            logger.warning("Continuing conversation flow despite chaining error")

    def delete_question(self, chatbot_id: str, question_id: str, tenant_id: int) -> Dict[str, Any]:
        """
        Delete a specific question from a chatbot

        Args:
            chatbot_id: The chatbot ID
            question_id: The question ID to delete
            tenant_id: The tenant ID (integer)

        Returns:
            Dict[str, Any]: Deletion confirmation message
        """
        db = None
        try:
            db = next(get_db())

            # Check if question exists
            question = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.id == question_id,
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).first()

            if not question:
                raise HTTPException(status_code=404, detail="Question not found")

            # Delete entity fields first (to avoid foreign key constraint violation)
            db.query(ChatbotQuestionEntityField).filter(
                ChatbotQuestionEntityField.question_id == question_id
            ).delete()

            # Now delete the question
            db.delete(question)
            db.commit()

            logger.info(f"Deleted question {question_id} from chatbot {chatbot_id} for tenant {tenant_id}")

            return {
                "message": "Question deleted successfully",
                "question_id": question_id,
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error deleting question {question_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error deleting question: {str(e)}")
        finally:
            if db:
                db.close()

    def configure_chatbot_questions(self, chatbot_id: str, questions: List[QuestionCreate], tenant_id: int, user_id: str, token: str) -> Dict[str, Any]:
        """
        Configure questions for a chatbot (replace all existing questions)

        Args:
            chatbot_id: The chatbot ID
            questions: List of QuestionCreate objects (maximum 6 allowed)
            tenant_id: The tenant ID
            user_id: The user ID configuring the questions
            token: Authorization token for user validation

        Returns:
            Dict[str, Any]: Configuration result with questions
        """
        db = None
        try:
            # Import the custom exception
            from app.exceptions import ChatbotQuestionValidationError, ValidationError
            
            # Validate maximum 6 total configurations (5 predefined questions + 1 field config for user questions)
            if len(questions) > 6:
                raise ChatbotQuestionValidationError(
                    validation_type="total_configurations",
                    provided_count=len(questions),
                    max_allowed=6,
                    message=f"Maximum 6 configurations allowed (5 predefined questions + 1 user questions field). You provided {len(questions)} configurations."
                )

            # Validate that only positions 1-5 have actual questions, position 6 is field config only
            predefined_questions = [q for q in questions if q.position <= 5]
            position_6_configs = [q for q in questions if q.position == 6]

            if len(predefined_questions) > 5:
                raise ChatbotQuestionValidationError(
                    validation_type="predefined_questions",
                    provided_count=len(predefined_questions),
                    max_allowed=5,
                    message=f"Maximum 5 predefined questions allowed (positions 1-5). You provided {len(predefined_questions)} predefined questions."
                )

            if len(position_6_configs) > 1:
                raise ChatbotQuestionValidationError(
                    validation_type="position_6_configs",
                    provided_count=len(position_6_configs),
                    max_allowed=1,
                    message="Only one position 6 configuration allowed for user questions field."
                )

            # Validate position uniqueness and range
            positions = [q.position for q in questions]
            if len(set(positions)) != len(positions):
                raise ValidationError(
                    field="positions",
                    value=positions,
                    message="Question positions must be unique. Duplicate positions found.",
                    error_code="041084"
                )

            for position in positions:
                if position < 1 or position > 6:
                    raise ValidationError(
                        field="position",
                        value=position,
                        message=f"Question position must be between 1 and 6. Found position: {position}",
                        error_code="041085"
                    )

            # Validate entity field types (TEXT_FIELD or PARAGRAPH_TEXT only)
            logger.info("Validating entity field types for questions")
            self._validate_entity_field_types(questions, token)

            db = next(get_db())

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Get allowed entities for this chatbot
            allowed_entities = self._get_allowed_entities(chatbot_id, tenant_id)

            # Delete existing questions and their entity fields (handle foreign key constraints)
            # First, get all existing question IDs for this chatbot
            existing_questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).all()

            existing_question_ids = [q.id for q in existing_questions]

            # Delete entity fields first (to avoid foreign key constraint violation)
            if existing_question_ids:
                db.query(ChatbotQuestionEntityField).filter(
                    ChatbotQuestionEntityField.question_id.in_(existing_question_ids)
                ).delete(synchronize_session=False)

            # Now delete the questions
            db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).delete()

            # Add new questions
            created_questions = []
            for q_data in questions:
                # Create the main question record
                question = ChatbotQuestion(
                    id=str(uuid.uuid4()),
                    chatbot_id=chatbot_id,
                    tenant_id=tenant_id,
                    question=q_data.question,
                    position=q_data.position
                )
                db.add(question)
                db.flush()  # Flush to get the question ID

                # Handle both new and legacy formats
                if hasattr(q_data, 'entityFields') and q_data.entityFields:
                    # New format with multiple entity fields
                    for entity_field in q_data.entityFields:
                        # Handle nullable fields in EntityFieldMapping
                        entity_type = entity_field.entityType or "unknown"
                        field_id = entity_field.fieldId or 0
                        name = entity_field.name or "unknown_field"
                        display_name = entity_field.displayName or "Unknown Field"
                        standard = entity_field.standard if entity_field.standard is not None else False

                        # Skip entity fields with critical null values
                        if not entity_field.entityType or not entity_field.name:
                            logger.warning(f"Skipping entity field with null entityType or name: {entity_field}")
                            continue

                        # Validate entity type against allowed entities
                        if allowed_entities is not None:
                            # If chatbot has entity restrictions, validate against them
                            if entity_type.upper() not in allowed_entities:
                                logger.warning(
                                    f"🚫 ENTITY FILTER - Skipping entity field for entity '{entity_type}' "
                                    f"as it's not in chatbot's allowed entities: {allowed_entities}"
                                )
                                continue

                        # Normalize entity type to lowercase for consistency
                        entity_type = entity_type.lower()

                        entity_field_record = ChatbotQuestionEntityField(
                            id=str(uuid.uuid4()),
                            question_id=question.id,
                            entity_type=entity_type,
                            field_id=field_id,
                            name=name,
                            display_name=display_name,
                            standard=standard
                        )
                        db.add(entity_field_record)
                else:
                    # Legacy format - convert to new structure
                    # Validate entity type against allowed entities
                    if allowed_entities is not None and hasattr(q_data, 'entityType') and q_data.entityType:
                        # If chatbot has entity restrictions, validate against them
                        if q_data.entityType.upper() not in allowed_entities:
                            logger.warning(
                                f"🚫 ENTITY FILTER - Skipping entity field for entity '{q_data.entityType}' "
                                f"as it's not in chatbot's allowed entities: {allowed_entities}"
                            )
                            # Skip adding this entity field
                            created_questions.append(question)
                            continue

                    if hasattr(q_data, 'entityType') and q_data.entityType:
                        entity_field_record = ChatbotQuestionEntityField(
                            id=str(uuid.uuid4()),
                            question_id=question.id,
                            entity_type=q_data.entityType,
                            field_id=q_data.fieldId,
                            name=q_data.name,
                            display_name=q_data.displayName,
                            standard=q_data.standard
                        )
                        db.add(entity_field_record)

                created_questions.append(question)

            # Note: Chatbot status should only be changed via explicit activate/deactivate API calls
            # Removed automatic activation when questions are added

            db.commit()

            # Refresh to get the created questions with their IDs and load entity fields
            for question in created_questions:
                db.refresh(question)

            logger.info(f"Configured {len(questions)} questions for chatbot {chatbot_id} for tenant {tenant_id}")

            # Format questions with their entity fields
            formatted_questions = []
            for question in created_questions:
                entity_fields = db.query(ChatbotQuestionEntityField).filter(
                    ChatbotQuestionEntityField.question_id == question.id
                ).all()

                formatted_questions.append({
                    "id": question.id,
                    "question": question.question,
                    "position": question.position,
                    "entityFields": [{
                        "entityType": ef.entity_type,
                        "fieldId": ef.field_id,
                        "name": ef.name,
                        "displayName": ef.display_name,
                        "standard": ef.standard
                    } for ef in entity_fields] if entity_fields else None,
                    "createdAt": question.created_at.isoformat() if question.created_at else None,
                    "updatedAt": question.updated_at.isoformat() if question.updated_at else None
                })

            return {
                "message": "Questions configured successfully",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id,
                "questions": formatted_questions,
                "status": chatbot.status
            }

        except (HTTPException, ChatbotQuestionValidationError, ValidationError):
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error configuring questions for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error configuring questions: {str(e)}")
        finally:
            if db:
                db.close()

    async def update_chatbot_status(
        self,
        chatbot_id: str,
        new_status: str,
        tenant_id: int,
        user_id: str,
        token: str
    ) -> Dict[str, Any]:
        """
        Update chatbot status and publish event

        Args:
            chatbot_id: The chatbot ID
            new_status: The new status (DRAFT, ACTIVE, INACTIVE)
            tenant_id: The tenant ID
            user_id: The user ID updating the status
            token: Authorization token for user validation

        Returns:
            Dict[str, Any]: Updated chatbot information
        """
        db = None
        try:
            db = next(get_db())

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Store old status for comparison
            old_status = chatbot.status

            # Note: Multiple active chatbots per account are now allowed
            # The workflow event listener determines which chatbot to trigger based on the event payload

            # Update the status
            chatbot.status = new_status
            chatbot.updated_by = user_id

            # Commit the changes
            db.commit()
            db.refresh(chatbot)

            logger.info(f"Updated chatbot {chatbot_id} status from {old_status} to {new_status}")

            # Publish status updated event based on new logic
            if chatbot.connected_account_id and chatbot.connected_account_display_name:
                try:
                    event_publisher = ChatbotEventPublisher()

                    # Ensure exchange exists
                    await event_publisher.ensure_exchange_exists()

                    # Determine if we should publish the event
                    should_publish = False

                    if new_status == "ACTIVE":
                        # Always publish for activation
                        should_publish = True
                        logger.info(f"Publishing activation event for chatbot {chatbot_id}")
                    elif new_status == "INACTIVE":
                        # Check if this is the last active chatbot for the account
                        is_last_active = self._is_last_active_chatbot_for_account(
                            db, chatbot.connected_account_id, tenant_id, chatbot_id
                        )

                        # Only publish event if this is the last active chatbot for the account
                        if is_last_active:
                            should_publish = True
                            logger.info(f"Publishing deactivation event for chatbot {chatbot_id} (last active chatbot for account)")
                            await event_publisher.publish_chatbot_deactivated_event(
                                chatbot_id=chatbot_id,
                                chatbot_name=chatbot.name,
                                connected_account_id=chatbot.connected_account_id,
                                connected_account_name=chatbot.connected_account_display_name,
                                tenant_id=tenant_id,
                                is_last_active=True
                            )
                            should_publish = False  # Already published above
                        else:
                            # Don't publish any event for non-last active chatbot deactivations
                            should_publish = False
                            logger.info(f"Skipping deactivation event for chatbot {chatbot_id} (not the last active chatbot for account)")
                    else:
                        # For other status changes (like DRAFT), don't publish
                        logger.info(f"Skipping status event for chatbot {chatbot_id} (status: {new_status})")

                    if should_publish:
                        await event_publisher.publish_status_updated_event(
                            status=new_status,
                            connected_account_id=chatbot.connected_account_id,
                            connected_account_name=chatbot.connected_account_display_name,
                            chatbot_id=chatbot_id,
                            tenant_id=tenant_id
                        )
                except Exception as e:
                    logger.error(f"Failed to publish status updated event for chatbot {chatbot_id}: {str(e)}")
                    # Don't fail the status update if event publishing fails

            # Build response
            connected_account = None
            if (chatbot.connected_account_display_name or chatbot.connected_account_id):
                connected_account = {
                    "displayName": chatbot.connected_account_display_name,
                    "accountId": chatbot.connected_account_id
                }

            return {
                "id": chatbot.id,
                "name": chatbot.name,
                "status": chatbot.status,
                "previousStatus": old_status,
                "connectedAccount": connected_account,
                "trigger": chatbot.trigger,
                "updated_at": chatbot.updated_at,
                "updated_by": user_id
            }

        except (HTTPException, ActiveChatbotExistsError):
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error updating chatbot status {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating chatbot status: {str(e)}")
        finally:
            if db:
                db.close()

    def _is_last_active_chatbot_for_account(
        self, 
        db: Session, 
        connected_account_id: int, 
        tenant_id: int, 
        current_chatbot_id: str
    ) -> bool:
        """
        Check if the current chatbot is the last active chatbot for the connected account
        
        Args:
            db: Database session
            connected_account_id: The connected account ID
            tenant_id: The tenant ID
            current_chatbot_id: The ID of the chatbot being deactivated
            
        Returns:
            bool: True if this is the last active chatbot for the account, False otherwise
        """
        try:
            # Count active chatbots for this connected account (excluding the current one)
            active_count = db.query(Chatbot).filter(
                Chatbot.connected_account_id == connected_account_id,
                Chatbot.tenant_id == tenant_id,
                Chatbot.status == "ACTIVE",
                Chatbot.id != current_chatbot_id
            ).count()
            
            # If there are no other active chatbots, this is the last one
            is_last = active_count == 0
            
            logger.info(
                f"Active chatbot check for account {connected_account_id}: "
                f"current_chatbot={current_chatbot_id}, other_active_count={active_count}, is_last={is_last}"
            )
            
            return is_last
            
        except Exception as e:
            logger.error(f"Error checking if chatbot is last active for account {connected_account_id}: {str(e)}")
            # Default to True (publish event) if there's an error to be safe
            return True

    def list_chatbots(self, tenant_id: int, include_draft: bool = True) -> List[Dict[str, Any]]:
        """
        List all chatbots for a tenant, sorted with active chatbots first by updated_at desc,
        then non-active chatbots

        Args:
            tenant_id: The tenant ID
            include_draft: Whether to include DRAFT status chatbots

        Returns:
            List[Dict[str, Any]]: List of chatbots with basic information, sorted by status and updated_at
        """
        db = None
        try:
            db = next(get_db())

            query = db.query(Chatbot).filter(Chatbot.tenant_id == tenant_id)

            if not include_draft:
                query = query.filter(Chatbot.status != "DRAFT")

            # Order by status (ACTIVE first) and then by updated_at desc
            chatbots = query.order_by(
                case(
                    (Chatbot.status == "ACTIVE", 0),
                    else_=1
                ),
                desc(Chatbot.updated_at)
            ).all()

            # Get all unique user IDs for batch fetching
            user_ids = set()
            for chatbot in chatbots:
                if chatbot.created_by:
                    user_ids.add(chatbot.created_by)
                if chatbot.updated_by:
                    user_ids.add(chatbot.updated_by)

            # Fetch all users in one query
            users_dict = user_service.get_users_by_ids(list(user_ids), db) if user_ids else {}

            result = []
            for chatbot in chatbots:
                # Get question count for each chatbot
                question_count = db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot.id,
                    ChatbotQuestion.tenant_id == tenant_id
                ).count()

                # Get knowledgebase count for each chatbot
                kb_count = db.query(ChatbotKnowledgebase).filter(
                    ChatbotKnowledgebase.chatbot_id == chatbot.id,
                    ChatbotKnowledgebase.tenant_id == tenant_id
                ).count()

                # Get user information for createdBy and updatedBy
                created_by_user = users_dict.get(chatbot.created_by) if chatbot.created_by else None
                updated_by_user = users_dict.get(chatbot.updated_by) if chatbot.updated_by else None

                result.append({
                    "id": chatbot.id,
                    "name": chatbot.name,
                    "type": chatbot.type,
                    "description": chatbot.description,
                    "status": chatbot.status,
                    "welcomeMessage": chatbot.welcome_message,
                    "thankYouMessage": chatbot.thank_you_message,
                    "connectedAccountDisplayName": chatbot.connected_account_display_name,
                    "connectedAccountId": chatbot.connected_account_id,
                    "trigger": chatbot.trigger,  # Keep trigger field
                    "entities": chatbot.entities,  # Include entities configuration
                    # Removed entityType - not needed for account-based chatbots
                    "createdBy": {"id": created_by_user.id, "name": created_by_user.name} if created_by_user else None,
                    "updatedBy": {"id": updated_by_user.id, "name": updated_by_user.name} if updated_by_user else None,
                    "createdAt": chatbot.created_at,
                    "updatedAt": chatbot.updated_at,
                    "questionCount": question_count,
                    "knowledgebaseCount": kb_count
                })

            logger.info(f"Listed {len(result)} chatbots for tenant {tenant_id} (include_draft: {include_draft}) - sorted with active first")

            return result

        except Exception as e:
            logger.error(f"Error listing chatbots for tenant {tenant_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error listing chatbots: {str(e)}")
        finally:
            if db:
                db.close()

    def get_chatbot_for_conversation(self, chatbot_id: str, tenant_id: int) -> Optional[Chatbot]:
        """
        Get a chatbot for conversation purposes (used by conversation endpoints)

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Optional[Chatbot]: The chatbot if found, None otherwise
        """
        db = None
        try:
            db = next(get_db())

            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if chatbot:
                logger.info(f"Retrieved chatbot {chatbot_id} for conversation")
            else:
                logger.warning(f"Chatbot {chatbot_id} not found for tenant {tenant_id}")

            return chatbot

        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id} for conversation: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def get_chatbot_questions_for_conversation(self, chatbot_id: str, tenant_id: int) -> List[Dict[str, Any]]:
        """
        Get chatbot questions for conversation purposes with entity field mappings
        Only returns positions 1-5 (predefined questions). Position 6 is field config only.

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            List[Dict[str, Any]]: List of predefined questions (positions 1-5) with entity field mappings
        """
        db = None
        try:
            db = next(get_db())

            # Only get positions 1-5 for conversation (predefined questions)
            # Position 6 is field configuration only, not asked to users
            questions = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id,
                ChatbotQuestion.position <= 5  # Only predefined questions
            ).order_by(ChatbotQuestion.position).all()  # Order by position

            # Format questions with their entity fields
            formatted_questions = []
            for question in questions:
                entity_fields = db.query(ChatbotQuestionEntityField).filter(
                    ChatbotQuestionEntityField.question_id == question.id
                ).all()

                formatted_questions.append({
                    "id": question.id,
                    "question": question.question,
                    "position": question.position,
                    "entity_fields": [{
                        "entity_type": ef.entity_type,
                        "field_id": ef.field_id,
                        "name": ef.name,
                        "display_name": ef.display_name,
                        "standard": ef.standard
                    } for ef in entity_fields]
                })

            logger.info(f"Retrieved {len(formatted_questions)} questions for chatbot {chatbot_id}")

            return formatted_questions

        except Exception as e:
            logger.error(f"Error getting questions for chatbot {chatbot_id}: {str(e)}")
            raise
        finally:
            if db:
                db.close()



    def find_chatbot_by_account(self, connected_account_id: int, tenant_id: int) -> Optional[Chatbot]:
        """
        Find an active chatbot by connected account ID.

        Note: Multiple active chatbots per account are now supported.
        This method returns the first active chatbot found for backward compatibility.
        For workflow-triggered conversations, the specific chatbot_id is provided in the event payload.

        Args:
            connected_account_id: The connected account ID
            tenant_id: The tenant ID

        Returns:
            Optional[Chatbot]: The first active chatbot model object or None if not found
        """
        db = None
        try:
            db = next(get_db())

            chatbot = db.query(Chatbot).filter(
                Chatbot.connected_account_id == connected_account_id,
                Chatbot.tenant_id == tenant_id,
                Chatbot.status == "ACTIVE"
            ).first()

            if chatbot:
                logger.info(f"Found chatbot {chatbot.id} for account_id={connected_account_id}")
            else:
                logger.warning(f"No active chatbot found for account_id={connected_account_id}, tenant={tenant_id}")

            return chatbot

        except Exception as e:
            logger.error(f"Error finding chatbot by account: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    async def validate_connected_account(self, connected_account_id: int, token: str) -> Dict[str, Any]:
        """
        Validate that a connected account exists and is active by calling the connected accounts API

        Args:
            connected_account_id: The connected account ID to validate
            token: Authorization token for API calls

        Returns:
            Dict[str, Any]: Connected account details if valid

        Raises:
            ConnectedAccountNotFoundError: If connected account doesn't exist
            ConnectedAccountNotActiveError: If connected account is not active
            HTTPException: For other API errors
        """
        from app.exceptions import ConnectedAccountNotFoundError, ConnectedAccountNotActiveError
        
        try:
            logger.info(f"Validating connected account ID: {connected_account_id}")
            
            # Call the connected accounts API
            url = "http://sd-message/v1/messages/connected-accounts"
            headers = {
                "Accept": "application/json",
                "Authorization": f"Bearer {token}"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code != 200:
                    logger.info(f"Failed to fetch connected accounts. Status: {response.status_code}, Response: {response.text}")
                    raise HTTPException(
                        status_code=500,
                        detail=f"Failed to validate connected account. API returned status {response.status_code}"
                    )
                
                data = response.json()
                connected_accounts = data.get("content", [])
                
                # Find the specific connected account
                target_account = None
                for account in connected_accounts:
                    if account.get("id") == connected_account_id:
                        target_account = account
                        break
                
                if not target_account:
                    logger.error(f"Connected account with ID {connected_account_id} not found")
                    raise ConnectedAccountNotFoundError(connected_account_id)
                
                # Check if the account is active
                account_status = target_account.get("status")
                if account_status != "active":
                    logger.error(f"Connected account {connected_account_id} is not active. Status: {account_status}")
                    raise ConnectedAccountNotActiveError(connected_account_id, account_status)
                
                logger.info(f"Connected account {connected_account_id} validated successfully. Status: {account_status}")
                return target_account
                
        except (HTTPException, ConnectedAccountNotFoundError, ConnectedAccountNotActiveError):
            # Re-raise these exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Error validating connected account {connected_account_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to validate connected account: {str(e)}"
            )

    async def update_entities_after_conversation(
        self,
        entity_details: List[Dict[str, Any]],
        collected_answers: List[Dict[str, Any]],
        tenant_id: int,
        user_id: str,
        chatbot_id: str = None,
        conversation_state: dict = None
    ) -> Dict[str, Any]:
        """
        Update all entities with collected answers after conversation completion

        Args:
            entity_details: List of entities with id and entityType
            collected_answers: List of question-answer pairs collected during conversation
            tenant_id: The tenant ID (extracted from JWT token)
            user_id: The user ID (extracted from JWT token)

        Returns:
            Dict with update results for each entity, and publishes events to RabbitMQ with format:
            {
                "leadUpdateRequest": {
                    "first": "Akshay",
                    "last": "Gunshetti"
                },
                "metadata": {
                    "entityId": 583615,
                    "tenantId": 82,
                    "userId": 159,
                    "entityType": "LEAD"
                }
            }
        """
        # Filter entities based on chatbot configuration
        allowed_entities = self._get_allowed_entities(chatbot_id, tenant_id)
        
        if allowed_entities is not None:
            # Filter entity_details to only include allowed entities
            original_count = len(entity_details)
            entity_details = [
                entity for entity in entity_details
                if (entity.get('entityType') or entity.get('entity', '')).upper() in allowed_entities
            ]
            filtered_count = original_count - len(entity_details)
            if filtered_count > 0:
                logger.info(f"🔒 ENTITY FILTER - Filtered out {filtered_count} entities not in chatbot configuration. Allowed: {allowed_entities}")
        
        update_results = {
            "successful_updates": [],
            "failed_updates": [],
            "total_entities": len(entity_details)
        }

        try:
            logger.info(f"Updating {len(entity_details)} entities with {len(collected_answers)} answers")

            # Log all entities being processed
            for i, entity in enumerate(entity_details, 1):
                entity_id = entity.get('id') or entity.get('entityId')  # Support both formats
                entity_type = entity.get('entityType') or entity.get('entity')  # Support both formats
                if entity_type:
                    entity_type = entity_type.upper()
                logger.info(f"Entity {i}: {entity_type} (ID: {entity_id})")

            for entity in entity_details:
                entity_id = entity.get("id") or entity.get("entityId")  # Support both formats
                entity_type = entity.get("entityType") or entity.get("entity")  # Support both formats
                
                # Convert entity type to uppercase for consistency
                if entity_type:
                    entity_type = entity_type.upper()

                logger.info(f"🔄 Processing entity: {entity_type} {entity_id}")



                try:
                    # Filter answers that apply to this specific entity type
                    entity_update_data = {}
                    applicable_answers = []

                    for answer in collected_answers:
                        answer_entity_type = answer.get("entity_type")
                        field_name = answer.get("field_name")
                        field_value = answer.get("answer")

                        # Normalize entity types for comparison (case-insensitive)
                        normalized_answer_entity_type = answer_entity_type.lower() if answer_entity_type else None
                        normalized_entity_type = entity_type.lower() if entity_type else None

                        # Debug logging to understand the filtering
                        logger.info(f"🔍 Filtering answer for {entity_type}: field_name={field_name}, answer_entity_type={answer_entity_type}, normalized_answer={normalized_answer_entity_type}, normalized_entity={normalized_entity_type}")

                        # Include answers that either:
                        # 1. Are specifically for this entity type (case-insensitive), OR
                        # 2. Are generic (no entity_type specified) and have a field_name
                        if field_name and field_value and (
                            not answer_entity_type or
                            normalized_answer_entity_type == normalized_entity_type
                        ):
                            logger.info(f"✅ Including answer for {entity_type}: {field_name} = {field_value}")
                            entity_update_data[field_name] = field_value
                            applicable_answers.append({
                                "question": answer.get("question"),
                                "question_id": answer.get("question_id"),
                                "field_name": field_name,
                                "value": field_value
                            })
                        else:
                            logger.info(f"❌ Excluding answer for {entity_type}: {field_name} = {field_value} (answer_entity_type={answer_entity_type}, entity_type={entity_type})")

                    if not entity_update_data:
                        logger.info(f"No applicable answers found for {entity_type} {entity_id}")
                        update_results["successful_updates"].append({
                            "entity_id": entity_id,
                            "entity_type": entity_type,
                            "updated_fields": [],
                            "status": "success",
                            "message": "No applicable fields to update"
                        })
                        continue

                    # Prepare entity update event with new payload format including tenantId and userId
                    entity_event = await self._prepare_entity_update_event(
                        entity_id, entity_type, applicable_answers, tenant_id, user_id, chatbot_id, conversation_state
                    )

                    # Publish entity update event
                    publish_success = await self._publish_entity_update_event(entity_event)

                    if publish_success:
                        logger.info(f"✅ Published entity update for {entity_type} {entity_id}")
                    else:
                        logger.error(f"❌ Failed to publish entity update for {entity_type} {entity_id}")

                    update_results["successful_updates"].append({
                        "entity_id": entity_id,
                        "entity_type": entity_type,
                        "updated_fields": list(entity_update_data.keys()),
                        "field_updates": applicable_answers,
                        "event_published": entity_event,
                        "status": "success"
                    })

                except Exception as entity_error:
                    logger.error(f"Failed to update {entity_type} {entity_id}: {str(entity_error)}")
                    update_results["failed_updates"].append({
                        "entity_id": entity_id,
                        "entity_type": entity_type,
                        "error": str(entity_error),
                        "status": "failed"
                    })

            logger.info(f"Entity updates completed: {len(update_results['successful_updates'])} successful, {len(update_results['failed_updates'])} failed")
            return update_results

        except Exception as e:
            logger.error(f"Error updating entities after conversation: {str(e)}")
            raise

    def _trim_field_value(self, field_value: str, is_position_6: bool = False) -> str:
        """
        Trim field value based on position requirements
        
        Args:
            field_value: The field value to trim
            is_position_6: Whether this is a position 6 field (user questions)
            
        Returns:
            str: Trimmed field value
        """
        if not isinstance(field_value, str):
            return field_value
            
        if is_position_6:
            # Position 6 field: trim to 2500 characters (for user questions)
            max_length = 2500
            if len(field_value) > max_length:
                logger.info(f"Trimming position 6 field value from {len(field_value)} to {max_length} characters")
                return field_value[:max_length]
        else:
            # Other positions: trim to 255 characters (for predefined questions)
            max_length = 255
            if len(field_value) > max_length:
                logger.info(f"Trimming field value from {len(field_value)} to {max_length} characters")
                return field_value[:max_length]
        
        return field_value

    def _validate_entity_field_types(self, questions: List[QuestionCreate], token: str) -> None:
        """
        Validate that entity fields used in questions are of allowed types (TEXT_FIELD or PARAGRAPH_TEXT),
        that field names are not restricted, and that field IDs match field names from sd-config API
        
        Args:
            questions: List of questions to validate
            token: Authorization token for API calls
            
        Raises:
            ValidationError: If any field type is not allowed
            RestrictedFieldNameError: If any field name is restricted
            FieldIdNameMismatchError: If field ID does not match field name from API
        """
        import httpx
        from app.exceptions import ValidationError, RestrictedFieldNameError, FieldIdNameMismatchError
        
        # Allowed field types
        ALLOWED_FIELD_TYPES = ["TEXT_FIELD", "PARAGRAPH_TEXT"]
        
        # Restricted field names that cannot be used
        RESTRICTED_FIELD_NAMES = ["subSource", "utmSource", "utmMedium", "utmCampaign", "utmTerm", "utmContent","pipelineStageReason"]
        
        # Collect all unique entity types and field IDs from questions, along with provided field names
        entity_fields_to_validate = set()
        field_name_mapping = {}  # Store field ID -> provided name mapping for validation
        
        for question in questions:
            if hasattr(question, 'entityFields') and question.entityFields:
                for entity_field in question.entityFields:
                    if entity_field.entityType and entity_field.fieldId:
                        entity_type_lower = entity_field.entityType.lower()
                        field_id = entity_field.fieldId
                        entity_fields_to_validate.add((entity_type_lower, field_id))
                        
                        # Store provided field name for later validation
                        if hasattr(entity_field, 'name') and entity_field.name:
                            field_name_mapping[(entity_type_lower, field_id)] = entity_field.name
                            
                            # Validate field name if provided (restricted names check)
                            if entity_field.name in RESTRICTED_FIELD_NAMES:
                                raise RestrictedFieldNameError(
                                    field_name=entity_field.name,
                                    restricted_names=RESTRICTED_FIELD_NAMES,
                                    message=f"Field '{entity_field.name}' is restricted and cannot be used for chatbot questions. Restricted field names: {', '.join(RESTRICTED_FIELD_NAMES)}"
                                )
            else:
                # Legacy format
                if hasattr(question, 'entityType') and hasattr(question, 'fieldId') and question.entityType and question.fieldId:
                    entity_type_lower = question.entityType.lower()
                    field_id = question.fieldId
                    entity_fields_to_validate.add((entity_type_lower, field_id))
                    
                    # Store provided field name for later validation
                    if hasattr(question, 'name') and question.name:
                        field_name_mapping[(entity_type_lower, field_id)] = question.name
        
        if not entity_fields_to_validate:
            logger.info("No entity fields to validate")
            return
        
        logger.info(f"Validating {len(entity_fields_to_validate)} entity fields")
        
        # Fetch field information for each entity type
        field_info_cache = {}
        
        for entity_type, field_id in entity_fields_to_validate:
            if entity_type not in field_info_cache:
                try:
                    # Call the entity fields API
                    url = f"http://sd-config/v1/entities/{entity_type}/fields"
                    params = {
                        "entityType": entity_type,
                        "custom-only": "false",
                        "sort": "createdAt,asc",
                        "page": 0,
                        "size": 1000  # Get more fields to ensure we find the one we need
                    }
                    
                    headers = {
                        "Authorization": f"Bearer {token}",
                        "Accept": "application/json",
                        "Content-Type": "application/json"
                    }
                    
                    logger.info(f"Fetching fields for entity type: {entity_type}")
                    
                    with httpx.Client(timeout=30.0) as client:
                        response = client.get(url, params=params, headers=headers)
                        response.raise_for_status()
                        
                        fields_data = response.json()
                        field_info_cache[entity_type] = fields_data
                        
                        logger.info(f"Retrieved {len(fields_data)} fields for {entity_type}")
                        
                except Exception as e:
                    logger.error(f"Failed to fetch fields for entity type {entity_type}: {str(e)}")
                    raise ValidationError(
                        field="entity_fields",
                        value=entity_type,
                        message=f"Failed to validate entity fields for {entity_type}: {str(e)}",
                        error_code="041086"
                    )
            
            # Find the specific field in the response
            field_found = False
            field_type = None
            field_name = None
            
            for field in field_info_cache[entity_type]:
                if field.get("id") == field_id:
                    field_found = True
                    field_type = field.get("type")
                    field_name = field.get("name", "Unknown")
                    break
            
            if not field_found:
                raise ValidationError(
                    field="entity_fields",
                    value=f"{entity_type}:{field_id}",
                    message=f"Field ID {field_id} not found for entity type {entity_type}",
                    error_code="041087"
                )
            
            # Validate that field name is not restricted
            if field_name in RESTRICTED_FIELD_NAMES:
                raise RestrictedFieldNameError(
                    field_name=field_name,
                    restricted_names=RESTRICTED_FIELD_NAMES,
                    message=f"Field '{field_name}' (ID: {field_id}) is restricted and cannot be used for chatbot questions. Restricted field names: {', '.join(RESTRICTED_FIELD_NAMES)}"
                )
            
            # Validate field ID matches field name if provided in question data
            if (entity_type, field_id) in field_name_mapping:
                provided_name = field_name_mapping[(entity_type, field_id)]
                if provided_name != field_name:
                    raise FieldIdNameMismatchError(
                        field_id=field_id,
                        provided_name=provided_name,
                        actual_name=field_name,
                        entity_type=entity_type,
                        message=f"Field ID {field_id} does not match the field name. Provided name: '{provided_name}', Actual name from API: '{field_name}' for entity type {entity_type}"
                    )
            
            if field_type not in ALLOWED_FIELD_TYPES:
                raise ValidationError(
                    field="entity_fields",
                    value=f"{entity_type}:{field_id}",
                    message=f"Field '{field_name}' (ID: {field_id}) of type '{field_type}' is not allowed. Only TEXT_FIELD and PARAGRAPH_TEXT field types are supported for chatbot questions.",
                    error_code="041088"
                )
            
            logger.info(f"✅ Validated field {field_name} (ID: {field_id}) of type {field_type} for entity {entity_type}")
        
        logger.info(f"✅ All {len(entity_fields_to_validate)} entity fields validated successfully")

    def _get_allowed_entities(self, chatbot_id: str, tenant_id: int) -> Optional[List[str]]:
        """
        Get list of allowed entity types from chatbot configuration.
        
        Args:
            chatbot_id: Chatbot ID
            tenant_id: Tenant ID
            
        Returns:
            List of allowed entity types (uppercase) or None if no restriction
        """
        if not chatbot_id:
            logger.info("🔓 ENTITY FILTER - No chatbot_id provided, allowing all entities")
            return None
        
        try:
            db_gen = get_db()
            db: Session = next(db_gen)
            
            try:
                chatbot = db.query(Chatbot).filter(
                    Chatbot.id == chatbot_id,
                    Chatbot.tenant_id == tenant_id
                ).first()
                
                if not chatbot:
                    logger.warning(f"⚠️ ENTITY FILTER - Chatbot {chatbot_id} not found, allowing all entities")
                    return None
                
                if not chatbot.entities:
                    logger.info(f"🔓 ENTITY FILTER - No entities configured for chatbot {chatbot_id}, allowing all entities")
                    return None
                
                # Extract entity types from entities configuration
                allowed_entities = [entity.get('entity', '').upper() for entity in chatbot.entities if entity.get('entity')]
                
                logger.info(f"🔒 ENTITY FILTER - Loaded {len(allowed_entities)} allowed entities for chatbot {chatbot_id}: {allowed_entities}")
                return allowed_entities
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ ENTITY FILTER - Error loading chatbot entities: {str(e)}, allowing all entities")
            return None

    async def _prepare_entity_update_event(
        self,
        entity_id: int,
        entity_type: str,
        applicable_answers: List[Dict[str, Any]],
        tenant_id: int,
        user_id: str,
        chatbot_id: str = None,
        conversation_state: dict = None
    ) -> Dict[str, Any]:
        """
        Prepare entity update event with new payload format including tenantId and userId

        Args:
            entity_id: The entity ID
            entity_type: The entity type (lead, contact, etc.)
            applicable_answers: List of answers applicable to this entity
            tenant_id: The tenant ID (from JWT token)
            user_id: The user ID (from JWT token)

        Returns:
            Dict with entity update event data in the new format:
            {
                "leadUpdateRequest": {
                    "first": "Akshay",
                    "last": "Gunshetti"
                },
                "metadata": {
                    "entityId": 583615,
                    "tenantId": 82,
                    "userId": 159,
                    "entityType": "LEAD"
                }
            }
        """
        global position_6_field
        try:
            # Get the original question data to determine standard vs custom fields
            db = next(get_db())

            # Create new payload format based on entity type
            entity_type_lower = entity_type.lower()

            # Determine the update request field name
            if entity_type_lower == "lead":
                update_request_field = "leadUpdateRequest"
                entity_type_upper = "LEAD"
            elif entity_type_lower == "contact":
                update_request_field = "contactUpdateRequest"
                entity_type_upper = "CONTACT"
            else:
                # Fallback for other entity types
                update_request_field = f"{entity_type_lower}UpdateRequest"
                entity_type_upper = entity_type.upper()

            # Build the update request object with standard and custom fields separation
            update_request = {}
            custom_field_values = {}

            # Process each answer and separate standard vs custom fields
            for answer in applicable_answers:
                field_name = answer["field_name"]
                field_value = answer["value"]  # Use "value" key as created in the main method
                question_id = answer.get("question_id")

                # Get field information to determine if it's standard or custom
                is_standard = False

                if question_id:
                    # Query the question entity field to check if it's standard
                    # Try multiple entity type formats to handle different database formats
                    entity_field = None
                    
                    # Try exact match first
                    entity_field = db.query(ChatbotQuestionEntityField).filter(
                        ChatbotQuestionEntityField.question_id == question_id,
                        ChatbotQuestionEntityField.entity_type == entity_type,
                        ChatbotQuestionEntityField.name == field_name
                    ).first()
                    
                    # Try lowercase if not found
                    if not entity_field:
                        entity_field = db.query(ChatbotQuestionEntityField).filter(
                            ChatbotQuestionEntityField.question_id == question_id,
                            ChatbotQuestionEntityField.entity_type == entity_type.lower(),
                            ChatbotQuestionEntityField.name == field_name
                        ).first()

                    # Try uppercase if not found
                    if not entity_field:
                        entity_field = db.query(ChatbotQuestionEntityField).filter(
                            ChatbotQuestionEntityField.question_id == question_id,
                            ChatbotQuestionEntityField.entity_type == entity_type_upper,
                            ChatbotQuestionEntityField.name == field_name
                        ).first()

                    # Try case-insensitive search as last resort for question fields
                    if not entity_field:
                        all_fields = db.query(ChatbotQuestionEntityField).filter(
                            ChatbotQuestionEntityField.question_id == question_id,
                            ChatbotQuestionEntityField.name == field_name
                        ).all()
                        
                        # Find matching entity type (case-insensitive)
                        for field in all_fields:
                            if field.entity_type.lower() == entity_type.lower():
                                entity_field = field
                                logger.info(f"Found question entity field via case-insensitive search: {field_name} for {entity_type}")
                                break
                    
                    # If still not found, try ChatbotNodeEntityField (for rule-based chatbots)
                    if not entity_field:
                        entity_field = db.query(ChatbotNodeEntityField).filter(
                            ChatbotNodeEntityField.node_id == question_id,
                            ChatbotNodeEntityField.entity_type == entity_type,
                            ChatbotNodeEntityField.name == field_name
                        ).first()
                        
                        # Try lowercase for node fields
                        if not entity_field:
                            entity_field = db.query(ChatbotNodeEntityField).filter(
                                ChatbotNodeEntityField.node_id == question_id,
                                ChatbotNodeEntityField.entity_type == entity_type.lower(),
                                ChatbotNodeEntityField.name == field_name
                            ).first()
                            
                        # Try uppercase for node fields
                        if not entity_field:
                            entity_field = db.query(ChatbotNodeEntityField).filter(
                                ChatbotNodeEntityField.node_id == question_id,
                                ChatbotNodeEntityField.entity_type == entity_type_upper,
                                ChatbotNodeEntityField.name == field_name
                            ).first()
                    
                    # Try case-insensitive search for node fields if still not found
                    if not entity_field:
                        all_node_fields = db.query(ChatbotNodeEntityField).filter(
                            ChatbotNodeEntityField.node_id == question_id,
                            ChatbotNodeEntityField.name == field_name
                        ).all()
                        
                        # Find matching entity type (case-insensitive)
                        for field in all_node_fields:
                            if field.entity_type.lower() == entity_type.lower():
                                entity_field = field
                                logger.info(f"Found node entity field via case-insensitive search: {field_name} for {entity_type}")
                                break

                    if entity_field:
                        is_standard = entity_field.standard
                    else:
                        logger.debug(f"No entity field found for question_id={question_id}, entity_type={entity_type}, field_name={field_name}")
                else:
                    # For rule-based chatbots, check if the answer includes the standard flag
                    # This comes from the entityFields JSON in the node data
                    if "standard" in answer:
                        is_standard = answer.get("standard", False)
                    else:
                        # Fallback to prefix-based detection if standard flag not provided
                        is_standard = not (field_name and field_name.startswith('cf'))

                # Add to appropriate section based on standard flag
                if is_standard:
                    # Standard field - add directly to update request
                    trimmed_value = self._trim_field_value(field_value, is_position_6=False)
                    update_request[field_name] = trimmed_value
                    logger.info(f"Added standard field: {field_name} = {trimmed_value}")
                else:
                    # Custom field - add to customFieldValues
                    trimmed_value = self._trim_field_value(field_value, is_position_6=False)
                    custom_field_values[field_name] = trimmed_value
                    logger.info(f"Added custom field: {field_name} = {trimmed_value}")

            # Add customFieldValues to update request if there are any custom fields
            if custom_field_values:
                update_request["customFieldValues"] = custom_field_values

            # Get user questions from conversation state and add to the appropriate field
            conversation_questions = None
            if conversation_state:
                conversation_questions = self.get_conversation_questions_for_entity_update(conversation_state)
                logger.info(f"Retrieved conversation questions for entity update: {conversation_questions}")

            if conversation_questions and chatbot_id:
                logger.info(f"Processing position 6 field for chatbot {chatbot_id}, tenant {tenant_id}")
                # Find the position 6 question's field mapping for this entity type
                position_6_question = db.query(ChatbotQuestion).filter(
                    ChatbotQuestion.chatbot_id == chatbot_id,
                    ChatbotQuestion.tenant_id == tenant_id,
                    ChatbotQuestion.position == 6
                ).first()

                if position_6_question:
                    logger.info(f"Found position 6 question: {position_6_question.id}")
                    
                    # Try multiple entity type formats to handle case sensitivity
                    position_6_field = None
                    
                    # Try exact match first
                    position_6_field = db.query(ChatbotQuestionEntityField).filter(
                        ChatbotQuestionEntityField.question_id == position_6_question.id,
                        ChatbotQuestionEntityField.entity_type == entity_type_upper
                    ).first()
                    
                    # Try lowercase if not found
                    if not position_6_field:
                        position_6_field = db.query(ChatbotQuestionEntityField).filter(
                            ChatbotQuestionEntityField.question_id == position_6_question.id,
                            ChatbotQuestionEntityField.entity_type == entity_type.lower()
                        ).first()
                    
                    # Try original entity_type if not found
                    if not position_6_field:
                        position_6_field = db.query(ChatbotQuestionEntityField).filter(
                            ChatbotQuestionEntityField.question_id == position_6_question.id,
                            ChatbotQuestionEntityField.entity_type == entity_type
                        ).first()

                    if position_6_field:
                        logger.info(f"Found position 6 field mapping: {position_6_field.name} (standard={position_6_field.standard})")
                    else:
                        logger.warning(f"No position 6 field mapping found for entity_type={entity_type_upper}")
                        # Try to find any position 6 field mappings for this question
                        all_position_6_fields = db.query(ChatbotQuestionEntityField).filter(
                            ChatbotQuestionEntityField.question_id == position_6_question.id
                        ).all()
                        logger.info(f"Available position 6 field mappings: {[(f.entity_type, f.name, f.standard) for f in all_position_6_fields]}")
                else:
                    logger.info(f"No position 6 question found for chatbot {chatbot_id}, tenant {tenant_id}")

                if position_6_field:
                        field_name = position_6_field.name
                        # Trim position 6 field value to 2500 characters
                        trimmed_conversation_questions = self._trim_field_value(conversation_questions, is_position_6=True)
                        
                        if position_6_field.standard:
                            # Standard field - add directly to update request
                            update_request[field_name] = trimmed_conversation_questions
                            logger.info(f"Added position 6 field to standard fields: {field_name} = {trimmed_conversation_questions}")
                        else:
                            # Custom field - add to customFieldValues
                            if "customFieldValues" not in update_request:
                                update_request["customFieldValues"] = {}
                            update_request["customFieldValues"][field_name] = trimmed_conversation_questions
                            logger.info(f"Added position 6 field to custom fields: {field_name} = {trimmed_conversation_questions}")

            # Get chatbot name if chatbot_id is provided
            chatbot_name = None
            if chatbot_id:
                try:
                    chatbot = db.query(Chatbot).filter(
                        Chatbot.id == chatbot_id,
                        Chatbot.tenant_id == tenant_id
                    ).first()
                    if chatbot:
                        chatbot_name = chatbot.name
                        logger.info(f"Found chatbot name: {chatbot_name} for chatbot_id: {chatbot_id}")
                    else:
                        logger.warning(f"Chatbot not found for chatbot_id: {chatbot_id}, tenant_id: {tenant_id}")
                except Exception as e:
                    logger.error(f"Error fetching chatbot name for chatbot_id {chatbot_id}: {str(e)}")

            # Create the new payload format with chatbot info at top level
            entity_event = {
                "chatbotId": chatbot_id,  # Chatbot ID at top level
                "chatbotName": chatbot_name,  # Chatbot name at top level
                update_request_field: update_request,
                "metadata": {
                    "entityId": int(entity_id),  # Standardized field name
                    "tenantId": int(tenant_id),  # From JWT token
                    "userId": int(user_id),  # From JWT token
                    "entityType": entity_type_upper  # Uppercase entity type
                }
            }

            return entity_event

        except Exception as e:
            logger.error(f"Error preparing entity update event: {str(e)}")
            # Fallback: create basic event structure with new metadata format
            entity_type_lower = entity_type.lower()
            
            # Get chatbot name for fallback cases
            chatbot_name = None
            if chatbot_id and db:
                try:
                    chatbot = db.query(Chatbot).filter(
                        Chatbot.id == chatbot_id,
                        Chatbot.tenant_id == tenant_id
                    ).first()
                    if chatbot:
                        chatbot_name = chatbot.name
                except Exception as chatbot_error:
                    logger.error(f"Error fetching chatbot name in fallback: {str(chatbot_error)}")
            
            if entity_type_lower == "lead":
                return {
                    "chatbotId": chatbot_id,  # Chatbot ID at top level
                    "chatbotName": chatbot_name,  # Chatbot name at top level
                    "leadUpdateRequest": {"error": f"Failed to prepare event: {str(e)}"},
                    "metadata": {
                        "entityId": int(entity_id),
                        "tenantId": int(tenant_id),
                        "userId": int(user_id),
                        "entityType": "LEAD"
                    }
                }
            elif entity_type_lower == "contact":
                return {
                    "chatbotId": chatbot_id,  # Chatbot ID at top level
                    "chatbotName": chatbot_name,  # Chatbot name at top level
                    "contactUpdateRequest": {"error": f"Failed to prepare event: {str(e)}"},
                    "metadata": {
                        "entityId": int(entity_id),
                        "tenantId": int(tenant_id),
                        "userId": int(user_id),
                        "entityType": "CONTACT"
                    }
                }
            else:
                return {
                    "chatbotId": chatbot_id,  # Chatbot ID at top level
                    "chatbotName": chatbot_name,  # Chatbot name at top level
                    f"{entity_type_lower}UpdateRequest": {"error": f"Failed to prepare event: {str(e)}"},
                    "metadata": {
                        "entityId": int(entity_id),
                        "tenantId": int(tenant_id),
                        "userId": int(user_id),
                        "entityType": entity_type.upper()
                    }
                }
        finally:
            if db:
                db.close()

    async def _publish_entity_update_event(self, entity_event: Dict[str, Any]) -> bool:
        """
        Publish entity update event with detailed logging

        Args:
            entity_event: The entity update event data

        Returns:
            bool: True if published successfully, False otherwise
        """
        try:

            # Use entity_event directly as the payload (no wrapper)
            event_payload = entity_event

            # Determine entity type and ID from the new metadata format
            entity_type = None
            entity_id = None
            update_request = {}

            # Extract entity type and ID from the new metadata format
            if "metadata" in entity_event:
                metadata = entity_event["metadata"]
                entity_id = metadata.get("entityId")
                entity_type_upper = metadata.get("entityType")  # "LEAD", "CONTACT"

                if entity_type_upper:
                    entity_type = entity_type_upper.lower()  # Convert to lowercase for routing key

                # Extract update request based on entity type
                if entity_type == "lead":
                    update_request = entity_event.get("leadUpdateRequest", {})
                elif entity_type == "contact":
                    update_request = entity_event.get("contactUpdateRequest", {})
                else:
                    # Fallback for other entity types
                    update_request_key = f"{entity_type}UpdateRequest"
                    update_request = entity_event.get(update_request_key, {})
            else:
                # Fallback: try to extract from old format (for backward compatibility)
                if "leadId" in entity_event:
                    entity_type = "lead"
                    entity_id = entity_event["leadId"]
                    update_request = entity_event.get("leadUpdateRequest", {})
                elif "contactId" in entity_event:
                    entity_type = "contact"
                    entity_id = entity_event["contactId"]
                    update_request = entity_event.get("contactUpdateRequest", {})
                else:
                    # Last resort fallback
                    for key in entity_event.keys():
                        if key.endswith("Id"):
                            entity_type = key[:-2]  # Remove "Id" suffix
                            entity_id = entity_event[key]
                            update_request_key = f"{entity_type}UpdateRequest"
                            update_request = entity_event.get(update_request_key, {})
                            break

            # RabbitMQ configuration
            exchange_name = "ex.whatsappChatbot"

            # Ensure entity_type is not None to avoid "chatbot.None.update" routing key
            if not entity_type:
                logger.error(f"❌ Entity type is None or empty. Cannot determine routing key.")
                logger.error(f"   Event payload: {entity_event}")
                return False

            routing_key = f"chatbot.{entity_type}.update"

            # Log publishing details
            # Serialize payload
            payload_json = json.dumps(event_payload, default=str)

            # Publish to RabbitMQ
            await rabbitmq_service.publish_message(
                exchange=exchange_name,
                routing_key=routing_key,
                message=payload_json,
                durable=True
            )

            # Success logging
            return True

        except Exception as e:
            logger.error(f"Failed to publish entity update: {entity_type or 'unknown'} {entity_id or 'unknown'} - {str(e)}", exc_info=True)
            return False

    def get_conversations_by_entity(self, entity_id: int, entity_type: str, tenant_id: int) -> List[Dict[str, Any]]:
        """
        Get all conversations that involved a specific entity

        Args:
            entity_id: The entity ID to search for
            entity_type: The entity type to search for
            tenant_id: The tenant ID

        Returns:
            List of conversation records that involved this entity
        """
        db = None
        try:
            db = next(get_db())

            # Query conversations where entity_details contains the specified entity
            conversations = db.query(ChatbotConversation).filter(
                ChatbotConversation.tenant_id == tenant_id,
                ChatbotConversation.entity_details.isnot(None)
            ).all()

            # Filter conversations that contain the specific entity
            matching_conversations = []
            for conv in conversations:
                if conv.entity_details:
                    for entity in conv.entity_details:
                        current_entity_id = entity.get("id") or entity.get("entityId")  # Support both formats
                        if (current_entity_id == entity_id and
                            entity.get("entityType") == entity_type):
                            matching_conversations.append({
                                "conversation_id": conv.id,
                                "chatbot_id": conv.chatbot_id,
                                "completed": conv.completed,
                                "entity_details": conv.entity_details,
                                "connected_account_id": conv.connected_account_id,
                                "connected_account_name": conv.connected_account_name,
                                "entity_update_status": conv.entity_update_status,
                                "created_at": conv.created_at.isoformat() if conv.created_at else None,
                                "updated_at": conv.updated_at.isoformat() if conv.updated_at else None
                            })
                            break

            logger.info(f"Found {len(matching_conversations)} conversations for {entity_type} {entity_id}")
            return matching_conversations

        except Exception as e:
            logger.error(f"Error getting conversations by entity: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def get_conversations_by_account(self, connected_account_id: int, tenant_id: int) -> List[Dict[str, Any]]:
        """
        Get all conversations for a specific connected account

        Args:
            connected_account_id: The connected account ID
            tenant_id: The tenant ID

        Returns:
            List of conversation records for this account
        """
        db = None
        try:
            db = next(get_db())

            conversations = db.query(ChatbotConversation).filter(
                ChatbotConversation.connected_account_id == connected_account_id,
                ChatbotConversation.tenant_id == tenant_id
            ).order_by(ChatbotConversation.created_at.desc()).all()

            result = []
            for conv in conversations:
                result.append({
                    "conversation_id": conv.id,
                    "chatbot_id": conv.chatbot_id,
                    "completed": conv.completed,
                    "entity_details": conv.entity_details,
                    "connected_account_id": conv.connected_account_id,
                    "connected_account_name": conv.connected_account_name,
                    "entity_update_status": conv.entity_update_status,
                    "created_at": conv.created_at.isoformat() if conv.created_at else None,
                    "updated_at": conv.updated_at.isoformat() if conv.updated_at else None
                })

            logger.info(f"Found {len(result)} conversations for account {connected_account_id}")
            return result

        except Exception as e:
            logger.error(f"Error getting conversations by account: {str(e)}")
            raise
        finally:
            if db:
                db.close()

    def has_knowledgebase(self, chatbot_id: str, tenant_id: int) -> bool:
        """
        Check if a chatbot has any knowledgebase documents

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            bool: True if chatbot has knowledgebase, False otherwise
        """
        db = None
        try:
            db = next(get_db())

            kb_count = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).count()

            logger.info(f"Chatbot {chatbot_id} has {kb_count} knowledgebase documents")
            return kb_count > 0

        except Exception as e:
            logger.error(f"Error checking knowledgebase for chatbot {chatbot_id}: {str(e)}")
            return False
        finally:
            if db:
                db.close()

    def update_all_chatbot_questions(self, chatbot_id: str, questions: List[QuestionCreate], tenant_id: int, user_id: str = None, token: str = None) -> Dict[str, Any]:
        """
        Update all questions for a chatbot (replaces existing questions)

        Args:
            chatbot_id: The chatbot ID
            questions: List of QuestionCreate objects (maximum 6 allowed)
            tenant_id: The tenant ID
            user_id: The user ID (optional for backward compatibility)
            token: Authorization token (optional for backward compatibility)

        Returns:
            Dict[str, Any]: Update result with questions
        """
        # Validate maximum 6 total configurations (5 predefined questions + 1 field config for user questions)
        if len(questions) > 6:
            raise HTTPException(
                status_code=400,
                detail=f"Maximum 6 configurations allowed (5 predefined questions + 1 user questions field). You provided {len(questions)} configurations."
            )

        # Validate that only positions 1-5 have actual questions, position 6 is field config only
        predefined_questions = [q for q in questions if q.position <= 5]
        position_6_configs = [q for q in questions if q.position == 6]

        if len(predefined_questions) > 5:
            raise HTTPException(
                status_code=400,
                detail=f"Maximum 5 predefined questions allowed (positions 1-5). You provided {len(predefined_questions)} predefined questions."
            )

        if len(position_6_configs) > 1:
            raise HTTPException(
                status_code=400,
                detail="Only one position 6 configuration allowed for user questions field."
            )

        # This is the same as configure_chatbot_questions
        return self.configure_chatbot_questions(chatbot_id, questions, tenant_id, user_id or "system", token or "")

    def update_chatbot_question(self, chatbot_id: str, question_id: str, question_data: QuestionUpdate, tenant_id: int, token: str = None) -> Dict[str, Any]:
        """
        Update a specific question for a chatbot

        Args:
            chatbot_id: The chatbot ID
            question_id: The question ID to update
            question_data: QuestionUpdate object with new data
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Updated question information
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Check if question exists
            question = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.id == question_id,
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id
            ).first()

            if not question:
                raise HTTPException(status_code=404, detail="Question not found")

            # Validate entity field types if token is provided and entity fields are being updated
            if token and hasattr(question_data, 'entityFields') and question_data.entityFields is not None:
                logger.info("Validating entity field types for question update")
                # Create a temporary QuestionCreate object for validation
                temp_question = QuestionCreate(
                    question=question_data.question or question.question,
                    position=question.position,
                    entityFields=question_data.entityFields
                )
                self._validate_entity_field_types([temp_question], token)

            # Update question fields if provided
            if question_data.question is not None:
                question.question = question_data.question

            # Handle entity fields update
            if hasattr(question_data, 'entityFields') and question_data.entityFields is not None:
                # Delete existing entity fields
                db.query(ChatbotQuestionEntityField).filter(
                    ChatbotQuestionEntityField.question_id == question_id
                ).delete()

                # Add new entity fields
                for entity_field in question_data.entityFields:
                    # Handle nullable fields in EntityFieldMapping
                    entity_type = entity_field.entityType or "unknown"
                    field_id = entity_field.fieldId or 0
                    name = entity_field.name or "unknown_field"
                    display_name = entity_field.displayName or "Unknown Field"
                    standard = entity_field.standard if entity_field.standard is not None else False

                    # Skip entity fields with critical null values
                    if not entity_field.entityType or not entity_field.name:
                        logger.warning(f"Skipping entity field with null entityType or name: {entity_field}")
                        continue
                    
                    # Normalize entity type to lowercase for consistency
                    entity_type = entity_type.lower()

                    entity_field_record = ChatbotQuestionEntityField(
                        id=str(uuid.uuid4()),
                        question_id=question_id,
                        entity_type=entity_type,
                        field_id=field_id,
                        name=name,
                        display_name=display_name,
                        standard=standard
                    )
                    db.add(entity_field_record)
            else:
                # Handle legacy format
                existing_entity_field = db.query(ChatbotQuestionEntityField).filter(
                    ChatbotQuestionEntityField.question_id == question_id
                ).first()

                if existing_entity_field:
                    if hasattr(question_data, 'fieldId') and question_data.fieldId is not None:
                        existing_entity_field.field_id = question_data.fieldId
                    if hasattr(question_data, 'entityType') and question_data.entityType is not None:
                        existing_entity_field.entity_type = question_data.entityType
                    if hasattr(question_data, 'name') and question_data.name is not None:
                        existing_entity_field.name = question_data.name
                    if hasattr(question_data, 'displayName') and question_data.displayName is not None:
                        existing_entity_field.display_name = question_data.displayName
                    if hasattr(question_data, 'standard') and question_data.standard is not None:
                        existing_entity_field.standard = question_data.standard

            db.commit()
            db.refresh(question)

            # Get updated entity fields for response
            entity_fields = db.query(ChatbotQuestionEntityField).filter(
                ChatbotQuestionEntityField.question_id == question_id
            ).all()

            logger.info(f"Updated question {question_id} for chatbot {chatbot_id}")

            return {
                "message": "Question updated successfully",
                "question": {
                    "id": question.id,
                    "question": question.question,
                    "position": question.position,  # Include position
                    "entityFields": [{
                        "entityType": ef.entity_type,
                        "fieldId": ef.field_id,
                        "name": ef.name,
                        "displayName": ef.display_name,
                        "standard": ef.standard
                    } for ef in entity_fields] if entity_fields else None,  # Handle null entity_fields
                    "createdAt": question.created_at.isoformat() if question.created_at else None,
                    "updatedAt": question.updated_at.isoformat() if question.updated_at else None
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error updating question {question_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating question: {str(e)}")
        finally:
            if db:
                db.close()

    def add_user_question_to_position_6(self, chatbot_id: str, tenant_id: int, user_question: str, max_questions: int = 5) -> dict:
        """
        Add a user question to the position 6 field configuration

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID
            user_question: The question asked by the user
            max_questions: Maximum number of questions allowed (default: 5)

        Returns:
            dict: {"success": bool, "question_count": int, "limit_reached": bool}
        """
        db = None
        try:
            db = next(get_db())

            # Find the position 6 question for this chatbot
            position_6_question = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id,
                ChatbotQuestion.position == 6
            ).first()

            if not position_6_question:
                logger.warning(f"No position 6 question found for chatbot {chatbot_id}")
                return False

            # Parse existing user questions or initialize empty list
            import json
            try:
                existing_questions = json.loads(position_6_question.user_questions) if position_6_question.user_questions else []
            except (json.JSONDecodeError, TypeError):
                existing_questions = []

            # Check if adding this question would exceed the limit
            current_count = len(existing_questions)
            if current_count >= max_questions:
                logger.warning(f"User question limit ({max_questions}) already reached for chatbot {chatbot_id}")
                return {
                    "success": False,
                    "question_count": current_count,
                    "limit_reached": True
                }

            # Add the new user question with timestamp
            new_question_entry = {
                "question": user_question,
                "timestamp": datetime.utcnow().isoformat()
            }
            existing_questions.append(new_question_entry)
            new_count = len(existing_questions)

            # Update the user_questions field
            position_6_question.user_questions = json.dumps(existing_questions)

            db.commit()
            logger.info(f"Added user question to position 6 for chatbot {chatbot_id}: {user_question} (count: {new_count}/{max_questions})")

            return {
                "success": True,
                "question_count": new_count,
                "limit_reached": new_count >= max_questions
            }

        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error adding user question to position 6: {str(e)}")
            return {
                "success": False,
                "question_count": 0,
                "limit_reached": False
            }
        finally:
            if db:
                db.close()

    def get_user_questions_count(self, chatbot_id: str, tenant_id: int) -> int:
        """
        Get the count of user questions already asked for position 6

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            int: Number of user questions already asked
        """
        db = None
        try:
            db = next(get_db())

            # Find the position 6 question for this chatbot
            position_6_question = db.query(ChatbotQuestion).filter(
                ChatbotQuestion.chatbot_id == chatbot_id,
                ChatbotQuestion.tenant_id == tenant_id,
                ChatbotQuestion.position == 6
            ).first()

            if not position_6_question or not position_6_question.user_questions:
                return 0

            # Parse existing user questions
            import json
            try:
                existing_questions = json.loads(position_6_question.user_questions)
                return len(existing_questions)
            except (json.JSONDecodeError, TypeError):
                return 0

        except Exception as e:
            logger.error(f"Error getting user questions count: {str(e)}")
            return 0
        finally:
            if db:
                db.close()

    def can_ask_more_questions(self, chatbot_id: str, tenant_id: int, max_questions: int = 5) -> bool:
        """
        Check if user can ask more questions (hasn't reached the limit)

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID
            max_questions: Maximum number of questions allowed (default: 5)

        Returns:
            bool: True if user can ask more questions, False if limit reached
        """
        current_count = self.get_user_questions_count(chatbot_id, tenant_id)
        return current_count < max_questions

    def get_conversation_questions_count(self, conversation_state: dict) -> int:
        """
        Get the count of user questions asked in this specific conversation

        Args:
            conversation_state: The conversation state dictionary

        Returns:
            int: Number of user questions asked in this conversation
        """
        user_questions = conversation_state.get("user_questions", [])
        return len(user_questions)

    def can_ask_more_questions_in_conversation(self, conversation_state: dict, max_questions: int = 5) -> bool:
        """
        Check if user can ask more questions in this specific conversation

        Args:
            conversation_state: The conversation state dictionary
            max_questions: Maximum number of questions allowed (default: 5)

        Returns:
            bool: True if user can ask more questions, False if limit reached
        """
        current_count = self.get_conversation_questions_count(conversation_state)
        return current_count < max_questions

    def add_question_to_conversation(self, conversation_state: dict, user_question: str, max_questions: int = 5) -> dict:
        """
        Add a user question to the conversation state and check limits

        Args:
            conversation_state: The conversation state dictionary
            user_question: The question asked by the user
            max_questions: Maximum number of questions allowed (default: 5)

        Returns:
            dict: {"success": bool, "question_count": int, "limit_reached": bool}
        """
        try:
            # Get existing questions or initialize empty list
            user_questions = conversation_state.get("user_questions", [])

            # Check if adding this question would exceed the limit
            current_count = len(user_questions)
            if current_count >= max_questions:
                logger.warning(f"User question limit ({max_questions}) already reached for conversation")
                return {
                    "success": False,
                    "question_count": current_count,
                    "limit_reached": True
                }

            # Add the new user question with timestamp
            new_question_entry = {
                "question": user_question,
                "timestamp": datetime.utcnow().isoformat()
            }
            user_questions.append(new_question_entry)
            new_count = len(user_questions)

            # Update the conversation state
            conversation_state["user_questions"] = user_questions

            logger.info(f"Added user question to conversation: {user_question} (count: {new_count}/{max_questions})")

            return {
                "success": True,
                "question_count": new_count,
                "limit_reached": new_count >= max_questions
            }

        except Exception as e:
            logger.error(f"Error adding user question to conversation: {str(e)}")
            return {
                "success": False,
                "question_count": 0,
                "limit_reached": False
            }

    def get_conversation_questions_for_entity_update(self, conversation_state: dict) -> str:
        """
        Get user questions from conversation state formatted for entity updates

        Args:
            conversation_state: The conversation state dictionary

        Returns:
            str: Formatted string of numbered user questions for entity field
        """
        try:
            user_questions = conversation_state.get("user_questions", [])
            if not user_questions:
                return None

            # Format questions as numbered text instead of JSON
            formatted_questions = []
            question_number = 1
            for question_data in user_questions:
                question_text = question_data.get("question", "")
                if question_text:
                    formatted_questions.append(f"question{question_number}: {question_text}")
                    question_number += 1
            
            return "\n".join(formatted_questions)

        except Exception as e:
            logger.error(f"Error getting conversation questions for entity update: {str(e)}")
            return None

    def get_chatbot_knowledgebase(self, chatbot_id: str, tenant_id: int) -> Dict[str, Any]:
        """
        Get all knowledgebase documents for a chatbot

        Args:
            chatbot_id: The chatbot ID
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: List of knowledgebase documents
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Get knowledgebase associations
            kb_assocs = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            # Get document details
            documents = []
            for kb in kb_assocs:
                document = db.query(Document).filter(
                    Document.id == kb.document_id,
                    Document.tenant_id == tenant_id
                ).first()
                if document:
                    documents.append({
                        "id": document.id,
                        "document_name": document.document_name,
                        "document_type": document.document_type,
                        "created_at": document.created_at,
                        "created_by": document.created_by
                    })

            logger.info(f"Retrieved {len(documents)} knowledgebase documents for chatbot {chatbot_id}")

            return {
                "chatbot_id": chatbot_id,
                "documents": documents,
                "total_count": len(documents)
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting knowledgebase for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error getting knowledgebase: {str(e)}")
        finally:
            if db:
                db.close()

    async def upload_multiple_knowledgebase_files(
        self,
        chatbot_id: str,
        files: List[UploadFile],
        tenant_id: int,
        user_id: str,
        token: str
    ) -> Dict[str, Any]:
        """
        Upload multiple files to chatbot knowledgebase

        Args:
            chatbot_id: The chatbot ID
            files: List of UploadFile objects
            tenant_id: The tenant ID
            user_id: The user ID for audit trail
            token: Authorization token for user validation

        Returns:
            Dict[str, Any]: Upload results with success and failure details
        """
        db = None
        successful_uploads = []
        failed_uploads = []

        try:
            # Validate file count
            if len(files) > MAX_FILE_COUNT:
                raise HTTPException(
                    status_code=400,
                    detail=f"You can add up to {MAX_FILE_COUNT} files only 📂. Please remove one before uploading a new file."
                )

            # Validate file sizes
            for file in files:
                if file.size and file.size > MAX_FILE_SIZE:
                    raise HTTPException(
                        status_code=400,
                        detail=f"File '{file.filename}' exceeds maximum size of 10MB. File size: {file.size / (1024*1024):.2f}MB"
                    )

            db = next(get_db())

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Initialize services
            s3_service = S3Service()
            es_service = ElasticsearchService()

            # Process each file
            for file in files:
                file_result = {
                    "filename": file.filename,
                    "status": "processing"
                }

                try:
                    # Validate file type
                    if not file.filename.lower().endswith('.pdf'):
                        file_result.update({
                            "status": "failed",
                            "error": "Only PDF files are supported"
                        })
                        failed_uploads.append(file_result)
                        continue

                    # Read file content
                    file_data = await file.read()

                    if not file_data:
                        file_result.update({
                            "status": "failed",
                            "error": "File is empty"
                        })
                        failed_uploads.append(file_result)
                        continue

                    # Secondary file size check (in case file.size wasn't available)
                    if len(file_data) > MAX_FILE_SIZE:
                        file_result.update({
                            "status": "failed",
                            "error": f"File exceeds maximum size of 10MB. File size: {len(file_data) / (1024*1024):.2f}MB"
                        })
                        failed_uploads.append(file_result)
                        continue

                    # Extract text from PDF
                    pdf_content = await self._extract_pdf_text(file_data, file.filename)

                    # Generate unique document ID
                    document_id = str(uuid.uuid4())

                    # Upload file to S3
                    file_data_io = BytesIO(file_data)
                    s3_key = s3_service.upload_file(
                        file_data=file_data_io,
                        tenant_id=tenant_id,
                        chatbot_id=chatbot_id,
                        filename=file.filename
                    )

                    # Clean and index content in Elasticsearch using new master index strategy
                    cleaned_content = es_service.clean_text_for_embedding(pdf_content)
                    es_index = es_service.index_document(
                        tenant_id,
                        document_id,
                        cleaned_content,
                        chatbot_id,  # chatbot_id
                        300,  # chunk_size
                        30,   # overlap
                        db    # database session for master index strategy
                    )

                    # Store document metadata in PostgreSQL
                    document = Document(
                        id=document_id,
                        tenant_id=tenant_id,
                        document_name=file.filename,
                        document_type="pdf",
                        es_index=es_index,
                        es_document_id=document_id,
                        s3_key=s3_key,
                        created_by=user_id
                    )
                    db.add(document)

                    # Create association between chatbot and document with enhanced metadata
                    kb_assoc = ChatbotKnowledgebase(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        document_id=document_id,
                        document_name=file.filename,
                        document_size=len(file_data),  # Use actual file data size
                        document_type="pdf",  # We know it's PDF from validation above
                        tenant_id=tenant_id
                    )
                    db.add(kb_assoc)

                    # Update file result with success
                    file_result.update({
                        "status": "success",
                        "document_id": document_id,
                        "s3_key": s3_key,
                        "es_index": es_index
                    })
                    successful_uploads.append(file_result)

                    logger.info(f"Successfully processed file {file.filename} for chatbot {chatbot_id}")

                except Exception as e:
                    logger.error(f"Error processing file {file.filename}: {str(e)}")
                    file_result.update({
                        "status": "failed",
                        "error": str(e)
                    })
                    failed_uploads.append(file_result)
                    continue

            # Note: Chatbot status should only be changed via explicit activate/deactivate API calls
            # Removed automatic activation when knowledgebase files are uploaded

            # Commit all successful changes
            if successful_uploads:
                db.commit()
                logger.info(f"Committed {len(successful_uploads)} successful uploads for chatbot {chatbot_id}")

            return {
                "message": f"Processed {len(files)} files",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id,
                "total_files": len(files),
                "successful_uploads": len(successful_uploads),
                "failed_uploads": len(failed_uploads),
                "results": {
                    "successful": successful_uploads,
                    "failed": failed_uploads
                },
                "chatbot_status": chatbot.status
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error in multiple file upload for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing files: {str(e)}")
        finally:
            if db:
                db.close()

    async def upload_multiple_knowledgebase_files_enhanced(
        self,
        chatbot_id: str,
        files_with_metadata: List[Dict[str, Any]],
        tenant_id: int,
        user_id: str,
        token: str
    ) -> Dict[str, Any]:
        """
        Upload multiple files to chatbot knowledgebase with enhanced metadata support

        Args:
            chatbot_id: The chatbot ID
            files_with_metadata: List of dicts containing file and metadata
            tenant_id: The tenant ID
            user_id: The user ID for audit trail
            token: Authorization token for user validation

        Returns:
            Dict[str, Any]: Upload results with success and failure details
        """
        db = None
        successful_uploads = []
        failed_uploads = []

        try:
            # Validate file count
            if len(files_with_metadata) > MAX_FILE_COUNT:
                raise HTTPException(
                    status_code=400,
                    detail=f"You can add up to {MAX_FILE_COUNT} files only 📂. Please remove one before uploading a new file."
                )

            # Validate file sizes using provided metadata
            for file_data in files_with_metadata:
                document_size = file_data.get("document_size", 0)
                document_name = file_data.get("document_name", "unknown")

                if document_size > MAX_FILE_SIZE:
                    raise HTTPException(
                        status_code=400,
                        detail=f"File '{document_name}' exceeds maximum size of 10MB. File size: {document_size / (1024*1024):.2f}MB"
                    )

            db = next(get_db())

            # Validate user exists and get user info
            user = user_service.validate_and_get_user(user_id, token, db)

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Initialize services
            s3_service = S3Service()
            es_service = ElasticsearchService()

            # Implement "replace all" strategy - get current knowledgebase and determine changes
            current_associations = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            current_document_ids = {assoc.document_id for assoc in current_associations}

            # Get document IDs from the new set (only existing documents have IDs)
            new_document_ids = set()
            for file_data in files_with_metadata:
                if file_data.get("is_existing_document", False) and file_data.get("document_id"):
                    new_document_ids.add(file_data["document_id"])

            # Documents to remove (in current but not in new set)
            documents_to_remove = current_document_ids - new_document_ids

            logger.info(f"Knowledgebase replacement for chatbot {chatbot_id}: current={len(current_document_ids)}, new={len(new_document_ids)}, to_remove={len(documents_to_remove)}")
            logger.info(f"Current document IDs: {current_document_ids}")
            logger.info(f"New document IDs: {new_document_ids}")
            logger.info(f"Documents to remove: {documents_to_remove}")

            # Remove documents that are no longer needed
            removed_count = 0
            es_removal_results = []
            
            if documents_to_remove:
                # Use bulk removal for better performance
                bulk_removal_result = es_service.remove_multiple_documents_from_index(
                    tenant_id, list(documents_to_remove), chatbot_id, db
                )
                
                # Process each document for database cleanup
                for document_id in documents_to_remove:
                    try:
                        # Get document details for cleanup
                        document = db.query(Document).filter(
                            Document.id == document_id,
                            Document.tenant_id == tenant_id
                        ).first()
                        
                        # Check ES removal result
                        es_removal_success = any(
                            result["document_id"] == document_id 
                            for result in bulk_removal_result["removed"]
                        )
                        
                        es_removal_results.append({
                            "document_id": document_id,
                            "es_removal_success": es_removal_success
                        })
                        
                        if not es_removal_success:
                            logger.warning(f"Failed to remove document {document_id} from Elasticsearch, but continuing with database removal")

                        # Remove association from database
                        deleted_rows = db.query(ChatbotKnowledgebase).filter(
                            ChatbotKnowledgebase.chatbot_id == chatbot_id,
                            ChatbotKnowledgebase.document_id == document_id,
                            ChatbotKnowledgebase.tenant_id == tenant_id
                        ).delete()

                        if deleted_rows > 0:
                            removed_count += 1
                            logger.info(f"Successfully removed document {document_id} from chatbot {chatbot_id} (ES: {'success' if es_removal_success else 'failed'}, DB: {deleted_rows} rows)")
                            
                            # Check if this document is used by other chatbots
                            other_associations = db.query(ChatbotKnowledgebase).filter(
                                ChatbotKnowledgebase.document_id == document_id,
                                ChatbotKnowledgebase.tenant_id == tenant_id,
                                ChatbotKnowledgebase.chatbot_id != chatbot_id
                            ).count()
                            
                            # If no other chatbots use this document and it exists, remove it completely
                            if other_associations == 0 and document:
                                # Remove from S3 if S3 key exists
                                if document.s3_key:
                                    try:
                                        s3_service.delete_file(document.s3_key)
                                        logger.info(f"Removed document {document_id} from S3: {document.s3_key}")
                                    except Exception as e:
                                        logger.error(f"Failed to remove document {document_id} from S3: {str(e)}")
                                
                                # Remove from database
                                db.delete(document)
                                logger.info(f"Completely removed document {document_id} as it's not used by other chatbots")
                            elif other_associations > 0:
                                logger.info(f"Document {document_id} is still used by {other_associations} other chatbots, keeping document")
                        else:
                            logger.warning(f"No database rows found to delete for document {document_id} in chatbot {chatbot_id}")

                    except Exception as e:
                        logger.error(f"Error removing document {document_id}: {str(e)}")
                        es_removal_results.append({
                            "document_id": document_id,
                            "es_removal_success": False,
                            "error": str(e)
                        })
                        # Continue with other operations

            # Process each file with metadata
            for file_data in files_with_metadata:
                document_name = file_data["document_name"]
                document_size = file_data["document_size"]
                document_type = file_data["document_type"]
                file_index = file_data.get("index", "unknown")
                is_existing_document = file_data.get("is_existing_document", False)

                file_result = {
                    "filename": document_name,
                    "document_size": document_size,
                    "document_type": document_type,
                    "file_index": file_index,
                    "status": "processing"
                }

                try:
                    # Handle existing document scenario (edit mode)
                    if is_existing_document:
                        document_id = file_data.get("document_id")
                        if not document_id:
                            file_result.update({
                                "status": "failed",
                                "error": "Document ID is required for existing documents"
                            })
                            failed_uploads.append(file_result)
                            continue

                        # Verify the document exists and belongs to the tenant
                        existing_document = db.query(Document).filter(
                            Document.id == document_id,
                            Document.tenant_id == tenant_id
                        ).first()

                        if not existing_document:
                            file_result.update({
                                "status": "failed",
                                "error": f"Document with ID {document_id} not found"
                            })
                            failed_uploads.append(file_result)
                            continue

                        # Check if association already exists
                        existing_association = db.query(ChatbotKnowledgebase).filter(
                            ChatbotKnowledgebase.chatbot_id == chatbot_id,
                            ChatbotKnowledgebase.document_id == document_id,
                            ChatbotKnowledgebase.tenant_id == tenant_id
                        ).first()

                        if existing_association:
                            # Update existing association metadata
                            existing_association.document_name = document_name
                            existing_association.document_size = document_size
                            existing_association.document_type = document_type
                        else:
                            # Create new association for existing document
                            kb_assoc = ChatbotKnowledgebase(
                                id=str(uuid.uuid4()),
                                chatbot_id=chatbot_id,
                                document_id=document_id,
                                document_name=document_name,
                                document_size=document_size,
                                document_type=document_type,
                                tenant_id=tenant_id
                            )
                            db.add(kb_assoc)

                        file_result.update({
                            "status": "success",
                            "document_id": document_id,
                            "message": "Existing document associated successfully"
                        })
                        successful_uploads.append(file_result)
                        continue

                    # Handle new file upload scenario
                    file = file_data.get("file")
                    if not file:
                        file_result.update({
                            "status": "failed",
                            "error": "File data is required for new uploads"
                        })
                        failed_uploads.append(file_result)
                        continue

                    # Validate file type using metadata
                    if not document_type.lower() in ['application/pdf', 'pdf']:
                        if not document_name.lower().endswith('.pdf'):
                            file_result.update({
                                "status": "failed",
                                "error": "Only PDF files are supported"
                            })
                            failed_uploads.append(file_result)
                            continue

                    # Read file content
                    file_content = await file.read()

                    # Validate actual file size matches metadata
                    actual_size = len(file_content)
                    if abs(actual_size - document_size) > 1024:  # Allow 1KB tolerance
                        logger.warning(f"File size mismatch for {document_name}: metadata={document_size}, actual={actual_size}")

                    # Use metadata for document info
                    document_id = str(uuid.uuid4())

                    # Extract text from PDF
                    pdf_content = await self._extract_pdf_text(file_content, document_name)

                    # Upload to S3 using provided metadata
                    file_data_io = BytesIO(file_content)
                    s3_key = s3_service.upload_file(
                        file_data=file_data_io,
                        tenant_id=tenant_id,
                        chatbot_id=chatbot_id,
                        filename=document_name,
                        content_type=document_type
                    )

                    # Clean and index content in Elasticsearch using master index strategy
                    cleaned_content = es_service.clean_text_for_embedding(pdf_content)
                    es_index = es_service.index_document(
                        tenant_id,
                        document_id,
                        cleaned_content,
                        chatbot_id,  # chatbot_id
                        300,  # chunk_size
                        30,   # overlap
                        db    # database session for master index strategy
                    )

                    # Store document metadata in PostgreSQL
                    document = Document(
                        id=document_id,
                        tenant_id=tenant_id,
                        document_name=document_name,
                        document_type="pdf",
                        es_index=es_index,
                        es_document_id=document_id,
                        s3_key=s3_key,
                        created_by=user_id
                    )
                    db.add(document)

                    # Create association between chatbot and document with enhanced metadata
                    kb_assoc = ChatbotKnowledgebase(
                        id=str(uuid.uuid4()),
                        chatbot_id=chatbot_id,
                        document_id=document_id,
                        document_name=document_name,
                        document_size=actual_size,  # Use actual file data size
                        document_type="pdf",  # We know it's PDF from validation above
                        tenant_id=tenant_id
                    )
                    db.add(kb_assoc)

                    file_result.update({
                        "status": "success",
                        "document_id": document_id,
                        "s3_key": s3_key,
                        "es_index": es_index,
                        "actual_size": actual_size
                    })
                    successful_uploads.append(file_result)

                    logger.info(f"Successfully uploaded enhanced file: {document_name} (size: {document_size}, type: {document_type})")

                except Exception as e:
                    logger.error(f"Error processing enhanced file {document_name}: {str(e)}")
                    file_result.update({
                        "status": "failed",
                        "error": str(e)
                    })
                    failed_uploads.append(file_result)
                    continue

            # Update chatbot status if it was in DRAFT and has successful uploads
            # Note: Chatbot status should only be changed via explicit activate/deactivate API calls
            # Removed automatic activation when enhanced knowledgebase files are uploaded

            # Commit all changes (uploads and removals)
            if successful_uploads or removed_count > 0:
                db.commit()
                logger.info(f"Committed {len(successful_uploads)} enhanced uploads and {removed_count} removals for chatbot {chatbot_id}")
            elif len(files_with_metadata) == 0:
                # Empty payload case - still need to commit removals
                db.commit()
                logger.info(f"Committed {removed_count} document removals for chatbot {chatbot_id} (empty payload)")

            return {
                "message": f"Processed {len(files_with_metadata)} files with enhanced metadata",
                "chatbot_id": chatbot_id,
                "tenant_id": tenant_id,
                "total_files": len(files_with_metadata),
                "successful_uploads": len(successful_uploads),
                "failed_uploads": len(failed_uploads),
                "removed_documents": removed_count,
                "results": {
                    "successful": successful_uploads,
                    "failed": failed_uploads
                },
                "summary": {
                    "processed": len(files_with_metadata),
                    "successful": len(successful_uploads),
                    "failed": len(failed_uploads),
                    "removed": removed_count
                },
                "chatbot_status": chatbot.status,
                "enhanced_upload": True
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error in enhanced multiple file upload for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing enhanced files: {str(e)}")
        finally:
            if db:
                db.close()

    def remove_knowledgebase_document(self, chatbot_id: str, document_id: str, tenant_id: int) -> Dict[str, Any]:
        """
        Remove a specific document from chatbot knowledgebase

        Args:
            chatbot_id: The chatbot ID
            document_id: The document ID to remove
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Removal result
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Check if document exists and is associated with this chatbot
            kb_assoc = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.document_id == document_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).first()

            if not kb_assoc:
                raise HTTPException(status_code=404, detail="Document not found in chatbot knowledgebase")

            # Get document details for cleanup
            document = db.query(Document).filter(
                Document.id == document_id,
                Document.tenant_id == tenant_id
            ).first()

            if not document:
                raise HTTPException(status_code=404, detail="Document not found")

            # Remove the association
            db.delete(kb_assoc)

            # Check if this document is used by other chatbots
            other_associations = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.document_id == document_id,
                ChatbotKnowledgebase.tenant_id == tenant_id,
                ChatbotKnowledgebase.chatbot_id != chatbot_id
            ).count()

            # Initialize services for cleanup
            es_service = ElasticsearchService()
            s3_service = S3Service()
            
            # If no other chatbots use this document, remove it completely
            if other_associations == 0:
                # Remove from Elasticsearch first
                es_removal_success = es_service.remove_document_from_index(tenant_id, document_id, chatbot_id, db)
                if not es_removal_success:
                    logger.warning(f"Failed to remove document {document_id} from Elasticsearch, but continuing with database removal")
                
                # Remove from S3 if S3 key exists
                if document.s3_key:
                    try:
                        s3_service.delete_file(document.s3_key)
                        logger.info(f"Removed document {document_id} from S3: {document.s3_key}")
                    except Exception as e:
                        logger.error(f"Failed to remove document {document_id} from S3: {str(e)}")
                
                # Remove from database
                db.delete(document)
                logger.info(f"Removed document {document_id} completely as it's not used by other chatbots (ES: {'success' if es_removal_success else 'failed'})")
            else:
                # Only remove from Elasticsearch for this specific chatbot
                es_removal_success = es_service.remove_document_from_index(tenant_id, document_id, chatbot_id, db)
                if not es_removal_success:
                    logger.warning(f"Failed to remove document {document_id} from Elasticsearch for chatbot {chatbot_id}")
                else:
                    logger.info(f"Removed document {document_id} from Elasticsearch for chatbot {chatbot_id}")
                
                logger.info(f"Document {document_id} is still used by {other_associations} other chatbots")

            db.commit()

            logger.info(f"Removed document {document_id} from chatbot {chatbot_id} knowledgebase")

            return {
                "message": "Document removed from knowledgebase successfully",
                "chatbot_id": chatbot_id,
                "document_id": document_id,
                "document_name": document.document_name,
                "completely_removed": other_associations == 0
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error removing document {document_id} from chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error removing document: {str(e)}")
        finally:
            if db:
                db.close()

    async def _extract_pdf_text(self, file_data: bytes, filename: str) -> str:
        """
        Extract text content from PDF file data

        Args:
            file_data: PDF file bytes
            filename: Original filename for error reporting

        Returns:
            str: Extracted text content
        """
        try:
            pdf_reader = PyPDF2.PdfReader(BytesIO(file_data))
            pdf_content = ""

            # Extract text from each page
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                pdf_content += page.extract_text() + "\n\n"

            if not pdf_content.strip():
                raise ValueError("Could not extract text from PDF")

            return pdf_content

        except Exception as e:
            logger.error(f"Error extracting text from PDF {filename}: {str(e)}")
            raise ValueError(f"Error processing PDF {filename}: {str(e)}")

    def update_chatbot_knowledgebase(self, chatbot_id: str, knowledgebase_data: KnowledgebaseUpdateRequest, tenant_id: int) -> Dict[str, Any]:
        """
        Update chatbot knowledgebase with enhanced document handling and ES synchronization

        Args:
            chatbot_id: The chatbot ID
            knowledgebase_data: KnowledgebaseUpdateRequest with document details
            tenant_id: The tenant ID

        Returns:
            Dict[str, Any]: Update result with document details
        """
        db = None
        try:
            db = next(get_db())

            # Check if chatbot exists
            chatbot = db.query(Chatbot).filter(
                Chatbot.id == chatbot_id,
                Chatbot.tenant_id == tenant_id
            ).first()

            if not chatbot:
                raise HTTPException(status_code=404, detail="Chatbot not found")

            # Get current knowledgebase associations
            current_associations = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            current_document_ids = {assoc.document_id for assoc in current_associations}
            new_document_ids = {doc.documentId for doc in knowledgebase_data.documents if doc.documentId is not None}

            # Initialize Elasticsearch service for vector operations
            es_service = ElasticsearchService()

            # Documents to remove (in current but not in new)
            documents_to_remove = current_document_ids - new_document_ids

            # Documents to add (in new but not in current, or documentId is None meaning new document)
            documents_to_add = []
            documents_to_update = []

            for doc in knowledgebase_data.documents:
                if doc.documentId is None:
                    # New document to be added (will be handled during file upload)
                    documents_to_add.append(doc)
                elif doc.documentId not in current_document_ids:
                    # Existing document to be added to this chatbot
                    documents_to_add.append(doc)
                else:
                    # Existing document to be updated
                    documents_to_update.append(doc)

            # Remove documents from ES and database
            for document_id in documents_to_remove:
                try:
                    # Remove from Elasticsearch
                    es_service.remove_document_from_index(tenant_id, document_id, chatbot_id, db)

                    # Remove association from database
                    db.query(ChatbotKnowledgebase).filter(
                        ChatbotKnowledgebase.chatbot_id == chatbot_id,
                        ChatbotKnowledgebase.document_id == document_id,
                        ChatbotKnowledgebase.tenant_id == tenant_id
                    ).delete()

                    logger.info(f"Removed document {document_id} from chatbot {chatbot_id}")

                except Exception as e:
                    logger.error(f"Error removing document {document_id}: {str(e)}")
                    # Continue with other operations

            # Add new documents to database (ES indexing would be handled separately during file upload)
            for doc in documents_to_add:
                if doc.documentId:  # Existing document
                    # Create association
                    kb_assoc = ChatbotKnowledgebase(
                        chatbot_id=chatbot_id,
                        document_id=doc.documentId,
                        document_name=doc.documentName,
                        document_size=doc.documentSize,
                        document_type=doc.documentType,
                        tenant_id=tenant_id
                    )
                    db.add(kb_assoc)
                    logger.info(f"Added existing document {doc.documentId} to chatbot {chatbot_id}")

            # Update existing document metadata
            for doc in documents_to_update:
                association = db.query(ChatbotKnowledgebase).filter(
                    ChatbotKnowledgebase.chatbot_id == chatbot_id,
                    ChatbotKnowledgebase.document_id == doc.documentId,
                    ChatbotKnowledgebase.tenant_id == tenant_id
                ).first()

                if association:
                    # Update metadata
                    if doc.documentName is not None:
                        association.document_name = doc.documentName
                    if doc.documentSize is not None:
                        association.document_size = doc.documentSize
                    if doc.documentType is not None:
                        association.document_type = doc.documentType

                    logger.info(f"Updated metadata for document {doc.documentId} in chatbot {chatbot_id}")

            db.commit()

            # Get updated knowledgebase for response
            updated_associations = db.query(ChatbotKnowledgebase).filter(
                ChatbotKnowledgebase.chatbot_id == chatbot_id,
                ChatbotKnowledgebase.tenant_id == tenant_id
            ).all()

            knowledgebase_documents = [{
                "documentId": assoc.document_id,
                "documentName": assoc.document_name,
                "documentSize": assoc.document_size,
                "documentType": assoc.document_type
            } for assoc in updated_associations]

            logger.info(f"Updated knowledgebase for chatbot {chatbot_id}: {len(documents_to_remove)} removed, {len(documents_to_add)} added, {len(documents_to_update)} updated")

            return {
                "message": "Knowledgebase updated successfully",
                "chatbot_id": chatbot_id,
                "documents": knowledgebase_documents,
                "summary": {
                    "removed": len(documents_to_remove),
                    "added": len(documents_to_add),
                    "updated": len(documents_to_update)
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            if db:
                db.rollback()
            logger.error(f"Error updating knowledgebase for chatbot {chatbot_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating knowledgebase: {str(e)}")
        finally:
            if db:
                db.close()
    
    async def prepare_conversation_completion_payload(
        self,
        conversation_id: str,
        entity_details: List[Dict[str, Any]],
        conversation_state: Dict[str, Any],
        token: str,
        completion_message: str = None,
        charge: int = 0,
        tenant_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Prepare the new conversation completion payload format
        
        Args:
            conversation_id: The conversation ID
            entity_details: List of entity details from conversation
            conversation_state: The conversation state
            token: Authorization token
            completion_message: Final message to send
            charge: Charge amount
            tenant_id: Tenant ID for multi-tenancy support
            
        Returns:
            Dict[str, Any]: Formatted payload for conversation completion
        """
        try:
            from app.services.entity_field_service import EntityFieldService
            
            entity_field_service = EntityFieldService()
            
            # Prepare entity details with owner IDs
            formatted_entity_details = []
            for entity in entity_details:
                entity_id = entity.get("id") or entity.get("entityId")  # Support both formats
                entity_type = entity.get("entityType")
                
                if entity_id and entity_type:
                    # Fetch both owner ID and entity name in a single API call
                    entity_details = entity_field_service.get_entity_details(
                        str(entity_id), entity_type, token
                    )
                    
                    formatted_entity_details.append({
                        "entityId": int(entity_id),
                        "entityType": entity_type,
                        "ownerId": entity_details.get("ownerId"),
                        "entityName": entity_details.get("entityName")
                    })
            
            # Prepare node details from conversation state
            node_details = None
            if conversation_state.get("current_node"):
                current_node = conversation_state["current_node"]
                node_details = {
                    "id": current_node.get("id"),
                    "name": current_node.get("name"),
                    "type": current_node.get("type"),
                    "data": current_node.get("data", [])
                }
            
            # Determine chatbot type
            chatbot_type = "RULE"  # Default to RULE, can be enhanced based on chatbot configuration
            
            # Prepare the payload
            payload = {
                "chatbotConversationId": conversation_id,
                "message": completion_message,
                "completed": True,
                "charge": charge,
                "chatbotType": chatbot_type,
                "entityDetails": formatted_entity_details
            }
            
            # Add tenant ID if provided
            if tenant_id is not None:
                payload["tenantId"] = tenant_id
            
            if node_details:
                payload["nodeDetails"] = node_details
            
            logger.info(f"Prepared conversation completion payload: {payload}")
            return payload
            
        except Exception as e:
            logger.error(f"Error preparing conversation completion payload: {str(e)}")
            # Return basic payload if preparation fails
            basic_payload = {
                "chatbotConversationId": conversation_id,
                "message": completion_message,
                "completed": True,
                "charge": charge,
                "chatbotType": "RULE",
                "entityDetails": []
            }
            
            # Add tenant ID if provided
            if tenant_id is not None:
                basic_payload["tenantId"] = tenant_id
                
            return basic_payload
