"""
Permission validation utilities for chatbot operations
"""

import logging
from typing import List, Dict, Any
from fastapi import HTTPException

logger = logging.getLogger(__name__)


class PermissionValidator:
    """Utility class for validating user permissions"""
    
    @staticmethod
    def validate_whatsapp_business_permission(permissions: List[Dict[str, Any]], required_action: str = "updateAll") -> bool:
        """
        Validate if user has whatsappBusiness permission with required action
        
        Args:
            permissions: List of permission objects from JWT token
            required_action: The required action (default: "updateAll")
            
        Returns:
            bool: True if user has the required permission, False otherwise
        """
        try:
            logger.info(f"=== VALIDATING WHATSAPP BUSINESS PERMISSION ===")
            logger.info(f"Required action: {required_action}")
            logger.info(f"Permissions input: {permissions}")
            
            if not permissions or not isinstance(permissions, list):
                logger.warning("No permissions found or permissions is not a list")
                return False
            
            logger.info(f"Processing {len(permissions)} permissions...")
            
            for i, permission in enumerate(permissions):
                logger.info(f"Permission {i}: {permission}")

                # Handle both dictionary and Pydantic model formats for permission
                permission_name = None
                action = None

                if isinstance(permission, dict):
                    # Dictionary format
                    permission_name = permission.get("name")
                    action = permission.get("action")
                    logger.info(f"Dictionary permission {i} name: {permission_name}")
                elif hasattr(permission, 'name'):
                    # Pydantic model format (TokenPermission)
                    permission_name = getattr(permission, 'name', None)
                    action = getattr(permission, 'action', None)
                    logger.info(f"Pydantic permission {i} name: {permission_name}")
                else:
                    logger.warning(f"Permission {i} has unknown format, skipping")
                    continue

                logger.info(f"Permission {i} name: {permission_name}")

                # Check if this is whatsappBusiness permission
                if permission_name == "whatsappBusiness":
                    logger.info(f"Found whatsappBusiness permission!")

                    logger.info(f"Action object: {action}")
                    logger.info(f"Action type: {type(action)}")

                    if action is None:
                        logger.warning(f"whatsappBusiness permission has no action object")
                        return False

                    # Handle both dictionary and Pydantic model formats for action
                    action_value = None

                    if isinstance(action, dict):
                        # Dictionary format (direct from JSON)
                        action_value = action.get(required_action)
                        logger.info(f"Dictionary action - '{required_action}' value: {action_value}")
                    elif hasattr(action, required_action):
                        # Pydantic model format (parsed by TokenAction)
                        action_value = getattr(action, required_action)
                        logger.info(f"Pydantic action - '{required_action}' value: {action_value}")
                    else:
                        logger.warning(f"Action object does not have '{required_action}' attribute")
                        logger.warning(f"Available attributes: {dir(action) if hasattr(action, '__dict__') else 'N/A'}")
                        return False

                    logger.info(f"Final action '{required_action}' value: {action_value} (type: {type(action_value)})")

                    if action_value is True:
                        logger.info(f"SUCCESS: User has whatsappBusiness permission with action.{required_action}=true")
                        return True
                    else:
                        logger.warning(f"FAILURE: User has whatsappBusiness permission but action.{required_action}={action_value}")
                        return False
            
            logger.warning("User does not have whatsappBusiness permission")
            return False
            
        except Exception as e:
            logger.error(f"Error validating whatsappBusiness permission: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    @staticmethod
    def require_whatsapp_business_permission(permissions: List[Dict[str, Any]], required_action: str = "updateAll") -> None:
        """
        Require whatsappBusiness permission with specified action, raise HTTPException if not found
        
        Args:
            permissions: List of permission objects from JWT token
            required_action: The required action (default: "updateAll")
            
        Raises:
            HTTPException: 403 if user doesn't have required permissions
        """
        if not PermissionValidator.validate_whatsapp_business_permission(permissions, required_action):
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. You need whatsappBusiness permission with {required_action}=true to perform this action."
            )
    
    @staticmethod
    def get_user_permissions_summary(permissions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Get a summary of user permissions for debugging
        
        Args:
            permissions: List of permission objects from JWT token
            
        Returns:
            Dict with permission summary
        """
        try:
            summary = {
                "total_permissions": len(permissions) if permissions else 0,
                "permission_names": [],
                "whatsapp_business": None
            }
            
            if not permissions:
                return summary
            
            for permission in permissions:
                # Handle both dictionary and Pydantic model formats
                name = None
                action = None

                if isinstance(permission, dict):
                    name = permission.get("name")
                    action = permission.get("action")
                elif hasattr(permission, 'name'):
                    name = getattr(permission, 'name', None)
                    action = getattr(permission, 'action', None)

                if name:
                    summary["permission_names"].append(name)

                # Special handling for whatsappBusiness permission
                if name == "whatsappBusiness":
                    if isinstance(action, dict):
                        # Dictionary format
                        summary["whatsapp_business"] = {
                            "updateAll": action.get("updateAll", False),
                            "readAll": action.get("readAll", False),
                            "deleteAll": action.get("deleteAll", False),
                            "read": action.get("read", False),
                            "write": action.get("write", False),
                            "update": action.get("update", False),
                            "delete": action.get("delete", False)
                        }
                    elif hasattr(action, 'updateAll'):
                        # Pydantic model format
                        summary["whatsapp_business"] = {
                            "updateAll": getattr(action, "updateAll", False),
                            "readAll": getattr(action, "readAll", False),
                            "deleteAll": getattr(action, "deleteAll", False),
                            "read": getattr(action, "read", False),
                            "write": getattr(action, "write", False),
                            "update": getattr(action, "update", False),
                            "delete": getattr(action, "delete", False)
                        }
                    else:
                        summary["whatsapp_business"] = {"error": f"Invalid action structure: {type(action)}"}
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting permissions summary: {str(e)}")
            return {"error": str(e)}


def validate_chatbot_permissions(auth_context) -> None:
    """
    Validate that user has permission to create/update chatbots
    
    Args:
        auth_context: Authentication context from JWT token
        
    Raises:
        HTTPException: 403 if user doesn't have required permissions
    """
    try:
        permissions = getattr(auth_context, 'permissions', [])

        # Enhanced debugging
        logger.info(f"=== CHATBOT PERMISSION VALIDATION DEBUG ===")
        logger.info(f"Auth context type: {type(auth_context)}")
        logger.info(f"Permissions type: {type(permissions)}")
        logger.info(f"Permissions length: {len(permissions) if permissions else 0}")
        logger.info(f"Raw permissions: {permissions}")

        # Log permission summary for debugging
        summary = PermissionValidator.get_user_permissions_summary(permissions)
        logger.info(f"User permissions summary: {summary}")

        # Check if we have any permissions at all
        if not permissions:
            logger.error("No permissions found in auth context")
            raise HTTPException(
                status_code=403,
                detail="No permissions found in authentication context"
            )

        # Require whatsappBusiness permission with updateAll=true
        PermissionValidator.require_whatsapp_business_permission(permissions, "updateAll")

        logger.info("Chatbot permission validation successful")
        logger.info("=== END PERMISSION VALIDATION DEBUG ===")

    except HTTPException:
        # Re-raise HTTP exceptions (permission denied)
        raise
    except Exception as e:
        logger.error(f"Error in chatbot permission validation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error validating permissions"
        )
