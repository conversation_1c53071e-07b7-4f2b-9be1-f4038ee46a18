"""
Response formatting utilities for converting snake_case to camelCase
"""

from typing import Any, Dict, List, Union
import re


def to_camel_case(snake_str: str) -> str:
    """Convert snake_case to camelCase"""
    if not snake_str:
        return snake_str
    
    components = snake_str.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])


def convert_keys_to_camel_case(obj: Any) -> Any:
    """
    Recursively convert all dictionary keys from snake_case to camelCase
    
    Args:
        obj: The object to convert (dict, list, or primitive)
        
    Returns:
        The object with camelCase keys
    """
    if isinstance(obj, dict):
        return {
            to_camel_case(key): convert_keys_to_camel_case(value)
            for key, value in obj.items()
        }
    elif isinstance(obj, list):
        return [convert_keys_to_camel_case(item) for item in obj]
    else:
        return obj


def format_response(data: Any) -> Any:
    """
    Format response data to use camelCase keys
    
    Args:
        data: The response data to format
        
    Returns:
        The formatted response with camelCase keys
    """
    return convert_keys_to_camel_case(data)


def format_question_response(question_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format a single question response with camelCase keys
    
    Args:
        question_data: The question data to format
        
    Returns:
        The formatted question with camelCase keys
    """
    return format_response(question_data)


def format_questions_response(questions_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Format a list of questions response with camelCase keys
    
    Args:
        questions_data: The questions data to format
        
    Returns:
        The formatted questions with camelCase keys
    """
    return format_response(questions_data)


def format_chatbot_response(chatbot_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format a chatbot response with camelCase keys
    
    Args:
        chatbot_data: The chatbot data to format
        
    Returns:
        The formatted chatbot with camelCase keys
    """
    return format_response(chatbot_data)


def format_entity_field_response(entity_field_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format an entity field response with camelCase keys
    
    Args:
        entity_field_data: The entity field data to format
        
    Returns:
        The formatted entity field with camelCase keys
    """
    return format_response(entity_field_data)


# Example usage and test cases
if __name__ == "__main__":
    # Test the conversion functions
    test_data = {
        "question_id": "123",
        "entity_fields": [
            {
                "entity_type": "lead",
                "field_id": 456,
                "display_name": "First Name",
                "created_at": "2025-07-31T12:00:00"
            }
        ],
        "created_at": "2025-07-31T12:00:00",
        "updated_at": "2025-07-31T12:05:00"
    }
    
    formatted = format_response(test_data)
    print("Original:", test_data)
    print("Formatted:", formatted)
    
    # Expected output:
    # {
    #     "questionId": "123",
    #     "entityFields": [
    #         {
    #             "entityType": "lead",
    #             "fieldId": 456,
    #             "displayName": "First Name",
    #             "createdAt": "2025-07-31T12:00:00"
    #         }
    #     ],
    #     "createdAt": "2025-07-31T12:00:00",
    #     "updatedAt": "2025-07-31T12:05:00"
    # }
