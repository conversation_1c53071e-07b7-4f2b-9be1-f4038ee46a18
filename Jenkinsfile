podTemplate(
  containers: [
    containerTemplate(
      name: 'python',
      alwaysPullImage: true,
      image: 'python:3.9',
      args: 'cat',
      ttyEnabled: true,
      resourceLimitMemory: '4000Mi'
    ),
    containerTemplate(
      name: 'docker',
      image: 'docker:20.10-dind',
      command: 'dockerd-entrypoint.sh',
      args: '--host=tcp://0.0.0.0:2375 --host=unix:///var/run/docker.sock',
      ttyEnabled: true,
      privileged: true,
      resourceLimitMemory: '4000Mi'
    )
  ],
  volumes: [
    emptyDirVolume(mountPath: '/var/lib/docker', memory: false) // storage for DinD
  ],
  imagePullSecrets: ['registry-credentials']
) {
  def webhookURL = "https://outlook.office.com/webhook/14bd0fd8-1bd7-43dd-bdf5-2f0e89c4ec50@46ecb5f0-1227-4755-a101-64d39a05e1c7/JenkinsCI/752745c8a19a45caae6f2bc8a2a9df07/ad82656b-8630-4724-81d2-b76ce5380247"
  node(POD_LABEL) {
    try {
      def gitBranch
      def gitCommit
      def targetBranch
      def dockerRegistry = 'nexus.sling-dev.com:8123'
      def message = "Starting Build (${env.BUILD_ID}) for '${env.JOB_NAME} (${env.BRANCH_NAME})'"
      def status = "Starting"
      def color = "a0a0a0"

      container('python') {
        stage('Git Checkout') {
          def gitRepo = checkout scm
          gitCommit = gitRepo.GIT_COMMIT
          gitBranch = gitRepo.GIT_BRANCH
          targetBranch = env.CHANGE_TARGET

          sh "echo $gitCommit"
          sh "echo $gitBranch"
          sh "echo $targetBranch"
        }

        stage('Install Dependencies') {
          sh '''
            python -m venv venv
            . venv/bin/activate
            pip install --upgrade pip
            pip install -r requirements.txt
          '''
        }

                stage('Run Tests') {
          sh '''
            . venv/bin/activate

            # Install pytest and required plugins
            pip install pytest pytest-asyncio pytest-timeout pytest-mock

            # Set environment variables to disable external connections
            export REDIS_URL="redis://fake-redis:6379/0"
            export ELASTICSEARCH_URL="http://fake-es:9200"
            export SKIP_REDIS_CONNECTION="true"
            export SKIP_EXTERNAL_CONNECTIONS="true"

            # Run comprehensive test suite (skip tests requiring external services)
            echo "🧪 Running CI-safe test suite..."
            
            # Run minimal CI-safe tests that don't require external dependencies
            echo "Running essential service tests..."
            pytest test/services/test_openai_service.py \
              test/services/test_user_service.py \
              --tb=short --disable-warnings || exit 1
            
            echo "Running authentication integration tests..."
            pytest test/integration/test_authentication.py \
              --tb=short --disable-warnings || exit 1
            
            echo "Running basic API integration tests..."  
            pytest test/integration/test_api_integration.py \
              --tb=short --disable-warnings || exit 1
            
            echo "✅ CI-safe tests completed successfully!"
          '''
        }
      }

      container('docker') {
        def imageName = "${dockerRegistry}/sling/sd-whatsapp-chatbot"
        def alembicImageName = "${dockerRegistry}/sling/sd-whatsapp-chatbot/db-migration"
        def gitTag = "${imageName}:${gitCommit}"
        def alembicGitTag = "${alembicImageName}:${gitCommit}"
        def pullRequestTag = "${imageName}:${gitBranch}"
        def alembicPullRequestTag = "${alembicImageName}:${gitBranch}"
        def latestTag = "${imageName}:latest"
        def alembicLatestTag = "${alembicImageName}:latest"

        if (gitBranch.startsWith('PR-') && targetBranch == 'main') {
          pullRequestTag = "${imageName}:RC-${env.BUILD_ID}"
          alembicPullRequestTag = "${alembicImageName}:RC-${env.BUILD_ID}"
        }

        withCredentials([[$class: 'UsernamePasswordMultiBinding',
                          credentialsId: 'docker-sling-registry',
                          usernameVariable: 'DOCKER_USER',
                          passwordVariable: 'DOCKER_PASSWORD']]) {
          stage('Prepare Alembic migrations') {
            sh script: "docker login ${dockerRegistry} -u ${DOCKER_USER} -p ${DOCKER_PASSWORD}", label: "Docker Login"
            if (gitBranch.startsWith('PR-')) {
              sh script: "docker build -f ./alembic-Dockerfile -t ${alembicPullRequestTag} .", label: "Build Alembic migrations image with Pull Request tag"
              sh script: "docker push ${alembicPullRequestTag}", label: "Push to registry"
            }
            if (gitBranch == 'dev') {
              sh script: "docker build -f ./alembic-Dockerfile -t ${alembicGitTag} .", label: "Build alembic migrations image with git commit hash"
              sh script: "docker push ${alembicGitTag}", label: "Push to registry"
            }
            if (gitBranch == 'main') {
              def alembicReleaseCandidateTag = "${alembicImageName}:Release-${env.BUILD_ID}"
              sh script: "docker build -f ./alembic-Dockerfile -t ${alembicGitTag} .", label: "Build alembic migrations image with git commit hash"
              sh script: "docker tag ${alembicGitTag} ${alembicReleaseCandidateTag}", label: "Tag image as release candidate"
              sh script: "docker tag ${alembicGitTag} ${alembicLatestTag}", label: "Tag image as latest candidate"

              sh script: "docker push ${alembicGitTag}", label: "Push to registry"
              sh script: "docker push ${alembicReleaseCandidateTag}", label: "Push release candidate image to registry"
              sh script: "docker push ${alembicLatestTag}", label: "Push latest image to registry"
            }
          }
          stage('Publish Docker Image') {
            sh script: "docker login ${dockerRegistry} -u ${DOCKER_USER} -p ${DOCKER_PASSWORD}", label: "Docker Login"
            if (gitBranch.startsWith('PR-')) {
              sh script: "docker build -t ${pullRequestTag} .", label: "Build image with Pull Request tag"
              sh script: "docker push ${pullRequestTag}", label: "Push to registry"
            }
            if (gitBranch == 'dev') {
              sh script: "docker build -t ${gitTag} .", label: "Build image with git commit hash"
              sh script: "docker push ${gitTag}", label: "Push to registry"
            }
            if (gitBranch == 'main') {
              def releaseCandidateTag = "${imageName}:Release-${env.BUILD_ID}"
              sh script: "docker build -t ${gitTag} .", label: "Build image with git commit hash"
              sh script: "docker tag ${gitTag} ${releaseCandidateTag}", label: "Tag image as release candidate"
              sh script: "docker tag ${gitTag} ${latestTag}", label: "Tag image as latest candidate"

              sh script: "docker push ${gitTag}", label: "Push to registry"
              sh script: "docker push ${releaseCandidateTag}", label: "Push release candidate image to registry"
              sh script: "docker push ${latestTag}", label: "Push latest image to registry"
            }
          }
        }
      }

      def userInput = false
      if (gitBranch.startsWith('PR-')) {
        try {
            if (targetBranch == 'main') {
              stage('Approval for Deployment') {
                userInput = input(id: 'confirm', message: 'Do you wish to deploy the PR to STAGE environment?',
                  parameters: [[$class: 'BooleanParameterDefinition', defaultValue: false, description: 'This will deploy the current PR in STAGE environment', name: 'confirm']])
              }
              stage('Start Deployments') {
                build job: '../deploy-to-stage',
                  parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: "RC-${env.BUILD_ID}"],
                              [$class: 'StringParameterValue', name: 'branchName', value: gitBranch],
                              [$class: 'StringParameterValue', name: 'targetBranch', value: targetBranch]],
                  wait: false
              }
          } else {
            stage('Approval for Deployment') {
              userInput = input(id: 'confirm', message: 'Do you wish to deploy the PR to QA environment?',
                parameters: [[$class: 'BooleanParameterDefinition', defaultValue: false, description: 'This will deploy the current PR in QA environment', name: 'confirm']])
            }
            stage('Start Deployments') {
              build job: '../deploy-to-qa',
                parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: gitBranch],
                            [$class: 'StringParameterValue', name: 'branchName', value: gitBranch],
                            [$class: 'StringParameterValue', name: 'targetBranch', value: targetBranch]],
                wait: false
            }
          }
        } catch (err) {
          def user = err.getCauses()[0].getUser()
          userInput = false
          echo "Aborted by: [${user}]"
        }
      }

      if (gitBranch == 'dev') {
        stage('Start Deployments') {
          build job: '../deploy-to-qa',
            parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: gitCommit],
                         [$class: 'StringParameterValue', name: 'branchName', value: gitBranch],
                         [$class: 'StringParameterValue', name: 'targetBranch', value: "dev"]],
            wait: false
        }
      }
      if (gitBranch == 'main') {
        stage('Start Stage Deployment') {
          build job: '../deploy-to-stage',
              parameters: [[$class: 'StringParameterValue', name: 'dockerImageTag', value: "Release-${env.BUILD_ID}"],
                           [$class: 'StringParameterValue', name: 'triggeredByJob', value: "build-and-package-chatbot-master : #${BUILD_NUMBER}"],
                           [$class: 'StringParameterValue', name: 'branchName', value: "master"]],
              wait: false
        }
      }

      if (currentBuild.currentResult == "SUCCESS") {
        status = "SUCCESS"
        color = "0bcc2c"
        message = "Build (${env.BUILD_ID}) for '${env.JOB_NAME} (${env.BRANCH_NAME})' is Successful. Great job!"
      } else if (currentBuild.currentResult == "UNSTABLE") {
        status = "UNSTABLE"
        color = "d00000"
        message = "Build (${env.BUILD_ID}) for '${env.JOB_NAME} (${env.BRANCH_NAME})' is UNSTABLE!"
      } else if (currentBuild.currentResult == "FAILURE") {
        status = "FAILURE"
        color = "d00000"
        message = "Build (${env.BUILD_ID}) for '${env.JOB_NAME} (${env.BRANCH_NAME})' Failed!"
      }
    } finally {
      // Add any cleanup or final steps here if necessary
    }
  }
}